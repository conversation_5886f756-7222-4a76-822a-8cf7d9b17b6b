"""
Editor Toolbar for Arcanum GUI Builder - Controls mouse interaction modes.
"""

from PyQt5.QtWidgets import (
    QToolBar, QAction, QActionGroup, QButtonGroup,
    QToolButton, QMenu, QWidget, QHBoxLayout
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon, QPixmap, QPainter, QColor, QFont


class EditorToolbar(QToolBar):
    """
    Professional editor toolbar with mouse interaction modes and tools.
    """
    
    # Signals
    mode_changed = pyqtSignal(str)  # Emits: 'select', 'pan', 'zoom', 'draw'
    tool_activated = pyqtSignal(str)  # Emits tool name
    
    def __init__(self, parent=None):
        super().__init__("Editor Tools", parent)
        self.current_mode = 'select'
        
        self.setup_toolbar()
        
    def setup_toolbar(self):
        """Set up the toolbar with interaction modes and tools."""
        self.setMovable(False)
        self.setFloatable(False)
        self.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        
        # Mouse interaction modes
        self.create_interaction_modes()
        
        self.addSeparator()
        
        # Editing tools
        self.create_editing_tools()
        
        self.addSeparator()
        
        # View tools
        self.create_view_tools()
        
        self.addSeparator()
        
        # Alignment tools
        self.create_alignment_tools()
        
        # Style the toolbar
        self.setStyleSheet("""
            QToolBar {
                background-color: #f8f9fa;
                border-bottom: 1px solid #dee2e6;
                spacing: 4px;
                padding: 4px;
            }
            QToolButton {
                border: 1px solid transparent;
                border-radius: 4px;
                padding: 4px;
                margin: 1px;
                background-color: transparent;
            }
            QToolButton:hover {
                background-color: #e9ecef;
                border-color: #adb5bd;
            }
            QToolButton:pressed, QToolButton:checked {
                background-color: #007bff;
                color: white;
                border-color: #0056b3;
            }
        """)
        
    def create_interaction_modes(self):
        """Create mouse interaction mode buttons."""
        # Create action group for exclusive selection
        self.mode_group = QActionGroup(self)
        
        # Select mode (default)
        select_action = QAction("🔍\nSelect", self)
        select_action.setCheckable(True)
        select_action.setChecked(True)
        select_action.setToolTip("Select and move widgets (S)")
        select_action.setShortcut("S")
        select_action.triggered.connect(lambda: self.set_mode('select'))
        self.mode_group.addAction(select_action)
        self.addAction(select_action)
        
        # Pan mode
        pan_action = QAction("✋\nPan", self)
        pan_action.setCheckable(True)
        pan_action.setToolTip("Pan the canvas (H)")
        pan_action.setShortcut("H")
        pan_action.triggered.connect(lambda: self.set_mode('pan'))
        self.mode_group.addAction(pan_action)
        self.addAction(pan_action)
        
        # Zoom mode
        zoom_action = QAction("🔍\nZoom", self)
        zoom_action.setCheckable(True)
        zoom_action.setToolTip("Zoom in/out (Z)")
        zoom_action.setShortcut("Z")
        zoom_action.triggered.connect(lambda: self.set_mode('zoom'))
        self.mode_group.addAction(zoom_action)
        self.addAction(zoom_action)
        
    def create_editing_tools(self):
        """Create editing tool buttons."""
        # Copy tool
        copy_action = QAction("📋\nCopy", self)
        copy_action.setToolTip("Copy selected widgets (Ctrl+C)")
        copy_action.setShortcut("Ctrl+C")
        copy_action.triggered.connect(lambda: self.tool_activated.emit('copy'))
        self.addAction(copy_action)
        
        # Paste tool
        paste_action = QAction("📄\nPaste", self)
        paste_action.setToolTip("Paste widgets (Ctrl+V)")
        paste_action.setShortcut("Ctrl+V")
        paste_action.triggered.connect(lambda: self.tool_activated.emit('paste'))
        self.addAction(paste_action)
        
        # Delete tool
        delete_action = QAction("🗑️\nDelete", self)
        delete_action.setToolTip("Delete selected widgets (Delete)")
        delete_action.setShortcut("Delete")
        delete_action.triggered.connect(lambda: self.tool_activated.emit('delete'))
        self.addAction(delete_action)
        
    def create_view_tools(self):
        """Create view control tools."""
        # Grid toggle
        grid_action = QAction("⊞\nGrid", self)
        grid_action.setCheckable(True)
        grid_action.setChecked(True)
        grid_action.setToolTip("Toggle grid display (G)")
        grid_action.setShortcut("G")
        grid_action.triggered.connect(lambda checked: self.tool_activated.emit('grid_toggle'))
        self.addAction(grid_action)
        
        # Snap toggle
        snap_action = QAction("🧲\nSnap", self)
        snap_action.setCheckable(True)
        snap_action.setChecked(True)
        snap_action.setToolTip("Toggle snap to grid (Shift+G)")
        snap_action.setShortcut("Shift+G")
        snap_action.triggered.connect(lambda checked: self.tool_activated.emit('snap_toggle'))
        self.addAction(snap_action)
        
        # Zoom fit
        fit_action = QAction("🔍\nFit", self)
        fit_action.setToolTip("Zoom to fit all widgets (Ctrl+0)")
        fit_action.setShortcut("Ctrl+0")
        fit_action.triggered.connect(lambda: self.tool_activated.emit('zoom_fit'))
        self.addAction(fit_action)
        
    def create_alignment_tools(self):
        """Create alignment tool buttons."""
        # Align left
        align_left_action = QAction("⫷\nLeft", self)
        align_left_action.setToolTip("Align selected widgets to left")
        align_left_action.triggered.connect(lambda: self.tool_activated.emit('align_left'))
        self.addAction(align_left_action)
        
        # Align center
        align_center_action = QAction("⫸\nCenter", self)
        align_center_action.setToolTip("Align selected widgets to center")
        align_center_action.triggered.connect(lambda: self.tool_activated.emit('align_center'))
        self.addAction(align_center_action)
        
        # Align right
        align_right_action = QAction("⫸\nRight", self)
        align_right_action.setToolTip("Align selected widgets to right")
        align_right_action.triggered.connect(lambda: self.tool_activated.emit('align_right'))
        self.addAction(align_right_action)
        
        # Distribute horizontally
        distribute_h_action = QAction("⟷\nSpread", self)
        distribute_h_action.setToolTip("Distribute widgets horizontally")
        distribute_h_action.triggered.connect(lambda: self.tool_activated.emit('distribute_horizontal'))
        self.addAction(distribute_h_action)
        
    def set_mode(self, mode: str):
        """Set the current interaction mode."""
        if mode != self.current_mode:
            self.current_mode = mode
            self.mode_changed.emit(mode)
            
    def get_current_mode(self) -> str:
        """Get the current interaction mode."""
        return self.current_mode
        
    def enable_tool(self, tool_name: str, enabled: bool = True):
        """Enable or disable a specific tool."""
        for action in self.actions():
            if action.text().split('\n')[-1].lower() == tool_name.lower():
                action.setEnabled(enabled)
                break
                
    def set_tool_checked(self, tool_name: str, checked: bool = True):
        """Set a tool's checked state."""
        for action in self.actions():
            if action.text().split('\n')[-1].lower() == tool_name.lower():
                if action.isCheckable():
                    action.setChecked(checked)
                break

"""
Professional widget icon system for Arcanum GUI Builder.
"""

from typing import Dict, Optional
from PyQt5.QtGui import QIcon, QPixmap, QPainter, QColor, QFont, QFontMetrics
from PyQt5.QtCore import Qt, QSize


class WidgetIconProvider:
    """Provides professional icons for widgets in the palette."""
    
    # Unicode icons for different widget types
    WIDGET_ICONS = {
        # Input widgets
        'input': '📝',
        'text_input': '📝',
        'textarea': '📄',
        'password': '🔒',
        'number': '🔢',
        'email': '📧',
        'url': '🌐',
        'search': '🔍',
        'date': '📅',
        'time': '⏰',
        'datetime': '📅',
        'file': '📁',
        'range': '🎚️',
        'color': '🎨',
        
        # Selection widgets
        'checkbox': '☑️',
        'radio': '🔘',
        'select': '📋',
        'dropdown': '📋',
        'combobox': '📋',
        'listbox': '📜',
        'multiselect': '📋',
        
        # Action widgets
        'button': '🔲',
        'submit': '✅',
        'reset': '🔄',
        'link': '🔗',
        'image_button': '🖼️',
        
        # Display widgets
        'label': '🏷️',
        'text': '📝',
        'heading': '📰',
        'paragraph': '📄',
        'image': '🖼️',
        'icon': '⭐',
        'divider': '➖',
        'spacer': '⬜',
        'progress': '📊',
        'meter': '📏',
        'badge': '🏷️',
        'alert': '⚠️',
        'notification': '🔔',
        
        # Layout widgets
        'container': '📦',
        'panel': '🗂️',
        'card': '🃏',
        'section': '📑',
        'group': '👥',
        'tabs': '📑',
        'accordion': '📁',
        'modal': '🪟',
        'sidebar': '📋',
        'navbar': '🧭',
        'footer': '⬇️',
        'header': '⬆️',
        
        # Data widgets
        'table': '📊',
        'list': '📝',
        'grid': '⚏',
        'chart': '📈',
        'graph': '📊',
        'calendar': '📅',
        'timeline': '⏱️',
        
        # Media widgets
        'video': '🎥',
        'audio': '🎵',
        'iframe': '🖼️',
        'embed': '🔗',
        'map': '🗺️',
        
        # Form widgets
        'form': '📋',
        'fieldset': '📦',
        'legend': '🏷️',
        
        # Navigation widgets
        'menu': '☰',
        'breadcrumb': '🍞',
        'pagination': '📄',
        'stepper': '👣',
        
        # Feedback widgets
        'tooltip': '💬',
        'popover': '💭',
        'toast': '🍞',
        'loading': '⏳',
        'spinner': '🌀',
        
        # Layout containers
        'row': '↔️',
        'column': '↕️',
        'flex': '🔄',
        'grid_container': '⚏',
        'stack': '📚',
    }
    
    # Category colors for visual grouping
    CATEGORY_COLORS = {
        'input': '#e3f2fd',      # Light blue
        'action': '#f3e5f5',     # Light purple
        'display': '#e8f5e8',    # Light green
        'layout': '#fff3e0',     # Light orange
        'data': '#fce4ec',       # Light pink
        'media': '#f1f8e9',      # Light lime
        'navigation': '#e0f2f1',  # Light teal
        'feedback': '#fff8e1',   # Light yellow
        'general': '#f5f5f5',    # Light gray
    }
    
    @classmethod
    def get_widget_icon(cls, widget_type: str) -> str:
        """Get the Unicode icon for a widget type."""
        if widget_type is None:
            return '❓'
        if not isinstance(widget_type, str):
            return '❓'
        if widget_type == '':
            return '❓'
        return cls.WIDGET_ICONS.get(widget_type.lower(), '❓')
    
    @classmethod
    def get_category_color(cls, category: str) -> str:
        """Get the color for a widget category."""
        if category is None:
            return '#f5f5f5'
        if not isinstance(category, str):
            return '#f5f5f5'
        if category == '':
            return '#f5f5f5'
        return cls.CATEGORY_COLORS.get(category.lower(), '#f5f5f5')
    
    @classmethod
    def create_widget_icon_pixmap(cls, widget_type: str, size: QSize = QSize(32, 32)) -> QPixmap:
        """Create a professional icon pixmap for a widget type."""
        pixmap = QPixmap(size)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Get icon text
        icon_text = cls.get_widget_icon(widget_type)
        
        # Set up font
        font = QFont()
        font.setPointSize(max(8, size.width() // 3))
        painter.setFont(font)
        
        # Draw icon
        painter.setPen(QColor('#333333'))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, icon_text)
        
        painter.end()
        return pixmap
    
    @classmethod
    def create_category_icon(cls, category: str, size: QSize = QSize(24, 24)) -> QPixmap:
        """Create an icon for a widget category."""
        pixmap = QPixmap(size)
        color = QColor(cls.get_category_color(category))
        pixmap.fill(color)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Draw border
        painter.setPen(QColor('#dee2e6'))
        painter.drawRect(pixmap.rect().adjusted(0, 0, -1, -1))
        
        # Category icons
        category_icons = {
            'input': '📝',
            'action': '⚡',
            'display': '👁️',
            'layout': '📦',
            'data': '📊',
            'media': '🎬',
            'navigation': '🧭',
            'feedback': '💬',
            'general': '⚙️',
        }
        
        icon_text = category_icons.get(category.lower(), '📦')
        
        # Set up font
        font = QFont()
        font.setPointSize(max(6, size.width() // 4))
        painter.setFont(font)
        
        # Draw icon
        painter.setPen(QColor('#333333'))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, icon_text)
        
        painter.end()
        return pixmap


class WidgetTemplateProvider:
    """Provides enhanced widget templates with better defaults."""
    
    @classmethod
    def get_widget_template(cls, widget_type: str) -> Dict:
        """Get an enhanced template for a widget type."""
        templates = {
            'button': {
                'widget': 'button',
                'label': 'Click Me',
                'action': 'button_clicked',
                'style': 'padding: 8px 16px; background-color: #007bff; color: white; border: none; border-radius: 4px;',
                'position': {'width': 120, 'height': 40}
            },
            'input': {
                'widget': 'input',
                'label': 'Text Input',
                'placeholder': 'Enter text...',
                'required': False,
                'style': 'padding: 8px; border: 1px solid #ccc; border-radius: 4px;',
                'position': {'width': 200, 'height': 35}
            },
            'label': {
                'widget': 'label',
                'text': 'Label Text',
                'style': 'font-weight: bold; color: #333;',
                'position': {'width': 100, 'height': 25}
            },
            'checkbox': {
                'widget': 'checkbox',
                'label': 'Check this option',
                'checked': False,
                'style': 'margin: 4px;',
                'position': {'width': 150, 'height': 25}
            },
            'select': {
                'widget': 'select',
                'label': 'Select Option',
                'options': ['Option 1', 'Option 2', 'Option 3'],
                'style': 'padding: 8px; border: 1px solid #ccc; border-radius: 4px;',
                'position': {'width': 180, 'height': 35}
            },
            'textarea': {
                'widget': 'textarea',
                'label': 'Text Area',
                'placeholder': 'Enter multiple lines...',
                'rows': 4,
                'style': 'padding: 8px; border: 1px solid #ccc; border-radius: 4px;',
                'position': {'width': 300, 'height': 100}
            },
            'image': {
                'widget': 'image',
                'src': 'https://via.placeholder.com/150x100',
                'alt': 'Placeholder Image',
                'style': 'border: 1px solid #ddd; border-radius: 4px;',
                'position': {'width': 150, 'height': 100}
            },
            'container': {
                'widget': 'container',
                'children': [],
                'style': 'padding: 16px; border: 1px solid #ddd; border-radius: 8px; background-color: #f8f9fa;',
                'position': {'width': 300, 'height': 200}
            }
        }
        
        # Get base template or create default
        if widget_type is None:
            widget_type = 'unknown'
        if not isinstance(widget_type, str):
            widget_type = 'unknown'

        template = templates.get(widget_type, {
            'widget': widget_type,
            'label': widget_type.title() if widget_type else 'Unknown',
            'position': {'width': 150, 'height': 35}
        })
        
        # Ensure position metadata exists
        if 'position' not in template:
            template['position'] = {'width': 150, 'height': 35}
            
        # Add default position values
        position = template['position']
        position.setdefault('x', 50)
        position.setdefault('y', 50)
        position.setdefault('snap_to_grid', True)
        position.setdefault('locked', False)
        position.setdefault('resizable', True)
        position.setdefault('z_index', 10)
        
        return template
    
    @classmethod
    def get_widget_categories(cls) -> Dict[str, list]:
        """Get organized widget categories."""
        return {
            'Input': ['input', 'textarea', 'password', 'number', 'email', 'date', 'file'],
            'Selection': ['checkbox', 'radio', 'select', 'dropdown'],
            'Action': ['button', 'submit', 'reset', 'link'],
            'Display': ['label', 'text', 'heading', 'image', 'divider'],
            'Layout': ['container', 'panel', 'card', 'section', 'row', 'column'],
            'Data': ['table', 'list', 'chart', 'progress'],
            'Navigation': ['menu', 'breadcrumb', 'tabs', 'pagination'],
            'Feedback': ['alert', 'tooltip', 'loading', 'notification']
        }

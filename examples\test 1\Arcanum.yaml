app_name: test 1
version: 1.0.0
description: lets see whsats goin on
start_layout: welcome_page
theme: default
autosave: true
logic_graph: true
embed_database: true
registry_path: pages/
ui_path: ui/
schemas_path: schemas/
plugins_path: plugins/
user_data_path: user_data/
export_targets:
- qt
- html
- json
database:
  type: sqlite
  path: user_data/app.db
  auto_migrate: true
themes:
  default:
    primary_color: '#3b82f6'
    secondary_color: '#64748b'
    background_color: '#ffffff'
    text_color: '#1e293b'
  dark:
    primary_color: '#60a5fa'
    secondary_color: '#94a3b8'
    background_color: '#0f172a'
    text_color: '#f1f5f9'
  fantasy:
    primary_color: '#8b5cf6'
    secondary_color: '#a78bfa'
    background_color: '#1e1b4b'
    text_color: '#e0e7ff'
  professional:
    primary_color: '#059669'
    secondary_color: '#6b7280'
    background_color: '#f9fafb'
    text_color: '#111827'
plugins:
  auto_update: true
  registry_url: https://github.com/arcanum/plugin-registry
  local_plugins: []
development:
  hot_reload: true
  debug_mode: false
  log_level: INFO
export:
  include_admin_mode: false
  minimal_runtime: true
  desktop:
    console: false
    debug: false
    upx: true
  web:
    include_dev_server: true
    minify: false
admin:
  password: null
  capabilities:
    gui_builder: true
    logic_editor: true
    export_system: true
    plugin_system: true
    project_editing: true
    debug_mode: true
    live_reload: true

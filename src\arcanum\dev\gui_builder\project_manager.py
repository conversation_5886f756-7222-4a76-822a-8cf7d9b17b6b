"""
Enhanced Project Management System for Arcanum GUI Builder.
"""

import json
import os
from pathlib import Path
from typing import List, Dict, Optional, Any
from datetime import datetime

try:
    from PyQt5.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
        QScrollArea, QFrame, QDialog, QLineEdit, QTextEdit, QComboBox,
        QFileDialog, QMessageBox, QGroupBox, QListWidget, QListWidgetItem
    )
    from PyQt5.QtCore import Qt, pyqtSignal, QSize
    from PyQt5.QtGui import QPixmap, QFont, QPalette, QIcon
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

from ...core.project import ArcanumProject


class RecentProjectsManager:
    """Manages recent projects list with persistent storage."""
    
    def __init__(self):
        self.config_dir = Path.home() / ".arcanum"
        self.config_dir.mkdir(exist_ok=True)
        self.recent_file = self.config_dir / "recent_projects.json"
        self.max_recent = 10
        
    def load_recent_projects(self) -> List[Dict[str, Any]]:
        """Load recent projects from storage."""
        if not self.recent_file.exists():
            return []
            
        try:
            with open(self.recent_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError):
            return []
    
    def add_recent_project(self, project_path: Path, project_name: str = None):
        """Add a project to recent projects list."""
        recent_projects = self.load_recent_projects()
        
        # Remove if already exists
        recent_projects = [p for p in recent_projects if p.get('path') != str(project_path)]
        
        # Add to front
        project_info = {
            'path': str(project_path),
            'name': project_name or project_path.name,
            'last_opened': datetime.now().isoformat(),
            'exists': project_path.exists()
        }
        recent_projects.insert(0, project_info)
        
        # Keep only max_recent items
        recent_projects = recent_projects[:self.max_recent]
        
        # Save
        try:
            with open(self.recent_file, 'w', encoding='utf-8') as f:
                json.dump(recent_projects, f, indent=2)
        except IOError:
            pass  # Silent failure for recent projects


class ProjectValidator:
    """Validates Arcanum project structure and configuration."""
    
    @staticmethod
    def is_valid_project(project_path: Path) -> tuple[bool, str]:
        """
        Validate if a directory is a valid Arcanum project.
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not project_path.exists():
            return False, "Directory does not exist"
            
        if not project_path.is_dir():
            return False, "Path is not a directory"
            
        # Check for Arcanum.yaml
        config_file = project_path / "Arcanum.yaml"
        if not config_file.exists():
            return False, "Missing Arcanum.yaml configuration file"
            
        # Try to load the project
        try:
            project = ArcanumProject(project_path)
            project.load()
            return True, ""
        except Exception as e:
            return False, f"Invalid project configuration: {str(e)}"
    
    @staticmethod
    def get_project_info(project_path: Path) -> Optional[Dict[str, Any]]:
        """Get basic project information."""
        try:
            project = ArcanumProject(project_path)
            config = project.load()
            return {
                'name': config.app_name,
                'description': config.description,
                'version': config.version,
                'path': str(project_path)
            }
        except Exception:
            return None


class ExampleProjectDetector:
    """Detects and manages example projects."""
    
    def __init__(self, examples_dir: Path = None):
        if examples_dir is None:
            # Default to examples directory relative to project root
            self.examples_dir = Path(__file__).parent.parent.parent.parent.parent / "examples"
        else:
            self.examples_dir = examples_dir
    
    def get_example_projects(self) -> List[Dict[str, Any]]:
        """Get list of available example projects."""
        examples = []
        
        if not self.examples_dir.exists():
            return examples
            
        for item in self.examples_dir.iterdir():
            if item.is_dir() and (item / "Arcanum.yaml").exists():
                project_info = ProjectValidator.get_project_info(item)
                if project_info:
                    # Look for screenshot
                    screenshot_path = item / "screenshot.png"
                    if not screenshot_path.exists():
                        screenshot_path = item / "docs" / "screenshot.png"
                    
                    examples.append({
                        **project_info,
                        'screenshot': str(screenshot_path) if screenshot_path.exists() else None,
                        'is_example': True
                    })
        
        return examples


class ProjectCard(QFrame):
    """A card widget for displaying project information."""
    
    project_selected = pyqtSignal(str)  # Emits project path
    
    def __init__(self, project_info: Dict[str, Any], parent=None):
        super().__init__(parent)
        self.project_info = project_info
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the card UI."""
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                background-color: white;
                margin: 5px;
            }
            QFrame:hover {
                border-color: #3b82f6;
                background-color: #f8fafc;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Screenshot or placeholder
        screenshot_label = QLabel()
        screenshot_label.setFixedSize(200, 120)
        screenshot_label.setStyleSheet("""
            QLabel {
                border: 1px solid #e2e8f0;
                border-radius: 4px;
                background-color: #f1f5f9;
            }
        """)
        screenshot_label.setAlignment(Qt.AlignCenter)
        
        if self.project_info.get('screenshot') and Path(self.project_info['screenshot']).exists():
            pixmap = QPixmap(self.project_info['screenshot'])
            screenshot_label.setPixmap(pixmap.scaled(200, 120, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        else:
            screenshot_label.setText("📁\nNo Preview")
            screenshot_label.setStyleSheet(screenshot_label.styleSheet() + "color: #64748b; font-size: 14px;")
        
        layout.addWidget(screenshot_label)
        
        # Project name
        name_label = QLabel(self.project_info.get('name', 'Unnamed Project'))
        name_label.setFont(QFont("Arial", 12, QFont.Bold))
        name_label.setStyleSheet("color: #1e293b;")
        layout.addWidget(name_label)
        
        # Project description
        desc_label = QLabel(self.project_info.get('description', 'No description'))
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: #64748b; font-size: 11px;")
        desc_label.setMaximumHeight(40)
        layout.addWidget(desc_label)
        
        # Open button
        open_btn = QPushButton("Open Project")
        open_btn.setStyleSheet("""
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
        """)
        open_btn.clicked.connect(self.open_project)
        layout.addWidget(open_btn)
        
        self.setFixedSize(230, 250)
    
    def open_project(self):
        """Emit signal to open this project."""
        self.project_selected.emit(self.project_info['path'])


class NewProjectDialog(QDialog):
    """Dialog for creating new projects."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Create New Arcanum Project")
        self.setModal(True)
        self.resize(500, 400)
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # Title
        title = QLabel("Create New Project")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #1e293b; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # Project details form
        form_layout = QVBoxLayout()
        
        # Project name
        form_layout.addWidget(QLabel("Project Name:"))
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("My Awesome App")
        form_layout.addWidget(self.name_edit)
        
        # Project location
        form_layout.addWidget(QLabel("Project Location:"))
        location_layout = QHBoxLayout()
        self.location_edit = QLineEdit()
        self.location_edit.setPlaceholderText("Choose directory...")
        location_layout.addWidget(self.location_edit)
        
        browse_btn = QPushButton("Browse...")
        browse_btn.clicked.connect(self.browse_location)
        location_layout.addWidget(browse_btn)
        form_layout.addLayout(location_layout)
        
        # Description
        form_layout.addWidget(QLabel("Description (optional):"))
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("Describe your application...")
        self.description_edit.setMaximumHeight(80)
        form_layout.addWidget(self.description_edit)
        
        # Template selection (placeholder for future)
        form_layout.addWidget(QLabel("Template:"))
        self.template_combo = QComboBox()
        self.template_combo.addItems(["Default Project (Templates coming soon)"])
        self.template_combo.setEnabled(False)  # Disabled as placeholder
        form_layout.addWidget(self.template_combo)

        # Template description
        self.template_desc = QLabel("Creates a new project with a helpful welcome screen and basic structure.")
        self.template_desc.setWordWrap(True)
        self.template_desc.setStyleSheet("color: #64748b; font-size: 11px; margin: 5px 0;")
        form_layout.addWidget(self.template_desc)
        
        layout.addLayout(form_layout)
        
        # Buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        create_btn = QPushButton("Create Project")
        create_btn.setStyleSheet("""
            QPushButton {
                background-color: #059669;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #047857;
            }
        """)
        create_btn.clicked.connect(self.create_project)
        button_layout.addWidget(create_btn)
        
        layout.addLayout(button_layout)
    
    def browse_location(self):
        """Browse for project location."""
        directory = QFileDialog.getExistingDirectory(
            self, "Select Project Directory", str(Path.home())
        )
        if directory:
            self.location_edit.setText(directory)
    
    def create_project(self):
        """Create the new project."""
        name = self.name_edit.text().strip()
        location = self.location_edit.text().strip()
        description = self.description_edit.toPlainText().strip()
        
        if not name:
            QMessageBox.warning(self, "Error", "Please enter a project name.")
            return
            
        if not location:
            QMessageBox.warning(self, "Error", "Please select a project location.")
            return
        
        # Create project path
        project_path = Path(location) / name
        
        if project_path.exists():
            reply = QMessageBox.question(
                self, "Directory Exists", 
                f"Directory '{project_path}' already exists. Continue anyway?",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply != QMessageBox.Yes:
                return
        
        self.project_path = project_path
        self.project_name = name
        self.project_description = description
        self.template_type = "Default"  # Simplified - no template logic
        self.accept()


class ModernDashboard(QWidget):
    """Modern dashboard for project management."""

    project_selected = pyqtSignal(str)  # Emits project path
    new_project_requested = pyqtSignal()
    open_project_requested = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.recent_manager = RecentProjectsManager()
        self.example_detector = ExampleProjectDetector()
        self.setup_ui()

    def setup_ui(self):
        """Set up the dashboard UI."""
        layout = QVBoxLayout(self)
        layout.setSpacing(30)
        layout.setContentsMargins(40, 40, 40, 40)

        # Header
        header_layout = QVBoxLayout()

        title = QLabel("🎨 Arcanum Builder")
        title.setFont(QFont("Arial", 28, QFont.Bold))
        title.setStyleSheet("color: #1e293b; margin-bottom: 5px;")
        title.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title)

        subtitle = QLabel("Create beautiful applications with drag-and-drop simplicity")
        subtitle.setFont(QFont("Arial", 14))
        subtitle.setStyleSheet("color: #64748b; margin-bottom: 20px;")
        subtitle.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(subtitle)

        layout.addLayout(header_layout)

        # Action buttons
        action_layout = QHBoxLayout()
        action_layout.setSpacing(15)
        action_layout.setAlignment(Qt.AlignCenter)

        new_btn = self.create_action_button("📁 New Project", "#059669", self.new_project_requested.emit)
        open_btn = self.create_action_button("📂 Open Project", "#3b82f6", self.open_project_requested.emit)

        action_layout.addWidget(new_btn)
        action_layout.addWidget(open_btn)
        layout.addLayout(action_layout)

        # Content area with scroll
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setStyleSheet("QScrollArea { border: none; }")

        content_widget = QWidget()
        self.content_layout = QVBoxLayout(content_widget)
        self.content_layout.setSpacing(30)

        # Add some spacing
        self.content_layout.addSpacing(50)

        self.content_layout.addStretch()
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)

    def create_action_button(self, text: str, color: str, callback) -> QPushButton:
        """Create a styled action button."""
        btn = QPushButton(text)
        btn.setFont(QFont("Arial", 12, QFont.Bold))
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                min-width: 150px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
        """)
        btn.clicked.connect(callback)
        return btn

    def darken_color(self, hex_color: str) -> str:
        """Darken a hex color for hover effects."""
        color_map = {
            "#059669": "#047857",
            "#3b82f6": "#2563eb",
            "#8b5cf6": "#7c3aed"
        }
        return color_map.get(hex_color, hex_color)



    def refresh_projects(self):
        """Refresh the project lists (simplified - no longer needed)."""
        pass



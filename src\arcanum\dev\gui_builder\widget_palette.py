"""
Widget Palette for the Arcanum GUI Builder.
"""

from typing import List
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QLabel,
    QScrollArea, QGroupBox, QGridLayout, QToolButton,
    QSizePolicy, QApplication
)
from PyQt5.QtCore import Qt, pyqtSignal, QMimeData
from PyQt5.QtGui import QDrag, QPainter, QPixmap, QFont

from ...core.registry import WidgetRegistry
from ...schemas.widgets import WidgetDefinition, WidgetType
from .widget_icons import WidgetIconProvider, WidgetTemplateProvider


class WidgetButton(QToolButton):
    """
    A compact draggable button representing a widget in the palette.
    """

    def __init__(self, widget_def: WidgetDefinition, parent=None):
        super().__init__(parent)
        self.widget_def = widget_def

        # Set up compact button appearance with professional icon
        icon_text = WidgetIconProvider.get_widget_icon(widget_def.name)
        self.setText(f"{icon_text} {widget_def.name.title()}")
        self.setToolTip(f"{widget_def.description}\nCategory: {widget_def.category}\nDrag to canvas to add")

        # Compact sizing
        self.setMinimumSize(120, 32)
        self.setMaximumSize(200, 36)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        # Compact professional styling
        self.setStyleSheet("""
            QToolButton {
                border: 1px solid #dee2e6;
                border-radius: 4px;
                background-color: #ffffff;
                padding: 4px 8px;
                text-align: left;
                font-size: 10px;
                font-weight: 500;
                color: #495057;
            }
            QToolButton:hover {
                background-color: #e3f2fd;
                border-color: #2196f3;
                color: #1976d2;
            }
            QToolButton:pressed {
                background-color: #bbdefb;
                border-color: #1976d2;
            }
        """)


        
    def mousePressEvent(self, event):
        """Handle mouse press for drag initiation."""
        if event.button() == Qt.LeftButton:
            self.drag_start_position = event.pos()
        super().mousePressEvent(event)
        
    def mouseMoveEvent(self, event):
        """Handle mouse move for drag operation."""
        if not (event.buttons() & Qt.LeftButton):
            return
            
        if not hasattr(self, 'drag_start_position'):
            return
            
        if ((event.pos() - self.drag_start_position).manhattanLength() < 
            QApplication.startDragDistance()):
            return
            
        # Start drag operation
        drag = QDrag(self)
        mime_data = QMimeData()
        
        # Set widget data
        mime_data.setText(f"arcanum_widget:{self.widget_def.type.value}")
        mime_data.setData("application/x-arcanum-widget", 
                         self.widget_def.type.value.encode())
        
        drag.setMimeData(mime_data)
        
        # Create drag pixmap
        pixmap = QPixmap(self.size())
        pixmap.fill(Qt.transparent)
        painter = QPainter(pixmap)
        self.render(painter)
        painter.end()
        
        drag.setPixmap(pixmap)
        drag.setHotSpot(event.pos())
        
        # Execute drag
        drop_action = drag.exec_(Qt.CopyAction)


class WidgetCategory(QGroupBox):
    """
    A collapsible group of widgets in a category.
    """
    
    def __init__(self, category_name: str, widgets: List[WidgetDefinition], parent=None):
        super().__init__(category_name.title(), parent)
        self.category_name = category_name
        self.widgets = widgets
        self.collapsed = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the compact category UI."""
        layout = QVBoxLayout(self)
        layout.setSpacing(2)
        layout.setContentsMargins(4, 8, 4, 4)

        # Add widget buttons in a vertical list for compact display
        for widget_def in self.widgets:
            button = WidgetButton(widget_def)
            layout.addWidget(button)

        # Add stretch to prevent expansion
        layout.addStretch()
        
        # Style the group box with professional color system
        color = WidgetIconProvider.get_category_color(self.category_name)

        self.setStyleSheet(f"""
            QGroupBox {{
                font-weight: 600;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: {color};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 6px 0 6px;
                color: #495057;
                font-size: 10px;
                background-color: white;
                border-radius: 3px;
            }}
        """)


class WidgetPalette(QWidget):
    """
    Widget palette showing available widgets organized by category.
    """
    
    # Signals
    widget_selected = pyqtSignal(str)  # Emits widget type
    
    def __init__(self, widget_registry: WidgetRegistry, parent=None):
        super().__init__(parent)
        self.widget_registry = widget_registry
        
        self.setup_ui()
        self.populate_widgets()
        
    def setup_ui(self):
        """Set up the palette UI."""
        layout = QVBoxLayout(self)
        
        # Title
        title_label = QLabel("Widget Palette")
        title_font = QFont()
        title_font.setBold(True)
        title_font.setPointSize(12)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Search box (placeholder for future implementation)
        # search_box = QLineEdit()
        # search_box.setPlaceholderText("Search widgets...")
        # layout.addWidget(search_box)
        
        # Scroll area for widget categories
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        # Container for categories
        self.categories_widget = QWidget()
        self.categories_layout = QVBoxLayout(self.categories_widget)
        self.categories_layout.setSpacing(8)
        
        scroll_area.setWidget(self.categories_widget)
        layout.addWidget(scroll_area)
        
        # Style the palette
        self.setStyleSheet("""
            WidgetPalette {
                background-color: #f8f9fa;
                border-right: 1px solid #dee2e6;
            }
        """)
        
    def populate_widgets(self):
        """Populate the palette with widgets from the registry."""
        # Get widgets organized by category
        categories = self.widget_registry.get_widgets_by_category()

        for category_name in sorted(categories.keys()):
            widget_names = categories[category_name]
            widget_defs = []

            for widget_name in widget_names:
                widget_def = self.widget_registry.get_widget(widget_name)
                if widget_def:
                    widget_defs.append(widget_def)

            if widget_defs:
                category_widget = WidgetCategory(category_name, widget_defs)
                self.categories_layout.addWidget(category_widget)
                
        # Add stretch to push categories to top
        self.categories_layout.addStretch()
        
    def refresh_widgets(self):
        """Refresh the widget palette."""
        # Clear existing categories
        while self.categories_layout.count():
            child = self.categories_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
                
        # Repopulate
        self.populate_widgets()
        
    def filter_widgets(self, search_text: str):
        """Filter widgets based on search text."""
        # TODO: Implement widget filtering
        pass

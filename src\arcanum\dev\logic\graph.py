"""
Core logic graph data structures.
"""

import uuid
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, field
from enum import Enum


class NodeType(Enum):
    """Types of logic nodes."""
    EVENT = "event"
    ACTION = "action"
    CONDITION = "condition"
    VARIABLE = "variable"
    FUNCTION = "function"
    TRIGGER = "trigger"
    DATA = "data"


class ConnectionType(Enum):
    """Types of connections between nodes."""
    EXECUTION = "execution"  # Control flow
    DATA = "data"           # Data flow
    EVENT = "event"         # Event flow


@dataclass
class NodePort:
    """Represents an input or output port on a node."""
    id: str
    name: str
    port_type: str  # "input" or "output"
    data_type: str  # "execution", "string", "number", "boolean", "object"
    required: bool = False
    default_value: Any = None
    description: str = ""


@dataclass
class LogicConnection:
    """Represents a connection between two node ports."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    source_node_id: str = ""
    source_port_id: str = ""
    target_node_id: str = ""
    target_port_id: str = ""
    connection_type: ConnectionType = ConnectionType.EXECUTION
    
    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())


@dataclass
class LogicNode:
    """Base class for all logic nodes."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    node_type: NodeType = NodeType.ACTION
    name: str = ""
    description: str = ""
    x: float = 0.0
    y: float = 0.0
    width: float = 150.0
    height: float = 100.0
    
    # Node configuration
    config: Dict[str, Any] = field(default_factory=dict)
    
    # Ports
    input_ports: List[NodePort] = field(default_factory=list)
    output_ports: List[NodePort] = field(default_factory=list)
    
    # Runtime state
    enabled: bool = True
    breakpoint: bool = False
    
    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.name:
            self.name = f"{self.node_type.value.title()} Node"
            
    def get_input_port(self, port_id: str) -> Optional[NodePort]:
        """Get an input port by ID."""
        return next((port for port in self.input_ports if port.id == port_id), None)
        
    def get_output_port(self, port_id: str) -> Optional[NodePort]:
        """Get an output port by ID."""
        return next((port for port in self.output_ports if port.id == port_id), None)
        
    def add_input_port(self, name: str, data_type: str, required: bool = False, 
                      default_value: Any = None, description: str = "") -> NodePort:
        """Add an input port to the node."""
        port = NodePort(
            id=str(uuid.uuid4()),
            name=name,
            port_type="input",
            data_type=data_type,
            required=required,
            default_value=default_value,
            description=description
        )
        self.input_ports.append(port)
        return port
        
    def add_output_port(self, name: str, data_type: str, 
                       description: str = "") -> NodePort:
        """Add an output port to the node."""
        port = NodePort(
            id=str(uuid.uuid4()),
            name=name,
            port_type="output",
            data_type=data_type,
            description=description
        )
        self.output_ports.append(port)
        return port
        
    def execute(self, inputs: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the node logic. Override in subclasses."""
        return {}
        
    def validate(self) -> List[str]:
        """Validate the node configuration. Returns list of error messages."""
        errors = []
        
        # Check required input ports
        for port in self.input_ports:
            if port.required and port.default_value is None:
                errors.append(f"Required input port '{port.name}' has no default value")
                
        return errors


class LogicGraph:
    """Represents a complete logic graph with nodes and connections."""
    
    def __init__(self, name: str = "New Graph", description: str = ""):
        self.id = str(uuid.uuid4())
        self.name = name
        self.description = description
        self.nodes: Dict[str, LogicNode] = {}
        self.connections: Dict[str, LogicConnection] = {}
        self.variables: Dict[str, Any] = {}
        self.metadata: Dict[str, Any] = {}
        
    def add_node(self, node: LogicNode) -> str:
        """Add a node to the graph."""
        self.nodes[node.id] = node
        return node.id
        
    def remove_node(self, node_id: str) -> bool:
        """Remove a node and all its connections."""
        if node_id not in self.nodes:
            return False
            
        # Remove all connections to/from this node
        connections_to_remove = []
        for conn_id, connection in self.connections.items():
            if (connection.source_node_id == node_id or 
                connection.target_node_id == node_id):
                connections_to_remove.append(conn_id)
                
        for conn_id in connections_to_remove:
            del self.connections[conn_id]
            
        # Remove the node
        del self.nodes[node_id]
        return True
        
    def add_connection(self, connection: LogicConnection) -> str:
        """Add a connection to the graph."""
        # Validate connection
        if (connection.source_node_id not in self.nodes or
            connection.target_node_id not in self.nodes):
            raise ValueError("Connection references non-existent nodes")
            
        source_node = self.nodes[connection.source_node_id]
        target_node = self.nodes[connection.target_node_id]
        
        # Check if ports exist
        source_port = source_node.get_output_port(connection.source_port_id)
        target_port = target_node.get_input_port(connection.target_port_id)
        
        if not source_port or not target_port:
            raise ValueError("Connection references non-existent ports")
            
        self.connections[connection.id] = connection
        return connection.id
        
    def remove_connection(self, connection_id: str) -> bool:
        """Remove a connection from the graph."""
        if connection_id in self.connections:
            del self.connections[connection_id]
            return True
        return False
        
    def get_node_connections(self, node_id: str) -> List[LogicConnection]:
        """Get all connections for a specific node."""
        connections = []
        for connection in self.connections.values():
            if (connection.source_node_id == node_id or 
                connection.target_node_id == node_id):
                connections.append(connection)
        return connections
        
    def get_execution_order(self) -> List[str]:
        """Get nodes in execution order using topological sort."""
        # Simple topological sort implementation
        visited = set()
        temp_visited = set()
        order = []
        
        def visit(node_id: str):
            if node_id in temp_visited:
                raise ValueError("Circular dependency detected in graph")
            if node_id in visited:
                return
                
            temp_visited.add(node_id)
            
            # Visit all nodes that this node depends on
            for connection in self.connections.values():
                if (connection.target_node_id == node_id and 
                    connection.connection_type == ConnectionType.EXECUTION):
                    visit(connection.source_node_id)
                    
            temp_visited.remove(node_id)
            visited.add(node_id)
            order.append(node_id)
            
        # Visit all nodes
        for node_id in self.nodes.keys():
            if node_id not in visited:
                visit(node_id)
                
        return order
        
    def validate(self) -> Dict[str, Any]:
        """Validate the entire graph."""
        errors = []
        warnings = []
        
        # Validate individual nodes
        for node in self.nodes.values():
            node_errors = node.validate()
            errors.extend([f"Node {node.name}: {error}" for error in node_errors])
            
        # Check for circular dependencies
        try:
            self.get_execution_order()
        except ValueError as e:
            errors.append(str(e))
            
        # Check for disconnected nodes
        connected_nodes = set()
        for connection in self.connections.values():
            connected_nodes.add(connection.source_node_id)
            connected_nodes.add(connection.target_node_id)
            
        disconnected = set(self.nodes.keys()) - connected_nodes
        if disconnected:
            warnings.extend([f"Node {self.nodes[node_id].name} is not connected" 
                           for node_id in disconnected])
            
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
        
    def clone(self) -> 'LogicGraph':
        """Create a deep copy of the graph."""
        import copy
        new_graph = LogicGraph(self.name + " (Copy)", self.description)
        new_graph.nodes = copy.deepcopy(self.nodes)
        new_graph.connections = copy.deepcopy(self.connections)
        new_graph.variables = copy.deepcopy(self.variables)
        new_graph.metadata = copy.deepcopy(self.metadata)
        return new_graph

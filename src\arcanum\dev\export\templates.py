"""
Template management for export system.
"""

import json
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging

from .base import ExportTarget

logger = logging.getLogger(__name__)


class ExportTemplate:
    """Represents an export template."""
    
    def __init__(self, name: str, target: ExportTarget, template_path: Path):
        """
        Initialize export template.
        
        Args:
            name: Template name.
            target: Export target.
            template_path: Path to template files.
        """
        self.name = name
        self.target = target
        self.template_path = template_path
        self.config_file = template_path / "template.json"
        self._config = None
    
    @property
    def config(self) -> Dict[str, Any]:
        """Get template configuration."""
        if self._config is None:
            self._load_config()
        return self._config
    
    def _load_config(self):
        """Load template configuration."""
        if self.config_file.exists():
            try:
                with open(self.config_file, "r") as f:
                    self._config = json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load template config: {e}")
                self._config = {}
        else:
            self._config = {}
    
    def get_files(self) -> List[Path]:
        """Get list of template files."""
        files = []
        for item in self.template_path.rglob("*"):
            if item.is_file() and item.name != "template.json":
                files.append(item)
        return files
    
    def copy_to(self, destination: Path, variables: Optional[Dict[str, str]] = None):
        """
        Copy template to destination with variable substitution.
        
        Args:
            destination: Destination path.
            variables: Variables for substitution.
        """
        variables = variables or {}
        
        # Ensure destination exists
        destination.mkdir(parents=True, exist_ok=True)
        
        # Copy files
        for file_path in self.get_files():
            relative_path = file_path.relative_to(self.template_path)
            dest_file = destination / relative_path
            
            # Create parent directories
            dest_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Copy and process file
            if self._is_text_file(file_path):
                self._copy_text_file(file_path, dest_file, variables)
            else:
                shutil.copy2(file_path, dest_file)
    
    def _is_text_file(self, file_path: Path) -> bool:
        """Check if file is a text file that should be processed."""
        text_extensions = {
            ".txt", ".md", ".json", ".yaml", ".yml", ".xml", ".html", ".htm",
            ".css", ".js", ".ts", ".jsx", ".tsx", ".py", ".java", ".cpp", ".c",
            ".h", ".hpp", ".cs", ".php", ".rb", ".go", ".rs", ".swift", ".kt"
        }
        return file_path.suffix.lower() in text_extensions
    
    def _copy_text_file(self, source: Path, dest: Path, variables: Dict[str, str]):
        """Copy text file with variable substitution."""
        try:
            with open(source, "r", encoding="utf-8") as f:
                content = f.read()
            
            # Perform variable substitution
            for key, value in variables.items():
                placeholder = f"{{{{{key}}}}}"
                content = content.replace(placeholder, value)
            
            with open(dest, "w", encoding="utf-8") as f:
                f.write(content)
                
        except Exception as e:
            logger.warning(f"Failed to process text file {source}: {e}")
            # Fallback to binary copy
            shutil.copy2(source, dest)


class ExportTemplateManager:
    """
    Manages export templates.
    """
    
    def __init__(self, templates_dir: Optional[Path] = None):
        """
        Initialize template manager.
        
        Args:
            templates_dir: Directory containing templates.
        """
        if templates_dir is None:
            # Use default templates directory
            templates_dir = Path(__file__).parent / "templates"
        
        self.templates_dir = templates_dir
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Ensure templates directory exists
        self.templates_dir.mkdir(parents=True, exist_ok=True)
    
    def get_available_templates(self) -> Dict[ExportTarget, List[ExportTemplate]]:
        """
        Get available templates organized by target.
        
        Returns:
            Dictionary mapping targets to available templates.
        """
        templates = {}
        
        for target_dir in self.templates_dir.iterdir():
            if not target_dir.is_dir():
                continue
            
            try:
                target = ExportTarget(target_dir.name)
            except ValueError:
                self.logger.warning(f"Unknown export target: {target_dir.name}")
                continue
            
            target_templates = []
            
            for template_dir in target_dir.iterdir():
                if template_dir.is_dir():
                    template = ExportTemplate(
                        name=template_dir.name,
                        target=target,
                        template_path=template_dir
                    )
                    target_templates.append(template)
            
            if target_templates:
                templates[target] = target_templates
        
        return templates
    
    def get_template(self, target: ExportTarget, name: str) -> Optional[ExportTemplate]:
        """
        Get specific template.
        
        Args:
            target: Export target.
            name: Template name.
            
        Returns:
            Template or None if not found.
        """
        template_path = self.templates_dir / target.value / name
        
        if template_path.exists() and template_path.is_dir():
            return ExportTemplate(name, target, template_path)
        
        return None
    
    def create_template(
        self,
        target: ExportTarget,
        name: str,
        description: str = "",
        files: Optional[Dict[str, str]] = None
    ) -> ExportTemplate:
        """
        Create a new template.
        
        Args:
            target: Export target.
            name: Template name.
            description: Template description.
            files: Dictionary of file paths to content.
            
        Returns:
            Created template.
        """
        template_path = self.templates_dir / target.value / name
        template_path.mkdir(parents=True, exist_ok=True)
        
        # Create template configuration
        config = {
            "name": name,
            "target": target.value,
            "description": description,
            "created_at": "2024-01-01T00:00:00Z",
            "variables": [
                {"name": "PROJECT_NAME", "description": "Project name"},
                {"name": "PROJECT_VERSION", "description": "Project version"},
                {"name": "PROJECT_DESCRIPTION", "description": "Project description"}
            ]
        }
        
        with open(template_path / "template.json", "w") as f:
            json.dump(config, f, indent=2)
        
        # Create template files
        if files:
            for file_path, content in files.items():
                full_path = template_path / file_path
                full_path.parent.mkdir(parents=True, exist_ok=True)
                
                with open(full_path, "w", encoding="utf-8") as f:
                    f.write(content)
        
        return ExportTemplate(name, target, template_path)
    
    def delete_template(self, target: ExportTarget, name: str) -> bool:
        """
        Delete a template.
        
        Args:
            target: Export target.
            name: Template name.
            
        Returns:
            True if deleted successfully.
        """
        template_path = self.templates_dir / target.value / name
        
        if template_path.exists():
            try:
                shutil.rmtree(template_path)
                return True
            except Exception as e:
                self.logger.error(f"Failed to delete template: {e}")
                return False
        
        return False
    
    def initialize_default_templates(self):
        """Initialize default templates for all targets."""
        self._create_static_html_template()
        self._create_react_template()
        self._create_pyinstaller_template()
        self._create_electron_template()
    
    def _create_static_html_template(self):
        """Create default static HTML template."""
        files = {
            "index.html": '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{PROJECT_NAME}}</title>
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div id="app">
        <h1>{{PROJECT_NAME}}</h1>
        <p>{{PROJECT_DESCRIPTION}}</p>
        <p>Version: {{PROJECT_VERSION}}</p>
    </div>
    <script src="scripts/main.js"></script>
</body>
</html>''',
            
            "styles/main.css": '''/* {{PROJECT_NAME}} Styles */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}

#app {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
    color: #333;
    text-align: center;
}

p {
    color: #666;
    line-height: 1.6;
}''',
            
            "scripts/main.js": '''// {{PROJECT_NAME}} JavaScript
console.log('{{PROJECT_NAME}} v{{PROJECT_VERSION}} loaded');

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded');
});'''
        }
        
        self.create_template(
            ExportTarget.STATIC_HTML,
            "default",
            "Default static HTML template",
            files
        )
    
    def _create_react_template(self):
        """Create default React template."""
        files = {
            "src/App.js": '''import React from 'react';
import './App.css';

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>{{PROJECT_NAME}}</h1>
        <p>{{PROJECT_DESCRIPTION}}</p>
        <p>Version: {{PROJECT_VERSION}}</p>
      </header>
    </div>
  );
}

export default App;''',
            
            "src/App.css": '''.App {
  text-align: center;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.App-header h1 {
  margin-bottom: 20px;
}'''
        }
        
        self.create_template(
            ExportTarget.REACT_APP,
            "default",
            "Default React template",
            files
        )
    
    def _create_pyinstaller_template(self):
        """Create default PyInstaller template."""
        files = {
            "main.py": '''#!/usr/bin/env python3
"""
{{PROJECT_NAME}} v{{PROJECT_VERSION}}
{{PROJECT_DESCRIPTION}}
"""

import sys
from pathlib import Path

def main():
    print("{{PROJECT_NAME}} v{{PROJECT_VERSION}}")
    print("{{PROJECT_DESCRIPTION}}")
    
    # Your application logic here
    
if __name__ == "__main__":
    main()''',
            
            "requirements.txt": '''# {{PROJECT_NAME}} Requirements
# Add your dependencies here
'''
        }
        
        self.create_template(
            ExportTarget.PYINSTALLER,
            "default",
            "Default PyInstaller template",
            files
        )
    
    def _create_electron_template(self):
        """Create default Electron template."""
        files = {
            "main.js": '''const { app, BrowserWindow } = require('electron');
const path = require('path');

function createWindow() {
    const mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false
        },
        title: '{{PROJECT_NAME}}'
    });

    mainWindow.loadFile('index.html');
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});''',
            
            "index.html": '''<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{{PROJECT_NAME}}</title>
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div id="app">
        <h1>{{PROJECT_NAME}}</h1>
        <p>{{PROJECT_DESCRIPTION}}</p>
        <p>Version: {{PROJECT_VERSION}}</p>
    </div>
    <script src="renderer.js"></script>
</body>
</html>''',
            
            "styles/main.css": '''body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}

#app {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}'''
        }
        
        self.create_template(
            ExportTarget.ELECTRON,
            "default",
            "Default Electron template",
            files
        )

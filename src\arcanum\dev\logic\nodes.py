"""
Specific node implementations for the logic graph system.
"""

from typing import Dict, Any, List, Callable
from .graph import LogicNode, NodeType


class EventNode(LogicNode):
    """Node that triggers on specific events."""
    
    def __init__(self, event_type: str = "click", **kwargs):
        super().__init__(node_type=NodeType.EVENT, **kwargs)
        self.config["event_type"] = event_type
        self.name = f"{event_type.title()} Event"
        
        # Add output ports
        self.add_output_port("trigger", "execution", "Triggered when event occurs")
        self.add_output_port("event_data", "object", "Data from the event")
        
    def execute(self, inputs: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute event node."""
        event_type = self.config.get("event_type", "click")
        
        # Check if this event should trigger
        current_event = context.get("current_event", {})
        if current_event.get("type") == event_type:
            return {
                "trigger": True,
                "event_data": current_event.get("data", {})
            }
        
        return {"trigger": False, "event_data": {}}


class ActionNode(LogicNode):
    """Node that performs an action."""
    
    def __init__(self, action_type: str = "set_value", **kwargs):
        super().__init__(node_type=NodeType.ACTION, **kwargs)
        self.config["action_type"] = action_type
        self.name = f"{action_type.title()} Action"
        
        # Add input/output ports based on action type
        self.add_input_port("execute", "execution", True, description="Execute this action")
        
        if action_type == "set_value":
            self.add_input_port("target", "string", True, description="Target widget ID")
            self.add_input_port("value", "object", True, description="Value to set")
        elif action_type == "show_message":
            self.add_input_port("message", "string", True, description="Message to show")
            self.add_input_port("title", "string", False, "Info", "Message title")
        elif action_type == "navigate":
            self.add_input_port("page", "string", True, description="Page to navigate to")
        elif action_type == "api_call":
            self.add_input_port("url", "string", True, description="API endpoint URL")
            self.add_input_port("method", "string", False, "GET", "HTTP method")
            self.add_input_port("data", "object", False, description="Request data")
            
        self.add_output_port("done", "execution", "Executed when action completes")
        self.add_output_port("result", "object", "Result of the action")
        
    def execute(self, inputs: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute action node."""
        action_type = self.config.get("action_type", "set_value")
        
        if action_type == "set_value":
            target = inputs.get("target")
            value = inputs.get("value")
            # TODO: Implement widget value setting
            return {"done": True, "result": f"Set {target} to {value}"}
            
        elif action_type == "show_message":
            message = inputs.get("message", "")
            title = inputs.get("title", "Info")
            # TODO: Implement message display
            return {"done": True, "result": f"Showed message: {title} - {message}"}
            
        elif action_type == "navigate":
            page = inputs.get("page", "")
            # TODO: Implement navigation
            return {"done": True, "result": f"Navigated to {page}"}
            
        elif action_type == "api_call":
            url = inputs.get("url", "")
            method = inputs.get("method", "GET")
            data = inputs.get("data", {})
            # TODO: Implement API call
            return {"done": True, "result": f"API call to {url} with {method}"}
            
        return {"done": True, "result": "Unknown action"}


class ConditionNode(LogicNode):
    """Node that evaluates a condition."""
    
    def __init__(self, condition_type: str = "equals", **kwargs):
        super().__init__(node_type=NodeType.CONDITION, **kwargs)
        self.config["condition_type"] = condition_type
        self.name = f"{condition_type.title()} Condition"
        
        # Add input ports
        self.add_input_port("execute", "execution", True, description="Execute this condition")
        self.add_input_port("value_a", "object", True, description="First value to compare")
        
        if condition_type in ["equals", "not_equals", "greater_than", "less_than", 
                             "greater_equal", "less_equal"]:
            self.add_input_port("value_b", "object", True, description="Second value to compare")
        elif condition_type in ["is_empty", "is_not_empty"]:
            pass  # Only need value_a
        elif condition_type == "contains":
            self.add_input_port("search_value", "object", True, description="Value to search for")
            
        # Add output ports
        self.add_output_port("true", "execution", "Executed when condition is true")
        self.add_output_port("false", "execution", "Executed when condition is false")
        self.add_output_port("result", "boolean", "Boolean result of condition")
        
    def execute(self, inputs: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute condition node."""
        condition_type = self.config.get("condition_type", "equals")
        value_a = inputs.get("value_a")
        
        result = False
        
        if condition_type == "equals":
            value_b = inputs.get("value_b")
            result = value_a == value_b
        elif condition_type == "not_equals":
            value_b = inputs.get("value_b")
            result = value_a != value_b
        elif condition_type == "greater_than":
            value_b = inputs.get("value_b")
            try:
                result = float(value_a) > float(value_b)
            except (ValueError, TypeError):
                result = False
        elif condition_type == "less_than":
            value_b = inputs.get("value_b")
            try:
                result = float(value_a) < float(value_b)
            except (ValueError, TypeError):
                result = False
        elif condition_type == "is_empty":
            result = not value_a or (hasattr(value_a, '__len__') and len(value_a) == 0)
        elif condition_type == "is_not_empty":
            result = bool(value_a) and (not hasattr(value_a, '__len__') or len(value_a) > 0)
        elif condition_type == "contains":
            search_value = inputs.get("search_value")
            try:
                result = search_value in value_a
            except TypeError:
                result = False
                
        return {
            "true": result,
            "false": not result,
            "result": result
        }


class VariableNode(LogicNode):
    """Node that manages variables."""
    
    def __init__(self, operation: str = "get", variable_name: str = "", **kwargs):
        super().__init__(node_type=NodeType.VARIABLE, **kwargs)
        self.config["operation"] = operation
        self.config["variable_name"] = variable_name
        self.name = f"{operation.title()} Variable: {variable_name}"
        
        if operation == "get":
            self.add_output_port("value", "object", "Current value of the variable")
        elif operation == "set":
            self.add_input_port("execute", "execution", True, description="Execute this operation")
            self.add_input_port("value", "object", True, description="Value to set")
            self.add_output_port("done", "execution", "Executed when variable is set")
        elif operation == "increment":
            self.add_input_port("execute", "execution", True, description="Execute this operation")
            self.add_input_port("amount", "number", False, 1, "Amount to increment by")
            self.add_output_port("done", "execution", "Executed when variable is incremented")
            self.add_output_port("new_value", "number", "New value after increment")
            
    def execute(self, inputs: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute variable node."""
        operation = self.config.get("operation", "get")
        variable_name = self.config.get("variable_name", "")
        variables = context.get("variables", {})
        
        if operation == "get":
            value = variables.get(variable_name, None)
            return {"value": value}
            
        elif operation == "set":
            value = inputs.get("value")
            variables[variable_name] = value
            return {"done": True}
            
        elif operation == "increment":
            amount = inputs.get("amount", 1)
            current_value = variables.get(variable_name, 0)
            try:
                new_value = float(current_value) + float(amount)
                variables[variable_name] = new_value
                return {"done": True, "new_value": new_value}
            except (ValueError, TypeError):
                return {"done": False, "new_value": current_value}
                
        return {}


class FunctionNode(LogicNode):
    """Node that calls a custom function."""
    
    def __init__(self, function_name: str = "", **kwargs):
        super().__init__(node_type=NodeType.FUNCTION, **kwargs)
        self.config["function_name"] = function_name
        self.name = f"Function: {function_name}"
        
        # Add basic input/output ports
        self.add_input_port("execute", "execution", True, description="Execute this function")
        self.add_input_port("args", "object", False, [], "Function arguments")
        self.add_output_port("done", "execution", "Executed when function completes")
        self.add_output_port("result", "object", "Function return value")
        
    def execute(self, inputs: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute function node."""
        function_name = self.config.get("function_name", "")
        args = inputs.get("args", [])
        
        # Get custom functions from context
        functions = context.get("functions", {})
        
        if function_name in functions:
            func = functions[function_name]
            try:
                if callable(func):
                    result = func(*args) if isinstance(args, list) else func(args)
                else:
                    result = func
                return {"done": True, "result": result}
            except Exception as e:
                return {"done": False, "result": f"Error: {e}"}
        else:
            return {"done": False, "result": f"Function '{function_name}' not found"}


class TriggerNode(LogicNode):
    """Node that triggers based on time or conditions."""
    
    def __init__(self, trigger_type: str = "timer", **kwargs):
        super().__init__(node_type=NodeType.TRIGGER, **kwargs)
        self.config["trigger_type"] = trigger_type
        self.name = f"{trigger_type.title()} Trigger"
        
        if trigger_type == "timer":
            self.add_input_port("interval", "number", True, 1000, "Timer interval in milliseconds")
            self.add_input_port("repeat", "boolean", False, True, "Whether to repeat the timer")
        elif trigger_type == "startup":
            pass  # No inputs needed
        elif trigger_type == "condition":
            self.add_input_port("condition", "boolean", True, description="Condition to monitor")
            
        self.add_output_port("trigger", "execution", "Triggered when condition is met")
        
    def execute(self, inputs: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute trigger node."""
        trigger_type = self.config.get("trigger_type", "timer")
        
        if trigger_type == "startup":
            # Check if this is application startup
            if context.get("is_startup", False):
                return {"trigger": True}
        elif trigger_type == "condition":
            condition = inputs.get("condition", False)
            if condition:
                return {"trigger": True}
        elif trigger_type == "timer":
            # Timer logic would be handled by the executor
            return {"trigger": True}
            
        return {"trigger": False}


class DataNode(LogicNode):
    """Node that provides or transforms data."""
    
    def __init__(self, data_type: str = "constant", **kwargs):
        super().__init__(node_type=NodeType.DATA, **kwargs)
        self.config["data_type"] = data_type
        self.name = f"{data_type.title()} Data"
        
        if data_type == "constant":
            self.add_input_port("value", "object", False, "", "Constant value")
        elif data_type == "random":
            self.add_input_port("min", "number", False, 0, "Minimum value")
            self.add_input_port("max", "number", False, 100, "Maximum value")
        elif data_type == "current_time":
            self.add_input_port("format", "string", False, "%Y-%m-%d %H:%M:%S", "Time format")
        elif data_type == "user_input":
            self.add_input_port("prompt", "string", False, "Enter value:", "Input prompt")
            
        self.add_output_port("value", "object", "Output value")
        
    def execute(self, inputs: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute data node."""
        data_type = self.config.get("data_type", "constant")
        
        if data_type == "constant":
            value = inputs.get("value", "")
            return {"value": value}
            
        elif data_type == "random":
            import random
            min_val = inputs.get("min", 0)
            max_val = inputs.get("max", 100)
            value = random.uniform(float(min_val), float(max_val))
            return {"value": value}
            
        elif data_type == "current_time":
            import datetime
            format_str = inputs.get("format", "%Y-%m-%d %H:%M:%S")
            value = datetime.datetime.now().strftime(format_str)
            return {"value": value}
            
        elif data_type == "user_input":
            prompt = inputs.get("prompt", "Enter value:")
            # TODO: Implement user input dialog
            value = f"User input for: {prompt}"
            return {"value": value}
            
        return {"value": None}

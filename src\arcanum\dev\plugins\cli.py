"""
CLI commands for plugin management.
"""

import click
from typing import Optional
from pathlib import Path
import json
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text

from .manager import PluginManager
from .manifest import create_plugin_manifest_template
from ...core.project import ArcanumProject

console = Console()


@click.group(name="plugin")
@click.pass_context
def plugin_cli(ctx):
    """Plugin management commands."""
    # Get project path from context
    project_path = ctx.obj.get('project_path', Path.cwd())
    
    # Initialize plugin manager
    plugins_path = project_path / "plugins"
    ctx.obj['plugin_manager'] = PluginManager(plugins_path)


@plugin_cli.command()
@click.argument('query', required=False)
@click.option('--tags', help='Filter by tags (comma-separated)')
@click.option('--limit', default=20, help='Maximum number of results')
@click.pass_context
def search(ctx, query: Optional[str], tags: Optional[str], limit: int):
    """Search for plugins in the registry."""
    plugin_manager: PluginManager = ctx.obj['plugin_manager']
    
    try:
        # Parse tags
        tag_list = [t.strip() for t in tags.split(',')] if tags else []
        
        # Search registry
        with console.status("Searching plugin registry..."):
            results = plugin_manager.search_registry(query or "")
        
        if not results:
            console.print("[yellow]No plugins found matching your criteria.[/yellow]")
            return
        
        # Display results
        table = Table(title=f"Plugin Search Results ({len(results)} found)")
        table.add_column("Name", style="cyan")
        table.add_column("Version", style="green")
        table.add_column("Author", style="blue")
        table.add_column("Description", style="white")
        table.add_column("Downloads", justify="right", style="yellow")
        
        for plugin in results[:limit]:
            table.add_row(
                plugin.name,
                plugin.version,
                plugin.author,
                plugin.description[:50] + "..." if len(plugin.description) > 50 else plugin.description,
                str(plugin.downloads)
            )
        
        console.print(table)
        
    except Exception as e:
        console.print(f"[red]Error searching plugins: {e}[/red]")


@plugin_cli.command()
@click.argument('plugin_source')
@click.option('--force', is_flag=True, help='Force installation even if plugin exists')
@click.pass_context
def install(ctx, plugin_source: str, force: bool):
    """Install a plugin from registry, URL, or local path."""
    plugin_manager: PluginManager = ctx.obj['plugin_manager']
    
    try:
        with console.status(f"Installing plugin {plugin_source}..."):
            success = plugin_manager.install_plugin(plugin_source, force)
        
        if success:
            console.print(f"[green]✅ Successfully installed plugin: {plugin_source}[/green]")
        else:
            console.print(f"[red]❌ Failed to install plugin: {plugin_source}[/red]")
            
    except Exception as e:
        console.print(f"[red]Error installing plugin: {e}[/red]")


@plugin_cli.command()
@click.argument('plugin_name')
@click.option('--confirm', is_flag=True, help='Skip confirmation prompt')
@click.pass_context
def uninstall(ctx, plugin_name: str, confirm: bool):
    """Uninstall a plugin."""
    plugin_manager: PluginManager = ctx.obj['plugin_manager']
    
    try:
        # Check if plugin exists
        plugin_info = plugin_manager.get_plugin_info(plugin_name)
        if not plugin_info:
            console.print(f"[red]Plugin not found: {plugin_name}[/red]")
            return
        
        # Confirm uninstallation
        if not confirm:
            if not click.confirm(f"Are you sure you want to uninstall '{plugin_name}'?"):
                console.print("Uninstallation cancelled.")
                return
        
        with console.status(f"Uninstalling plugin {plugin_name}..."):
            success = plugin_manager.uninstall_plugin(plugin_name)
        
        if success:
            console.print(f"[green]✅ Successfully uninstalled plugin: {plugin_name}[/green]")
        else:
            console.print(f"[red]❌ Failed to uninstall plugin: {plugin_name}[/red]")
            
    except Exception as e:
        console.print(f"[red]Error uninstalling plugin: {e}[/red]")


@plugin_cli.command()
@click.option('--filter', 'filter_type', default='all', 
              type=click.Choice(['all', 'enabled', 'disabled', 'loaded']),
              help='Filter plugins by status')
@click.pass_context
def list(ctx, filter_type: str):
    """List installed plugins."""
    plugin_manager: PluginManager = ctx.obj['plugin_manager']
    
    try:
        # Discover plugins
        with console.status("Discovering plugins..."):
            plugin_manager.discover_all_plugins()
        
        # Get filtered plugins
        plugins = plugin_manager.list_plugins(filter_type)
        
        if not plugins:
            console.print(f"[yellow]No {filter_type} plugins found.[/yellow]")
            return
        
        # Display plugins
        table = Table(title=f"{filter_type.title()} Plugins ({len(plugins)})")
        table.add_column("Name", style="cyan")
        table.add_column("Version", style="green")
        table.add_column("Status", style="blue")
        table.add_column("Description", style="white")
        table.add_column("Capabilities", style="yellow")
        
        for plugin in plugins:
            # Determine status
            status_parts = []
            if plugin.installed:
                status_parts.append("installed")
            if plugin.enabled:
                status_parts.append("enabled")
            if plugin.loaded:
                status_parts.append("loaded")
            if plugin.error:
                status_parts.append("error")
            
            status = ", ".join(status_parts) or "unknown"
            
            # Get capabilities
            capabilities = [cap.type for cap in plugin.manifest.capabilities]
            capabilities_str = ", ".join(capabilities) if capabilities else "none"
            
            table.add_row(
                plugin.name,
                plugin.version,
                status,
                plugin.description[:40] + "..." if len(plugin.description) > 40 else plugin.description,
                capabilities_str
            )
        
        console.print(table)
        
    except Exception as e:
        console.print(f"[red]Error listing plugins: {e}[/red]")


@plugin_cli.command()
@click.argument('plugin_name')
@click.pass_context
def enable(ctx, plugin_name: str):
    """Enable a plugin."""
    plugin_manager: PluginManager = ctx.obj['plugin_manager']
    
    try:
        success = plugin_manager.enable_plugin(plugin_name)
        
        if success:
            console.print(f"[green]✅ Enabled plugin: {plugin_name}[/green]")
        else:
            console.print(f"[red]❌ Failed to enable plugin: {plugin_name}[/red]")
            
    except Exception as e:
        console.print(f"[red]Error enabling plugin: {e}[/red]")


@plugin_cli.command()
@click.argument('plugin_name')
@click.pass_context
def disable(ctx, plugin_name: str):
    """Disable a plugin."""
    plugin_manager: PluginManager = ctx.obj['plugin_manager']
    
    try:
        success = plugin_manager.disable_plugin(plugin_name)
        
        if success:
            console.print(f"[green]✅ Disabled plugin: {plugin_name}[/green]")
        else:
            console.print(f"[red]❌ Failed to disable plugin: {plugin_name}[/red]")
            
    except Exception as e:
        console.print(f"[red]Error disabling plugin: {e}[/red]")


@plugin_cli.command()
@click.argument('plugin_name')
@click.pass_context
def info(ctx, plugin_name: str):
    """Show detailed information about a plugin."""
    plugin_manager: PluginManager = ctx.obj['plugin_manager']
    
    try:
        plugin_info = plugin_manager.get_plugin_info(plugin_name)
        if not plugin_info:
            console.print(f"[red]Plugin not found: {plugin_name}[/red]")
            return
        
        manifest = plugin_info.manifest
        
        # Create info panel
        info_text = Text()
        info_text.append(f"Name: {manifest.name}\n", style="bold cyan")
        info_text.append(f"Version: {manifest.version}\n", style="green")
        info_text.append(f"Author: {manifest.author.name}\n", style="blue")
        if manifest.author.email:
            info_text.append(f"Email: {manifest.author.email}\n", style="blue")
        info_text.append(f"License: {manifest.license}\n", style="yellow")
        info_text.append(f"Description: {manifest.description}\n\n", style="white")
        
        # Status
        status_parts = []
        if plugin_info.installed:
            status_parts.append("installed")
        if plugin_info.enabled:
            status_parts.append("enabled")
        if plugin_info.loaded:
            status_parts.append("loaded")
        if plugin_info.error:
            status_parts.append(f"error: {plugin_info.error}")
        
        info_text.append(f"Status: {', '.join(status_parts)}\n", style="bold")
        
        # Capabilities
        if manifest.capabilities:
            info_text.append("\nCapabilities:\n", style="bold yellow")
            for cap in manifest.capabilities:
                info_text.append(f"  • {cap.type}: {cap.name}\n", style="white")
                if cap.description:
                    info_text.append(f"    {cap.description}\n", style="dim white")
        
        # Dependencies
        if manifest.dependencies:
            info_text.append("\nDependencies:\n", style="bold yellow")
            for dep in manifest.dependencies:
                info_text.append(f"  • {dep.name} {dep.version}\n", style="white")
        
        # Permissions
        permissions = manifest.permissions
        if permissions.requires_approval():
            info_text.append("\nPermissions:\n", style="bold red")
            if permissions.filesystem:
                info_text.append("  • Filesystem access\n", style="red")
            if permissions.network:
                info_text.append("  • Network access\n", style="red")
            if permissions.database:
                info_text.append("  • Database access\n", style="red")
            if permissions.system:
                info_text.append("  • System access\n", style="red")
            if permissions.user_data:
                info_text.append("  • User data access\n", style="red")
        
        panel = Panel(info_text, title=f"Plugin Information: {plugin_name}", border_style="blue")
        console.print(panel)
        
    except Exception as e:
        console.print(f"[red]Error getting plugin info: {e}[/red]")


@plugin_cli.command()
@click.argument('plugin_name')
@click.option('--author-name', prompt=True, help='Plugin author name')
@click.option('--author-email', prompt=True, help='Plugin author email')
@click.option('--description', prompt=True, help='Plugin description')
@click.pass_context
def create(ctx, plugin_name: str, author_name: str, author_email: str, description: str):
    """Create a new plugin template."""
    try:
        project_path = ctx.obj.get('project_path', Path.cwd())
        plugins_path = project_path / "plugins"
        plugin_path = plugins_path / plugin_name
        
        # Check if plugin already exists
        if plugin_path.exists():
            console.print(f"[red]Plugin directory already exists: {plugin_path}[/red]")
            return
        
        # Create plugin directory
        plugin_path.mkdir(parents=True, exist_ok=True)
        
        # Create manifest
        manifest_content = create_plugin_manifest_template(
            plugin_name, description, author_name, author_email
        )
        
        with open(plugin_path / "plugin.yaml", 'w') as f:
            f.write(manifest_content)
        
        # Create basic plugin structure
        (plugin_path / "widgets").mkdir(exist_ok=True)
        (plugin_path / "renderers").mkdir(exist_ok=True)
        (plugin_path / "commands").mkdir(exist_ok=True)
        
        # Create main plugin file
        plugin_code = f'''"""
{plugin_name} plugin for Arcanum.
"""

from arcanum.plugins.interfaces import PluginInterface


class Plugin(PluginInterface):
    """Main plugin class for {plugin_name}."""
    
    def __init__(self):
        super().__init__()
        self.name = "{plugin_name}"
        self.version = "1.0.0"
    
    def initialize(self, config):
        """Initialize the plugin."""
        self.config = config
        return True
    
    def cleanup(self):
        """Clean up plugin resources."""
        pass
'''
        
        with open(plugin_path / "__init__.py", 'w') as f:
            f.write(plugin_code)
        
        # Create README
        readme_content = f"""# {plugin_name}

{description}

## Installation

Copy this plugin to your Arcanum project's `plugins/` directory.

## Usage

Enable the plugin using:
```bash
arcanum plugin enable {plugin_name}
```

## Development

Edit the plugin files to add your custom functionality:
- `plugin.yaml` - Plugin manifest and configuration
- `__init__.py` - Main plugin code
- `widgets/` - Custom widgets
- `renderers/` - Custom renderers
- `commands/` - CLI commands
"""
        
        with open(plugin_path / "README.md", 'w') as f:
            f.write(readme_content)
        
        console.print(f"[green]✅ Created plugin template: {plugin_path}[/green]")
        console.print(f"[blue]Edit {plugin_path}/plugin.yaml to configure your plugin[/blue]")
        
    except Exception as e:
        console.print(f"[red]Error creating plugin: {e}[/red]")


@plugin_cli.command()
@click.pass_context
def refresh(ctx):
    """Refresh plugin registry cache."""
    plugin_manager: PluginManager = ctx.obj['plugin_manager']
    
    try:
        with console.status("Refreshing plugin registry..."):
            success = plugin_manager.registry.refresh_cache()
        
        if success:
            console.print("[green]✅ Plugin registry cache refreshed[/green]")
        else:
            console.print("[red]❌ Failed to refresh plugin registry cache[/red]")
            
    except Exception as e:
        console.print(f"[red]Error refreshing registry: {e}[/red]")


# Add plugin commands to main CLI
def register_plugin_commands(cli_group):
    """Register plugin commands with the main CLI."""
    cli_group.add_command(plugin_cli)

"""
Plugin registry system for discovering and managing remote plugins.
"""

import json
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging
from datetime import datetime, timedelta
from urllib.parse import urlparse

try:
    import requests
except ImportError:
    requests = None

try:
    import yaml
except ImportError:
    yaml = None

from .manifest import PluginRegistryEntry, PluginManifest

logger = logging.getLogger(__name__)


class PluginRegistry:
    """Manages plugin registry operations."""
    
    def __init__(self, registry_url: str = "", cache_duration: int = 3600):
        """
        Initialize plugin registry.
        
        Args:
            registry_url: URL to plugin registry.
            cache_duration: Cache duration in seconds.
        """
        self.registry_url = registry_url or "https://github.com/arcanum/plugin-registry"
        self.cache_duration = cache_duration
        self.cache_file = Path.home() / ".arcanum" / "plugin_cache.json"
        self.cache_file.parent.mkdir(parents=True, exist_ok=True)
        
        # In-memory cache
        self._cache: Dict[str, Any] = {}
        self._cache_timestamp: Optional[datetime] = None
        
        # Load cache from file
        self._load_cache()
    
    def search(self, query: str = "", tags: List[str] = None, limit: int = 50) -> List[PluginRegistryEntry]:
        """
        Search for plugins in the registry.
        
        Args:
            query: Search query string.
            tags: Filter by tags.
            limit: Maximum number of results.
            
        Returns:
            List of matching plugin entries.
        """
        try:
            plugins = self._get_registry_plugins()
            results = []
            
            query_lower = query.lower() if query else ""
            tags = tags or []
            
            for plugin in plugins:
                # Text search
                if query_lower:
                    searchable_text = f"{plugin.name} {plugin.description} {plugin.author}".lower()
                    if query_lower not in searchable_text:
                        continue
                
                # Tag filter
                if tags:
                    if not any(tag in plugin.tags for tag in tags):
                        continue
                
                results.append(plugin)
                
                if len(results) >= limit:
                    break
            
            # Sort by relevance (downloads, rating, updated date)
            results.sort(key=lambda p: (p.downloads, p.rating, p.updated_at), reverse=True)
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to search plugin registry: {e}")
            return []
    
    def get_plugin(self, plugin_name: str) -> Optional[PluginRegistryEntry]:
        """
        Get a specific plugin from the registry.
        
        Args:
            plugin_name: Name of the plugin.
            
        Returns:
            Plugin entry or None if not found.
        """
        try:
            plugins = self._get_registry_plugins()
            return next((p for p in plugins if p.name == plugin_name), None)
            
        except Exception as e:
            logger.error(f"Failed to get plugin {plugin_name}: {e}")
            return None
    
    def get_plugin_manifest(self, plugin_entry: PluginRegistryEntry) -> Optional[PluginManifest]:
        """
        Download and parse plugin manifest.

        Args:
            plugin_entry: Plugin registry entry.

        Returns:
            Plugin manifest or None if failed.
        """
        try:
            if not requests:
                logger.error("requests library not available")
                return None

            if not yaml:
                logger.error("yaml library not available")
                return None

            response = requests.get(plugin_entry.manifest_url, timeout=10)
            response.raise_for_status()

            manifest_data = yaml.safe_load(response.text)
            return PluginManifest(**manifest_data)

        except Exception as e:
            logger.error(f"Failed to get manifest for {plugin_entry.name}: {e}")
            return None
    
    def list_categories(self) -> List[str]:
        """
        Get list of available plugin categories.
        
        Returns:
            List of category names.
        """
        try:
            plugins = self._get_registry_plugins()
            categories = set()
            
            for plugin in plugins:
                categories.update(plugin.tags)
            
            return sorted(list(categories))
            
        except Exception as e:
            logger.error(f"Failed to get categories: {e}")
            return []
    
    def get_featured_plugins(self, limit: int = 10) -> List[PluginRegistryEntry]:
        """
        Get featured/popular plugins.
        
        Args:
            limit: Maximum number of plugins to return.
            
        Returns:
            List of featured plugins.
        """
        try:
            plugins = self._get_registry_plugins()
            
            # Sort by downloads and rating
            featured = sorted(
                plugins,
                key=lambda p: (p.verified, p.downloads, p.rating),
                reverse=True
            )
            
            return featured[:limit]
            
        except Exception as e:
            logger.error(f"Failed to get featured plugins: {e}")
            return []
    
    def refresh_cache(self) -> bool:
        """
        Force refresh of plugin registry cache.
        
        Returns:
            True if refresh successful.
        """
        try:
            self._cache.clear()
            self._cache_timestamp = None
            self._get_registry_plugins()
            return True
            
        except Exception as e:
            logger.error(f"Failed to refresh cache: {e}")
            return False
    
    def _get_registry_plugins(self) -> List[PluginRegistryEntry]:
        """Get plugins from registry with caching."""
        # Check cache validity
        if self._is_cache_valid():
            return [PluginRegistryEntry(**p) for p in self._cache.get("plugins", [])]
        
        # Fetch from remote
        plugins = self._fetch_registry_plugins()
        
        # Update cache
        self._cache = {
            "plugins": [p.model_dump() for p in plugins],
            "timestamp": datetime.now().isoformat()
        }
        self._cache_timestamp = datetime.now()
        
        # Save cache to file
        self._save_cache()
        
        return plugins
    
    def _fetch_registry_plugins(self) -> List[PluginRegistryEntry]:
        """Fetch plugins from remote registry."""
        try:
            # For GitHub-based registry, we'll use the GitHub API
            if "github.com" in self.registry_url:
                return self._fetch_from_github()
            else:
                return self._fetch_from_url()
                
        except Exception as e:
            logger.error(f"Failed to fetch registry plugins: {e}")
            return []
    
    def _fetch_from_github(self) -> List[PluginRegistryEntry]:
        """Fetch plugins from GitHub repository."""
        try:
            if not requests:
                logger.error("requests library not available")
                return []

            # Parse GitHub URL
            # Expected format: https://github.com/owner/repo
            url_parts = self.registry_url.replace("https://github.com/", "").split("/")
            if len(url_parts) < 2:
                raise ValueError("Invalid GitHub URL format")

            owner, repo = url_parts[0], url_parts[1]

            # Get registry index file
            api_url = f"https://api.github.com/repos/{owner}/{repo}/contents/registry.json"
            response = requests.get(api_url, timeout=10)
            response.raise_for_status()

            # GitHub API returns base64 encoded content
            import base64
            content_data = response.json()
            registry_content = base64.b64decode(content_data["content"]).decode("utf-8")
            registry_data = json.loads(registry_content)

            # Convert to plugin entries
            plugins = []
            for plugin_data in registry_data.get("plugins", []):
                try:
                    plugin = PluginRegistryEntry(**plugin_data)
                    plugins.append(plugin)
                except Exception as e:
                    logger.warning(f"Failed to parse plugin entry: {e}")

            return plugins

        except Exception as e:
            logger.error(f"Failed to fetch from GitHub: {e}")
            return []
    
    def _fetch_from_url(self) -> List[PluginRegistryEntry]:
        """Fetch plugins from direct URL."""
        try:
            if not requests:
                logger.error("requests library not available")
                return []

            # Assume registry.json endpoint
            registry_url = f"{self.registry_url.rstrip('/')}/registry.json"
            response = requests.get(registry_url, timeout=10)
            response.raise_for_status()

            registry_data = response.json()

            plugins = []
            for plugin_data in registry_data.get("plugins", []):
                try:
                    plugin = PluginRegistryEntry(**plugin_data)
                    plugins.append(plugin)
                except Exception as e:
                    logger.warning(f"Failed to parse plugin entry: {e}")

            return plugins

        except Exception as e:
            logger.error(f"Failed to fetch from URL: {e}")
            return []
    
    def _is_cache_valid(self) -> bool:
        """Check if cache is still valid."""
        if not self._cache or not self._cache_timestamp:
            return False
        
        age = datetime.now() - self._cache_timestamp
        return age.total_seconds() < self.cache_duration
    
    def _load_cache(self):
        """Load cache from file."""
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'r') as f:
                    data = json.load(f)
                
                self._cache = data
                timestamp_str = data.get("timestamp")
                if timestamp_str:
                    self._cache_timestamp = datetime.fromisoformat(timestamp_str)
                    
        except Exception as e:
            logger.warning(f"Failed to load plugin cache: {e}")
    
    def _save_cache(self):
        """Save cache to file."""
        try:
            with open(self.cache_file, 'w') as f:
                json.dump(self._cache, f, indent=2)
                
        except Exception as e:
            logger.warning(f"Failed to save plugin cache: {e}")


class LocalPluginRegistry:
    """Manages local plugin registry for development."""
    
    def __init__(self, registry_path: Path):
        """
        Initialize local registry.
        
        Args:
            registry_path: Path to local registry file.
        """
        self.registry_path = registry_path
        self.registry_path.parent.mkdir(parents=True, exist_ok=True)
    
    def add_plugin(self, plugin_entry: PluginRegistryEntry):
        """Add plugin to local registry."""
        registry = self._load_registry()
        
        # Remove existing entry if present
        registry["plugins"] = [
            p for p in registry["plugins"]
            if p["name"] != plugin_entry.name
        ]
        
        # Add new entry
        registry["plugins"].append(plugin_entry.model_dump())
        registry["updated_at"] = datetime.now().isoformat()
        
        self._save_registry(registry)
    
    def remove_plugin(self, plugin_name: str):
        """Remove plugin from local registry."""
        registry = self._load_registry()
        
        registry["plugins"] = [
            p for p in registry["plugins"]
            if p["name"] != plugin_name
        ]
        registry["updated_at"] = datetime.now().isoformat()
        
        self._save_registry(registry)
    
    def list_plugins(self) -> List[PluginRegistryEntry]:
        """List all plugins in local registry."""
        registry = self._load_registry()
        return [PluginRegistryEntry(**p) for p in registry["plugins"]]
    
    def _load_registry(self) -> Dict[str, Any]:
        """Load registry from file."""
        if self.registry_path.exists():
            try:
                with open(self.registry_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load local registry: {e}")
        
        return {
            "version": "1.0",
            "plugins": [],
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
    
    def _save_registry(self, registry: Dict[str, Any]):
        """Save registry to file."""
        try:
            with open(self.registry_path, 'w') as f:
                json.dump(registry, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save local registry: {e}")

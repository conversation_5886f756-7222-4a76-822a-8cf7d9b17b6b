"""
Plugin manager for Arcanum - coordinates all plugin operations.
"""

import json
import shutil
import tempfile
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging
from datetime import datetime
from urllib.parse import urlparse

try:
    import requests
except ImportError:
    requests = None

from .manifest import PluginManifest, PluginInfo, PluginRegistryEntry
from .loader import PluginLoader, LoadedPlugin
from .registry import PluginRegistry
from .security import PluginSecurity

logger = logging.getLogger(__name__)


class PluginManager:
    """Central manager for all plugin operations."""
    
    def __init__(self, plugins_path: Path, config: Dict[str, Any] = None):
        """
        Initialize plugin manager.
        
        Args:
            plugins_path: Path to plugins directory.
            config: Plugin configuration.
        """
        self.plugins_path = plugins_path
        self.config = config or {}
        
        # Initialize components
        self.loader = PluginLoader(plugins_path)
        self.registry = PluginRegistry(self.config.get("registry_url", ""))
        self.security = PluginSecurity()
        
        # Plugin state
        self.discovered_plugins: Dict[str, PluginInfo] = {}
        self.enabled_plugins: List[str] = []
        self.disabled_plugins: List[str] = []
        
        # Load plugin state
        self._load_plugin_state()
    
    def discover_all_plugins(self) -> List[PluginInfo]:
        """
        Discover all available plugins (local and remote).
        
        Returns:
            List of all discovered plugins.
        """
        # Discover local plugins
        local_plugins = self.loader.discover_plugins()
        
        # Update discovered plugins
        for plugin in local_plugins:
            self.discovered_plugins[plugin.name] = plugin
        
        return local_plugins
    
    def install_plugin(self, plugin_source: str, force: bool = False) -> bool:
        """
        Install a plugin from various sources.
        
        Args:
            plugin_source: Plugin source (name, URL, or local path).
            force: Force installation even if plugin exists.
            
        Returns:
            True if installation successful.
        """
        try:
            # Determine source type
            if Path(plugin_source).exists():
                return self._install_from_local(plugin_source, force)
            elif plugin_source.startswith(('http://', 'https://')):
                return self._install_from_url(plugin_source, force)
            else:
                return self._install_from_registry(plugin_source, force)
                
        except Exception as e:
            logger.error(f"Failed to install plugin {plugin_source}: {e}")
            return False
    
    def uninstall_plugin(self, plugin_name: str) -> bool:
        """
        Uninstall a plugin.
        
        Args:
            plugin_name: Name of plugin to uninstall.
            
        Returns:
            True if uninstallation successful.
        """
        try:
            # Check if plugin exists
            if plugin_name not in self.discovered_plugins:
                logger.error(f"Plugin not found: {plugin_name}")
                return False
            
            plugin_info = self.discovered_plugins[plugin_name]
            
            # Unload plugin if loaded
            if plugin_name in self.loader.loaded_plugins:
                self.loader.unload_plugin(plugin_name)
            
            # Run uninstall script if available
            if plugin_info.manifest.uninstall_script:
                self._run_plugin_script(plugin_info, plugin_info.manifest.uninstall_script)
            
            # Remove plugin directory
            if plugin_info.path.exists():
                shutil.rmtree(plugin_info.path)
            
            # Update state
            del self.discovered_plugins[plugin_name]
            if plugin_name in self.enabled_plugins:
                self.enabled_plugins.remove(plugin_name)
            if plugin_name in self.disabled_plugins:
                self.disabled_plugins.remove(plugin_name)
            
            self._save_plugin_state()
            
            logger.info(f"Successfully uninstalled plugin: {plugin_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to uninstall plugin {plugin_name}: {e}")
            return False
    
    def enable_plugin(self, plugin_name: str) -> bool:
        """
        Enable a plugin.
        
        Args:
            plugin_name: Name of plugin to enable.
            
        Returns:
            True if successfully enabled.
        """
        if plugin_name not in self.discovered_plugins:
            logger.error(f"Plugin not found: {plugin_name}")
            return False
        
        plugin_info = self.discovered_plugins[plugin_name]
        
        # Check permissions
        if plugin_info.manifest.requires_permissions():
            if not self.security.request_permissions(plugin_info.manifest):
                logger.warning(f"Permission denied for plugin: {plugin_name}")
                return False
        
        # Enable plugin
        plugin_info.enabled = True
        
        # Update state
        if plugin_name in self.disabled_plugins:
            self.disabled_plugins.remove(plugin_name)
        if plugin_name not in self.enabled_plugins:
            self.enabled_plugins.append(plugin_name)
        
        self._save_plugin_state()
        
        logger.info(f"Enabled plugin: {plugin_name}")
        return True
    
    def disable_plugin(self, plugin_name: str) -> bool:
        """
        Disable a plugin.
        
        Args:
            plugin_name: Name of plugin to disable.
            
        Returns:
            True if successfully disabled.
        """
        if plugin_name not in self.discovered_plugins:
            logger.error(f"Plugin not found: {plugin_name}")
            return False
        
        # Unload plugin if loaded
        if plugin_name in self.loader.loaded_plugins:
            self.loader.unload_plugin(plugin_name)
        
        # Disable plugin
        plugin_info = self.discovered_plugins[plugin_name]
        plugin_info.enabled = False
        
        # Update state
        if plugin_name in self.enabled_plugins:
            self.enabled_plugins.remove(plugin_name)
        if plugin_name not in self.disabled_plugins:
            self.disabled_plugins.append(plugin_name)
        
        self._save_plugin_state()
        
        logger.info(f"Disabled plugin: {plugin_name}")
        return True
    
    def load_plugin(self, plugin_name: str) -> Optional[LoadedPlugin]:
        """
        Load a plugin.
        
        Args:
            plugin_name: Name of plugin to load.
            
        Returns:
            Loaded plugin instance or None.
        """
        if plugin_name not in self.discovered_plugins:
            logger.error(f"Plugin not found: {plugin_name}")
            return None
        
        plugin_info = self.discovered_plugins[plugin_name]
        
        if not plugin_info.enabled:
            logger.warning(f"Plugin not enabled: {plugin_name}")
            return None
        
        return self.loader.load_plugin(plugin_info)
    
    def load_all_plugins(self) -> List[LoadedPlugin]:
        """
        Load all enabled plugins.
        
        Returns:
            List of loaded plugins.
        """
        loaded_plugins = []
        
        for plugin_name in self.enabled_plugins:
            loaded_plugin = self.load_plugin(plugin_name)
            if loaded_plugin:
                loaded_plugins.append(loaded_plugin)
        
        return loaded_plugins
    
    def get_plugin_info(self, plugin_name: str) -> Optional[PluginInfo]:
        """Get plugin information."""
        return self.discovered_plugins.get(plugin_name)
    
    def list_plugins(self, filter_type: str = "all") -> List[PluginInfo]:
        """
        List plugins based on filter.
        
        Args:
            filter_type: Filter type ("all", "enabled", "disabled", "loaded").
            
        Returns:
            List of plugin information.
        """
        plugins = list(self.discovered_plugins.values())
        
        if filter_type == "enabled":
            plugins = [p for p in plugins if p.enabled]
        elif filter_type == "disabled":
            plugins = [p for p in plugins if not p.enabled]
        elif filter_type == "loaded":
            plugins = [p for p in plugins if p.loaded]
        
        return plugins
    
    def search_registry(self, query: str) -> List[PluginRegistryEntry]:
        """
        Search plugin registry.
        
        Args:
            query: Search query.
            
        Returns:
            List of matching plugins.
        """
        return self.registry.search(query)
    
    def update_plugin(self, plugin_name: str) -> bool:
        """
        Update a plugin to the latest version.
        
        Args:
            plugin_name: Name of plugin to update.
            
        Returns:
            True if update successful.
        """
        # TODO: Implement plugin updates
        logger.info(f"Plugin updates not yet implemented: {plugin_name}")
        return False
    
    def _install_from_local(self, source_path: str, force: bool) -> bool:
        """Install plugin from local directory or archive."""
        source = Path(source_path)
        
        if source.is_file():
            # Extract archive
            with tempfile.TemporaryDirectory() as temp_dir:
                shutil.unpack_archive(str(source), temp_dir)
                # Find plugin directory in extracted files
                extracted_dirs = [d for d in Path(temp_dir).iterdir() if d.is_dir()]
                if not extracted_dirs:
                    raise ValueError("No plugin directory found in archive")
                return self._install_plugin_directory(extracted_dirs[0], force)
        else:
            # Install from directory
            return self._install_plugin_directory(source, force)
    
    def _install_from_url(self, url: str, force: bool) -> bool:
        """Install plugin from URL."""
        # TODO: Implement URL-based installation
        logger.error("URL-based plugin installation not yet implemented")
        return False
    
    def _install_from_registry(self, plugin_name: str, force: bool) -> bool:
        """Install plugin from registry."""
        # TODO: Implement registry-based installation
        logger.error("Registry-based plugin installation not yet implemented")
        return False
    
    def _install_plugin_directory(self, source_dir: Path, force: bool) -> bool:
        """Install plugin from directory."""
        # Load manifest
        manifest_path = source_dir / "plugin.yaml"
        if not manifest_path.exists():
            raise FileNotFoundError("Plugin manifest not found")
        
        manifest = PluginManifest.from_file(manifest_path)
        
        # Check if plugin already exists
        target_dir = self.plugins_path / manifest.name
        if target_dir.exists() and not force:
            raise ValueError(f"Plugin already exists: {manifest.name}")
        
        # Validate plugin
        if not self.security.validate_plugin(source_dir, manifest):
            raise ValueError("Plugin failed security validation")
        
        # Copy plugin files
        if target_dir.exists():
            shutil.rmtree(target_dir)
        shutil.copytree(source_dir, target_dir)
        
        # Run install script if available
        if manifest.install_script:
            self._run_plugin_script_by_path(target_dir, manifest.install_script)
        
        # Create plugin info
        plugin_info = PluginInfo(
            manifest=manifest,
            path=target_dir,
            installed=True,
            enabled=True,
            install_date=datetime.now()
        )
        
        # Update state
        self.discovered_plugins[manifest.name] = plugin_info
        if manifest.name not in self.enabled_plugins:
            self.enabled_plugins.append(manifest.name)
        
        self._save_plugin_state()
        
        logger.info(f"Successfully installed plugin: {manifest.name}")
        return True
    
    def _run_plugin_script(self, plugin_info: PluginInfo, script_path: str):
        """Run a plugin script."""
        self._run_plugin_script_by_path(plugin_info.path, script_path)
    
    def _run_plugin_script_by_path(self, plugin_path: Path, script_path: str):
        """Run a plugin script by path."""
        # TODO: Implement secure script execution
        logger.info(f"Plugin script execution not yet implemented: {script_path}")
    
    def _load_plugin_state(self):
        """Load plugin state from file."""
        state_file = self.plugins_path / ".plugin_state.json"
        if state_file.exists():
            try:
                with open(state_file, 'r') as f:
                    state = json.load(f)
                self.enabled_plugins = state.get("enabled", [])
                self.disabled_plugins = state.get("disabled", [])
            except Exception as e:
                logger.warning(f"Failed to load plugin state: {e}")
    
    def _save_plugin_state(self):
        """Save plugin state to file."""
        state_file = self.plugins_path / ".plugin_state.json"
        state = {
            "enabled": self.enabled_plugins,
            "disabled": self.disabled_plugins,
            "last_updated": datetime.now().isoformat()
        }
        try:
            with open(state_file, 'w') as f:
                json.dump(state, f, indent=2)
        except Exception as e:
            logger.warning(f"Failed to save plugin state: {e}")

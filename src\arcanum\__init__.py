"""
Arcanum - Modular, Schema-Driven, Offline-First Application Builder

A comprehensive framework for building desktop and web applications using
declarative YAML configuration and optional Python logic.

This package provides both runtime components (for exported applications)
and development tools (for building applications).
"""

__version__ = "1.0.1"
__author__ = "Arcanum Development Team"
__email__ = "<EMAIL>"

# Core runtime components (always available)
from .core.config import ArcanumConfig
from .core.project import ArcanumProject
from .core.registry import WidgetRegistry
from .core.layout import LayoutEngine

# Runtime interface
from .runtime import ArcanumRuntime, run_app

# Development tools (conditionally available)
try:
    from .dev import (
        ArcanumDevelopmentSuite,
        launch_development_suite,
        check_development_environment
    )
    DEVELOPMENT_TOOLS_AVAILABLE = True
except ImportError:
    DEVELOPMENT_TOOLS_AVAILABLE = False

__all__ = [
    # Core runtime
    "ArcanumConfig",
    "ArcanumProject",
    "WidgetRegistry",
    "LayoutEngine",
    "ArcanumRuntime",
    "run_app",

    # Version and metadata
    "__version__",
    "DEVELOPMENT_TOOLS_AVAILABLE",
]

# Conditional exports for development tools
if DEVELOPMENT_TOOLS_AVAILABLE:
    __all__.extend([
        "ArcanumDevelopmentSuite",
        "launch_development_suite",
        "check_development_environment"
    ])

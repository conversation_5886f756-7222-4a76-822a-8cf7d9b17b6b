"""
Comprehensive error handling and logging system for Arcanum GUI Builder.
"""

import logging
import traceback
from typing import Optional, Callable, Any
from functools import wraps
from PyQt5.QtWidgets import QMessageBox, QWidget
from PyQt5.QtCore import QObject, pyqtSignal


class ErrorHandler(QObject):
    """Centralized error handling for the GUI Builder."""
    
    # Signals
    error_occurred = pyqtSignal(str, str)  # title, message
    warning_occurred = pyqtSignal(str, str)  # title, message
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_logging()
        
    def setup_logging(self):
        """Set up logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('arcanum_builder.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('ArcanumBuilder')
        
    def handle_exception(self, exc_type, exc_value, exc_traceback, context: str = ""):
        """Handle uncaught exceptions."""
        if issubclass(exc_type, KeyboardInterrupt):
            return
            
        error_msg = f"Uncaught exception in {context}: {exc_value}"
        self.logger.error(error_msg, exc_info=(exc_type, exc_value, exc_traceback))
        
        # Emit signal for UI handling
        self.error_occurred.emit("Unexpected Error", str(exc_value))
        
    def handle_error(self, error: Exception, context: str = "", show_dialog: bool = True):
        """Handle a specific error with context."""
        error_msg = f"Error in {context}: {str(error)}"
        self.logger.error(error_msg, exc_info=True)
        
        if show_dialog:
            self.error_occurred.emit("Error", f"{context}: {str(error)}")
            
    def handle_warning(self, message: str, context: str = "", show_dialog: bool = True):
        """Handle a warning message."""
        warning_msg = f"Warning in {context}: {message}"
        self.logger.warning(warning_msg)
        
        if show_dialog:
            self.warning_occurred.emit("Warning", f"{context}: {message}")
            
    def log_info(self, message: str, context: str = ""):
        """Log an informational message."""
        info_msg = f"{context}: {message}" if context else message
        self.logger.info(info_msg)


class ErrorDialog:
    """Utility class for showing error dialogs."""
    
    @staticmethod
    def show_error(parent: Optional[QWidget], title: str, message: str, details: str = ""):
        """Show an error dialog with optional details."""
        msg_box = QMessageBox(parent)
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        
        if details:
            msg_box.setDetailedText(details)
            
        msg_box.exec_()
        
    @staticmethod
    def show_warning(parent: Optional[QWidget], title: str, message: str):
        """Show a warning dialog."""
        QMessageBox.warning(parent, title, message)
        
    @staticmethod
    def show_info(parent: Optional[QWidget], title: str, message: str):
        """Show an information dialog."""
        QMessageBox.information(parent, title, message)
        
    @staticmethod
    def ask_question(parent: Optional[QWidget], title: str, question: str) -> bool:
        """Ask a yes/no question and return the result."""
        reply = QMessageBox.question(
            parent, title, question,
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        return reply == QMessageBox.Yes


def safe_execute(error_handler: ErrorHandler, context: str = ""):
    """Decorator for safe execution of methods with error handling."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_handler.handle_error(e, context or func.__name__)
                return None
        return wrapper
    return decorator


def validate_input(value: Any, validator: Callable[[Any], bool], error_message: str) -> bool:
    """Validate input with a custom validator function."""
    try:
        return validator(value)
    except Exception:
        return False


class ValidationError(Exception):
    """Custom exception for validation errors."""
    pass


class ProjectError(Exception):
    """Custom exception for project-related errors."""
    pass


class WidgetError(Exception):
    """Custom exception for widget-related errors."""
    pass


class LayoutError(Exception):
    """Custom exception for layout-related errors."""
    pass


class FileOperationError(Exception):
    """Custom exception for file operation errors."""
    pass


class ConfigurationError(Exception):
    """Custom exception for configuration errors."""
    pass


def create_error_context(operation: str, details: dict = None) -> str:
    """Create a detailed error context string."""
    context = f"Operation: {operation}"
    if details:
        detail_str = ", ".join([f"{k}: {v}" for k, v in details.items()])
        context += f" | Details: {detail_str}"
    return context


def log_performance(func: Callable) -> Callable:
    """Decorator to log performance metrics for functions."""
    @wraps(func)
    def wrapper(*args, **kwargs):
        import time
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # Log performance if it takes longer than 100ms
            if execution_time > 0.1:
                logging.getLogger('ArcanumBuilder.Performance').info(
                    f"{func.__name__} took {execution_time:.3f}s to execute"
                )
                
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logging.getLogger('ArcanumBuilder.Performance').error(
                f"{func.__name__} failed after {execution_time:.3f}s: {e}"
            )
            raise
            
    return wrapper


class SafeSignalConnection:
    """Utility class for safe signal/slot connections."""
    
    @staticmethod
    def connect_safe(signal, slot, error_handler: ErrorHandler, context: str = ""):
        """Safely connect a signal to a slot with error handling."""
        def safe_slot(*args, **kwargs):
            try:
                return slot(*args, **kwargs)
            except Exception as e:
                error_handler.handle_error(e, f"Signal connection {context}")
                
        signal.connect(safe_slot)
        
    @staticmethod
    def disconnect_safe(signal, slot):
        """Safely disconnect a signal from a slot."""
        try:
            signal.disconnect(slot)
        except (TypeError, RuntimeError):
            # Signal was already disconnected or object was deleted
            pass


class ResourceManager:
    """Manages resources and ensures proper cleanup."""
    
    def __init__(self):
        self.resources = []
        
    def register_resource(self, resource, cleanup_func: Callable = None):
        """Register a resource for cleanup."""
        self.resources.append((resource, cleanup_func))
        
    def cleanup_all(self):
        """Clean up all registered resources."""
        for resource, cleanup_func in self.resources:
            try:
                if cleanup_func:
                    cleanup_func(resource)
                elif hasattr(resource, 'close'):
                    resource.close()
                elif hasattr(resource, 'deleteLater'):
                    resource.deleteLater()
            except Exception as e:
                logging.getLogger('ArcanumBuilder.Cleanup').warning(
                    f"Failed to cleanup resource {resource}: {e}"
                )
                
        self.resources.clear()


# Global error handler instance
_global_error_handler: Optional[ErrorHandler] = None


def get_error_handler() -> ErrorHandler:
    """Get the global error handler instance."""
    global _global_error_handler
    if _global_error_handler is None:
        _global_error_handler = ErrorHandler()
    return _global_error_handler


def set_error_handler(handler: ErrorHandler):
    """Set the global error handler instance."""
    global _global_error_handler
    _global_error_handler = handler

#!/usr/bin/env python3
"""
Arcanum Runtime Launcher

This script provides a simple entry point for exported Arcanum applications.
It's designed to be lightweight and only include runtime dependencies.
"""

import sys
import os
from pathlib import Path
from typing import Optional

# Add the current directory to Python path for imports
current_dir = Path(__file__).parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

try:
    from . import ArcanumRuntime, run_app
except ImportError:
    # Fallback for standalone exports
    from arcanum.runtime import ArcanumRuntime, run_app


def main(project_path: Optional[str] = None, 
         renderer: str = "qt", 
         page: Optional[str] = None) -> int:
    """
    Main entry point for exported Arcanum applications.
    
    Args:
        project_path: Path to the project directory (defaults to current directory).
        renderer: Renderer type to use ("qt", "web", etc.).
        page: Specific page to load (uses start_layout if None).
        
    Returns:
        Exit code.
    """
    # Determine project path
    if project_path is None:
        # Look for project in current directory or parent directories
        current = Path.cwd()
        while current != current.parent:
            if (current / "Arcanum.yaml").exists():
                project_path = str(current)
                break
            current = current.parent
        
        if project_path is None:
            # Default to current directory
            project_path = str(Path.cwd())
    
    # Run the application
    try:
        return run_app(project_path, renderer, page)
    except Exception as e:
        print(f"Error running application: {e}")
        import traceback
        traceback.print_exc()
        return 1


def cli_main():
    """Command-line interface for the runtime launcher."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Arcanum Runtime Launcher",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python launcher.py                    # Run project in current directory
  python launcher.py --project ./app   # Run specific project
  python launcher.py --page welcome    # Start with specific page
  python launcher.py --renderer web    # Use web renderer
        """
    )
    
    parser.add_argument(
        "--project", "-p",
        type=str,
        help="Path to the Arcanum project directory"
    )
    
    parser.add_argument(
        "--renderer", "-r",
        type=str,
        default="qt",
        choices=["qt", "web"],
        help="Renderer type to use (default: qt)"
    )
    
    parser.add_argument(
        "--page",
        type=str,
        help="Specific page to load (uses start_layout if not specified)"
    )
    
    parser.add_argument(
        "--version", "-v",
        action="version",
        version=f"Arcanum Runtime {__version__}"
    )
    
    args = parser.parse_args()
    
    # Run the application
    exit_code = main(
        project_path=args.project,
        renderer=args.renderer,
        page=args.page
    )
    
    sys.exit(exit_code)


if __name__ == "__main__":
    cli_main()

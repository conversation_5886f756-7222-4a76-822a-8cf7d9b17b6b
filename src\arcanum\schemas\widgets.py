"""
Pydantic schemas for widget definitions and validation.
"""

from typing import Dict, List, Optional, Any, Union, Literal
from pydantic import BaseModel, Field, validator
from enum import Enum


class WidgetType(str, Enum):
    """Supported widget types."""
    TEXT = "text"
    TEXTAREA = "textarea"
    NUMBER = "number"
    CHECKBOX = "checkbox"
    DROPDOWN = "dropdown"
    MULTI_SELECT = "multi_select"
    COLOR = "color"
    IMAGE = "image"
    SLIDER = "slider"
    RADIO = "radio"
    BUTTON = "button"
    DATE = "date"
    FILE_UPLOAD = "file_upload"
    LABEL = "label"
    TAB = "tab"
    GROUP = "group"


class ActionType(str, Enum):
    """Supported action types."""
    LINK_TO = "link_to"
    SUBMIT = "submit"
    RESET = "reset"
    CUSTOM = "custom"


class LayoutType(str, Enum):
    """Supported layout types."""
    ROW = "row"
    COLUMN = "column"
    GRID = "grid"
    FLEX = "flex"
    TAB = "tab"
    ACCORDION = "accordion"
    CANVAS = "canvas"  # Absolute positioning layout


class ValidationRule(BaseModel):
    """Widget validation rule."""
    type: Literal["required", "min", "max", "pattern", "custom"]
    value: Optional[Union[str, int, float]] = None
    message: Optional[str] = None


class WidgetPosition(BaseModel):
    """Widget position and sizing metadata for GUI editing."""
    x: int = 50  # absolute x position in pixels
    y: int = 50  # absolute y position in pixels
    width: int = 200  # widget width in pixels
    height: int = 35  # widget height in pixels
    snap_to_grid: bool = True  # snap movement to layout grid
    locked: bool = False  # prevent movement in GUI if true
    resizable: bool = True  # allow corner dragging to resize
    z_index: int = 10  # optional for layered stacking


class WidgetAction(BaseModel):
    """Widget action definition."""
    type: ActionType
    target: Optional[str] = None
    params: Optional[Dict[str, Any]] = Field(default_factory=dict)
    condition: Optional[str] = None


class WidgetStyle(BaseModel):
    """Widget styling configuration."""
    width: Optional[Union[str, int]] = None
    height: Optional[Union[str, int]] = None
    margin: Optional[str] = None
    padding: Optional[str] = None
    background_color: Optional[str] = None
    color: Optional[str] = None
    font_size: Optional[str] = None
    font_weight: Optional[str] = None
    text_align: Optional[str] = None
    border: Optional[str] = None
    border_radius: Optional[str] = None
    custom_css: Optional[str] = None


class BaseWidget(BaseModel):
    """Base widget schema."""
    id: Optional[str] = None
    widget: WidgetType
    label: Optional[str] = None
    placeholder: Optional[str] = None
    default_value: Optional[Any] = None
    required: bool = False
    disabled: bool = False
    visible: bool = True
    tooltip: Optional[str] = None
    help_text: Optional[str] = None

    # Position and sizing (for GUI editing)
    position: Optional[WidgetPosition] = Field(default_factory=WidgetPosition)

    # Styling
    style: Optional[Union[str, WidgetStyle]] = None
    css_class: Optional[str] = None

    # Validation
    validation: Optional[List[ValidationRule]] = Field(default_factory=list)

    # Actions
    action: Optional[WidgetAction] = None
    on_change: Optional[WidgetAction] = None
    on_submit: Optional[WidgetAction] = None

    # Data binding
    binds_to: Optional[str] = None
    data_source: Optional[str] = None

    class Config:
        extra = "allow"  # Allow widget-specific properties


class TextWidget(BaseWidget):
    """Text input widget."""
    widget: Literal[WidgetType.TEXT] = WidgetType.TEXT
    max_length: Optional[int] = None
    min_length: Optional[int] = None
    pattern: Optional[str] = None


class TextAreaWidget(BaseWidget):
    """Textarea widget."""
    widget: Literal[WidgetType.TEXTAREA] = WidgetType.TEXTAREA
    rows: Optional[int] = 4
    cols: Optional[int] = None
    max_length: Optional[int] = None
    resize: Optional[Literal["none", "both", "horizontal", "vertical"]] = "both"


class NumberWidget(BaseWidget):
    """Number input widget."""
    widget: Literal[WidgetType.NUMBER] = WidgetType.NUMBER
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    step: Optional[Union[int, float]] = 1
    decimal_places: Optional[int] = None


class CheckboxWidget(BaseWidget):
    """Checkbox widget."""
    widget: Literal[WidgetType.CHECKBOX] = WidgetType.CHECKBOX
    checked: bool = False


class DropdownWidget(BaseWidget):
    """Dropdown/select widget."""
    widget: Literal[WidgetType.DROPDOWN] = WidgetType.DROPDOWN
    options: List[Union[str, Dict[str, str]]] = Field(default_factory=list)
    multiple: bool = False
    searchable: bool = False


class ButtonWidget(BaseWidget):
    """Button widget."""
    widget: Literal[WidgetType.BUTTON] = WidgetType.BUTTON
    button_type: Optional[Literal["button", "submit", "reset"]] = "button"
    variant: Optional[Literal["primary", "secondary", "success", "warning", "danger"]] = "primary"


class LabelWidget(BaseWidget):
    """Label/text display widget."""
    widget: Literal[WidgetType.LABEL] = WidgetType.LABEL
    text: str
    html: bool = False


class ImageWidget(BaseWidget):
    """Image display widget."""
    widget: Literal[WidgetType.IMAGE] = WidgetType.IMAGE
    src: str
    alt: Optional[str] = None
    width: Optional[Union[str, int]] = None
    height: Optional[Union[str, int]] = None


class SliderWidget(BaseWidget):
    """Slider/range widget."""
    widget: Literal[WidgetType.SLIDER] = WidgetType.SLIDER
    min_value: Union[int, float] = 0
    max_value: Union[int, float] = 100
    step: Union[int, float] = 1
    show_value: bool = True


class WidgetDefinition(BaseModel):
    """Complete widget definition with metadata."""
    name: str
    type: WidgetType
    description: Optional[str] = None
    category: Optional[str] = "general"
    icon: Optional[str] = None
    
    # Schema definition
    schema: Dict[str, Any]
    
    # Rendering hints
    render_hints: Optional[Dict[str, Any]] = Field(default_factory=dict)
    
    # Plugin information
    plugin: Optional[str] = None
    version: Optional[str] = "1.0.0"
    
    @validator("schema")
    def validate_schema(cls, v):
        """Validate that schema is a valid JSON schema."""
        # Basic validation - could be enhanced with jsonschema
        if not isinstance(v, dict):
            raise ValueError("Schema must be a dictionary")
        return v


# Widget type mapping for dynamic creation
WIDGET_TYPE_MAP = {
    WidgetType.TEXT: TextWidget,
    WidgetType.TEXTAREA: TextAreaWidget,
    WidgetType.NUMBER: NumberWidget,
    WidgetType.CHECKBOX: CheckboxWidget,
    WidgetType.DROPDOWN: DropdownWidget,
    WidgetType.BUTTON: ButtonWidget,
    WidgetType.LABEL: LabelWidget,
    WidgetType.IMAGE: ImageWidget,
    WidgetType.SLIDER: SliderWidget,
}

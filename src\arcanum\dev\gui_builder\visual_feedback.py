"""
Visual feedback system for enhanced user experience in Arcanum GUI Builder.
"""

from typing import Op<PERSON>, List, Tuple
from PyQt5.QtWidgets import <PERSON>Widget, QLabel, QProgressBar, QFrame, QVBoxLayout, QHBoxLayout
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal, QRect
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor, QFont, QFontMetrics


class StatusIndicator(QLabel):
    """A status indicator widget with animated feedback."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(16, 16)
        self.status = "idle"  # idle, working, success, error
        self.setup_animation()
        
    def setup_animation(self):
        """Set up animation for the indicator."""
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(300)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)
        
    def set_status(self, status: str):
        """Set the status and update appearance."""
        self.status = status
        self.update_appearance()
        
    def update_appearance(self):
        """Update the visual appearance based on status."""
        colors = {
            "idle": "#6c757d",
            "working": "#ffc107", 
            "success": "#28a745",
            "error": "#dc3545"
        }
        
        color = colors.get(self.status, "#6c757d")
        self.setStyleSheet(f"""
            QLabel {{
                background-color: {color};
                border-radius: 8px;
                border: 2px solid white;
            }}
        """)
        
        if self.status == "working":
            self.start_pulse_animation()
        else:
            self.stop_pulse_animation()
            
    def start_pulse_animation(self):
        """Start pulsing animation for working status."""
        self.animation.setStartValue(self.geometry())
        expanded = self.geometry().adjusted(-2, -2, 2, 2)
        self.animation.setEndValue(expanded)
        self.animation.finished.connect(self.pulse_back)
        self.animation.start()
        
    def pulse_back(self):
        """Pulse back to original size."""
        if self.status == "working":
            self.animation.finished.disconnect()
            self.animation.setStartValue(self.animation.endValue())
            self.animation.setEndValue(self.geometry())
            self.animation.finished.connect(self.start_pulse_animation)
            self.animation.start()
            
    def stop_pulse_animation(self):
        """Stop the pulsing animation."""
        self.animation.stop()


class ProgressToast(QFrame):
    """A toast notification with progress indicator."""
    
    finished = pyqtSignal()
    
    def __init__(self, message: str, parent=None):
        super().__init__(parent)
        self.message = message
        self.setup_ui()
        self.setup_animation()
        
    def setup_ui(self):
        """Set up the UI components."""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setStyleSheet("""
            QFrame {
                background-color: rgba(0, 0, 0, 0.8);
                border-radius: 8px;
                padding: 12px;
            }
            QLabel {
                color: white;
                font-weight: bold;
            }
            QProgressBar {
                border: none;
                background-color: rgba(255, 255, 255, 0.3);
                border-radius: 4px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #007bff;
                border-radius: 4px;
            }
        """)
        
        layout = QVBoxLayout(self)
        
        # Message label
        self.label = QLabel(self.message)
        layout.addWidget(self.label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar)
        
        self.setFixedSize(300, 80)
        
    def setup_animation(self):
        """Set up fade in/out animations."""
        self.fade_in = QPropertyAnimation(self, b"windowOpacity")
        self.fade_in.setDuration(300)
        self.fade_in.setStartValue(0.0)
        self.fade_in.setEndValue(1.0)
        
        self.fade_out = QPropertyAnimation(self, b"windowOpacity")
        self.fade_out.setDuration(300)
        self.fade_out.setStartValue(1.0)
        self.fade_out.setEndValue(0.0)
        self.fade_out.finished.connect(self.finished.emit)
        
    def show_toast(self):
        """Show the toast with fade in animation."""
        self.show()
        self.fade_in.start()
        
    def hide_toast(self):
        """Hide the toast with fade out animation."""
        self.fade_out.start()
        
    def update_progress(self, value: int):
        """Update the progress bar value."""
        self.progress_bar.setValue(value)
        if value >= 100:
            QTimer.singleShot(1000, self.hide_toast)


class SelectionHighlight(QWidget):
    """Visual highlight for selected widgets."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.target_widgets: List[QWidget] = []
        self.highlight_color = QColor(0, 123, 255, 100)  # Semi-transparent blue
        self.border_color = QColor(0, 123, 255, 255)     # Solid blue
        self.setAttribute(Qt.WA_TransparentForMouseEvents)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        
    def set_target_widgets(self, widgets: List[QWidget]):
        """Set the widgets to highlight."""
        self.target_widgets = widgets
        self.update_highlight()
        
    def update_highlight(self):
        """Update the highlight overlay."""
        if not self.target_widgets:
            self.hide()
            return
            
        # Calculate bounding rectangle for all selected widgets
        if len(self.target_widgets) == 1:
            widget = self.target_widgets[0]
            global_pos = widget.mapToGlobal(widget.rect().topLeft())
            self.setGeometry(global_pos.x(), global_pos.y(), 
                           widget.width(), widget.height())
        else:
            # Multi-selection bounding box
            min_x = min_y = float('inf')
            max_x = max_y = float('-inf')
            
            for widget in self.target_widgets:
                global_pos = widget.mapToGlobal(widget.rect().topLeft())
                min_x = min(min_x, global_pos.x())
                min_y = min(min_y, global_pos.y())
                max_x = max(max_x, global_pos.x() + widget.width())
                max_y = max(max_y, global_pos.y() + widget.height())
                
            self.setGeometry(min_x - 5, min_y - 5, 
                           max_x - min_x + 10, max_y - min_y + 10)
        
        self.show()
        self.update()
        
    def paintEvent(self, event):
        """Paint the highlight overlay."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Draw highlight background
        painter.fillRect(self.rect(), self.highlight_color)
        
        # Draw border
        pen = QPen(self.border_color, 2, Qt.DashLine)
        painter.setPen(pen)
        painter.drawRect(self.rect().adjusted(1, 1, -1, -1))


class LoadingOverlay(QWidget):
    """Loading overlay with spinner animation."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.angle = 0
        self.timer = QTimer()
        self.timer.timeout.connect(self.rotate)
        self.timer.setInterval(50)  # 20 FPS
        
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(255, 255, 255, 0.8);
            }
        """)
        
    def show_loading(self, message: str = "Loading..."):
        """Show the loading overlay."""
        self.message = message
        self.show()
        self.timer.start()
        
    def hide_loading(self):
        """Hide the loading overlay."""
        self.timer.stop()
        self.hide()
        
    def rotate(self):
        """Rotate the spinner."""
        self.angle = (self.angle + 10) % 360
        self.update()
        
    def paintEvent(self, event):
        """Paint the loading spinner."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Draw background
        painter.fillRect(self.rect(), QColor(255, 255, 255, 200))
        
        # Draw spinner
        center = self.rect().center()
        radius = 30
        
        painter.translate(center)
        painter.rotate(self.angle)
        
        # Draw spinner segments
        for i in range(8):
            alpha = 255 - (i * 30)
            color = QColor(0, 123, 255, max(alpha, 50))
            painter.setPen(QPen(color, 4, Qt.SolidLine, Qt.RoundCap))
            painter.drawLine(0, -radius, 0, -radius + 10)
            painter.rotate(45)
            
        painter.resetTransform()
        
        # Draw message
        if hasattr(self, 'message'):
            font = QFont()
            font.setPointSize(12)
            font.setBold(True)
            painter.setFont(font)
            painter.setPen(QColor(0, 0, 0))
            
            text_rect = QRect(0, center.y() + 50, self.width(), 30)
            painter.drawText(text_rect, Qt.AlignCenter, self.message)


class SnapGuides(QWidget):
    """Visual guides for snapping widgets to grid or other widgets."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.guides: List[Tuple[int, int, int, int]] = []  # x1, y1, x2, y2
        self.setAttribute(Qt.WA_TransparentForMouseEvents)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        
    def show_guides(self, guides: List[Tuple[int, int, int, int]]):
        """Show snap guides."""
        self.guides = guides
        if guides:
            self.show()
            self.update()
        else:
            self.hide()
            
    def paintEvent(self, event):
        """Paint the snap guides."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Draw guides
        pen = QPen(QColor(255, 0, 0, 150), 1, Qt.DashLine)
        painter.setPen(pen)
        
        for x1, y1, x2, y2 in self.guides:
            painter.drawLine(x1, y1, x2, y2)


class FeedbackManager:
    """Manages all visual feedback components."""
    
    def __init__(self, parent_widget: QWidget):
        self.parent = parent_widget
        self.status_indicator = StatusIndicator(parent_widget)
        self.selection_highlight = SelectionHighlight()
        self.loading_overlay = LoadingOverlay(parent_widget)
        self.snap_guides = SnapGuides()
        self.active_toasts: List[ProgressToast] = []
        
        # Position status indicator
        self.position_status_indicator()
        
    def position_status_indicator(self):
        """Position the status indicator in the parent widget."""
        parent_rect = self.parent.rect()
        indicator_size = self.status_indicator.size()
        x = parent_rect.width() - indicator_size.width() - 10
        y = 10
        self.status_indicator.move(x, y)
        
    def set_status(self, status: str):
        """Set the global status."""
        self.status_indicator.set_status(status)
        
    def show_progress_toast(self, message: str) -> ProgressToast:
        """Show a progress toast notification."""
        toast = ProgressToast(message, self.parent)
        
        # Position toast
        parent_rect = self.parent.rect()
        toast_x = (parent_rect.width() - toast.width()) // 2
        toast_y = parent_rect.height() - toast.height() - 50 - (len(self.active_toasts) * 90)
        toast.move(toast_x, toast_y)
        
        # Connect cleanup
        toast.finished.connect(lambda: self.remove_toast(toast))
        
        self.active_toasts.append(toast)
        toast.show_toast()
        
        return toast
        
    def remove_toast(self, toast: ProgressToast):
        """Remove a toast from active list."""
        if toast in self.active_toasts:
            self.active_toasts.remove(toast)
            toast.deleteLater()
            
    def highlight_selection(self, widgets: List[QWidget]):
        """Highlight selected widgets."""
        self.selection_highlight.set_target_widgets(widgets)
        
    def show_loading(self, message: str = "Loading..."):
        """Show loading overlay."""
        self.loading_overlay.resize(self.parent.size())
        self.loading_overlay.show_loading(message)
        
    def hide_loading(self):
        """Hide loading overlay."""
        self.loading_overlay.hide_loading()
        
    def show_snap_guides(self, guides: List[Tuple[int, int, int, int]]):
        """Show snap guides."""
        self.snap_guides.resize(self.parent.size())
        self.snap_guides.show_guides(guides)
        
    def hide_snap_guides(self):
        """Hide snap guides."""
        self.snap_guides.show_guides([])
        
    def cleanup(self):
        """Clean up all feedback components."""
        for toast in self.active_toasts[:]:
            self.remove_toast(toast)
        self.selection_highlight.hide()
        self.loading_overlay.hide()
        self.snap_guides.hide()

"""
Desktop export implementations.
"""

import json
import os
import shutil
import platform
from pathlib import Path
from typing import List, Dict, Any
import time

from .base import BaseExporter, ExportResult, ExportTarget, ExportValidationError, ExportBuildError


class DesktopExporter(BaseExporter):
    """Base class for desktop exporters."""
    
    def validate_requirements(self) -> List[str]:
        """Validate desktop export requirements."""
        missing = []
        
        # Check Python
        import sys
        if sys.version_info < (3, 10):
            missing.append("Python 3.10+")
        
        return missing


class PyInstallerExporter(DesktopExporter):
    """Exporter using PyInstaller for desktop applications."""
    
    def validate_requirements(self) -> List[str]:
        """Validate PyInstaller requirements."""
        missing = super().validate_requirements()
        
        # Check for PyInstaller
        try:
            import PyInstaller
        except ImportError:
            missing.append("PyInstaller")
        
        return missing
    
    def export(self, project_path: Path) -> ExportResult:
        """Export project as desktop executable using PyInstaller."""
        start_time = time.time()
        result = ExportResult(
            success=False,
            target=self.config.target,
            output_path=self.config.output_path,
            build_time=0
        )
        
        try:
            # Validate requirements
            missing = self.validate_requirements()
            if missing:
                raise ExportValidationError(
                    f"Missing requirements: {', '.join(missing)}",
                    self.config.target
                )
            
            # Prepare output directory
            output_path = self.prepare_output_directory()
            
            # Create launcher script
            launcher_script = self._create_launcher_script(project_path, output_path)

            # Create admin launcher (if enabled)
            if self.config.platform_config.get("include_admin_mode", False):
                self._create_admin_launcher(project_path, output_path)

            # Create PyInstaller spec file
            spec_file = self._create_spec_file(project_path, output_path, launcher_script)
            
            # Run PyInstaller
            if not self._run_pyinstaller(spec_file, output_path):
                raise ExportBuildError("PyInstaller build failed", self.config.target)
            
            # Copy additional files
            self._copy_additional_files(project_path, output_path)
            
            # Calculate build info
            build_time = time.time() - start_time
            dist_dir = output_path / "dist"
            file_size = self.calculate_build_size(dist_dir) if dist_dir.exists() else 0
            
            result.success = True
            result.build_time = build_time
            result.file_size = file_size
            result.artifacts = list(dist_dir.rglob("*")) if dist_dir.exists() else []
            result.build_info = self.create_build_info(project_path)
            result.logs.append(f"PyInstaller export completed in {build_time:.2f}s")
            
        except Exception as e:
            result.errors.append(str(e))
            result.build_time = time.time() - start_time
            self.logger.error(f"PyInstaller export failed: {e}")
        
        return result
    
    def _create_launcher_script(self, project_path: Path, output_path: Path) -> Path:
        """Create the main launcher script."""
        launcher_content = f'''#!/usr/bin/env python3
"""
Launcher script for {self.config.project_name}
Generated by Arcanum Export System - Runtime Version
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def main():
    """Main entry point using Arcanum Runtime."""
    try:
        from arcanum.runtime import run_app

        # Load project from bundled directory
        project_path = Path(__file__).parent / "project"

        # Run the application using runtime
        return run_app(str(project_path), renderer="qt")

    except Exception as e:
        print(f"Error starting application: {{e}}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    main()
'''
        
        launcher_path = output_path / "main.py"
        with open(launcher_path, "w", encoding="utf-8") as f:
            f.write(launcher_content)
        
        return launcher_path

    def _create_admin_launcher(self, project_path: Path, output_path: Path) -> Path:
        """Create admin launcher script for development access."""
        admin_launcher_content = f'''#!/usr/bin/env python3
"""
Admin Launcher for {self.config.project_name}
Generated by Arcanum Export System - Admin Mode
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def main():
    """Main entry point with admin mode enabled."""
    try:
        from arcanum.runtime import run_app
        from arcanum.runtime.admin import check_admin_mode

        # Load project from bundled directory
        project_path = Path(__file__).parent / "project"

        # Check for admin credentials
        admin_token = os.environ.get("ARCANUM_ADMIN_TOKEN")
        admin_password = input("Enter admin password (or set ARCANUM_ADMIN_TOKEN): ") if not admin_token else None

        # Initialize admin mode
        admin_manager = check_admin_mode(str(project_path))

        if admin_token:
            admin_enabled = admin_manager.enable_admin_mode(token=admin_token)
        elif admin_password:
            admin_enabled = admin_manager.enable_admin_mode(password=admin_password)
        else:
            print("No admin credentials provided")
            return 1

        if admin_enabled:
            print("🔧 Admin mode enabled - development features available")
            capabilities = admin_manager.get_admin_capabilities()
            print(f"Available capabilities: {{', '.join(capabilities.keys())}}")

            # Try to import development tools
            try:
                from arcanum.dev import ArcanumDevelopmentSuite
                dev_suite = ArcanumDevelopmentSuite()
                print("Development tools available:")
                env_check = dev_suite.check_development_environment()
                for tool, available in env_check["tools"].items():
                    status = "✓" if available else "✗"
                    print(f"  {{status}} {{tool}}")
            except ImportError:
                print("Development tools not bundled with this export")
        else:
            print("Failed to enable admin mode - invalid credentials")
            return 1

        # Run the application with admin mode
        return run_app(str(project_path), renderer="qt")

    except Exception as e:
        print(f"Error starting application: {{e}}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
'''

        # Write admin launcher
        admin_launcher_path = output_path / "admin_launcher.py"
        with open(admin_launcher_path, 'w', encoding='utf-8') as f:
            f.write(admin_launcher_content)

        # Make executable on Unix systems
        if hasattr(os, 'chmod'):
            os.chmod(admin_launcher_path, 0o755)

        return admin_launcher_path

    def _create_spec_file(self, project_path: Path, output_path: Path, launcher_script: Path) -> Path:
        """Create PyInstaller spec file."""
        
        # Get platform-specific settings
        platform_config = self.config.platform_config
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path.cwd() / "src"))

block_cipher = None

a = Analysis(
    ['{launcher_script.name}'],
    pathex=['{output_path}'],
    binaries=[],
    datas=[
        ('project', 'project'),
        ('src/arcanum/runtime', 'src/arcanum/runtime'),
        ('src/arcanum/core', 'src/arcanum/core'),
        ('src/arcanum/renderers', 'src/arcanum/renderers'),
        ('src/arcanum/schemas', 'src/arcanum/schemas'),
        ('src/arcanum/widgets', 'src/arcanum/widgets'),
        ('src/arcanum/database', 'src/arcanum/database'),
    ],
    hiddenimports=[
        'arcanum.runtime',
        'arcanum.core.project',
        'arcanum.core.config',
        'arcanum.core.registry',
        'arcanum.core.layout',
        'arcanum.renderers.qt_renderer',
        'arcanum.schemas.config',
        'arcanum.schemas.layout',
        'arcanum.schemas.widgets',
        'PyQt5.QtCore',
        'PyQt5.QtWidgets',
        'PyQt5.QtGui',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{self.config.project_name}',
    debug={platform_config.get('debug', False)},
    bootloader_ignore_signals=False,
    strip=False,
    upx={platform_config.get('upx', True)},
    upx_exclude=[],
    runtime_tmpdir=None,
    console={platform_config.get('console', False)},
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='{platform_config.get('icon', '')}',
)
'''
        
        spec_path = output_path / f"{self.config.project_name}.spec"
        with open(spec_path, "w", encoding="utf-8") as f:
            f.write(spec_content)
        
        return spec_path
    
    def _run_pyinstaller(self, spec_file: Path, output_path: Path) -> bool:
        """Run PyInstaller build."""
        command = f"pyinstaller --clean --noconfirm {spec_file.name}"
        return self.run_build_commands([command], output_path)
    
    def _copy_additional_files(self, project_path: Path, output_path: Path):
        """Copy additional project files."""
        # Copy project files
        project_dest = output_path / "project"
        if project_dest.exists():
            shutil.rmtree(project_dest)
        shutil.copytree(project_path, project_dest)
        
        # Copy src directory
        src_path = Path(__file__).parent.parent.parent / "src"
        src_dest = output_path / "src"
        if src_dest.exists():
            shutil.rmtree(src_dest)
        shutil.copytree(src_path, src_dest)


class ElectronExporter(DesktopExporter):
    """Exporter using Electron for desktop applications."""
    
    def validate_requirements(self) -> List[str]:
        """Validate Electron requirements."""
        missing = super().validate_requirements()
        
        # Check for Node.js and npm
        if not shutil.which("node"):
            missing.append("Node.js")
        if not shutil.which("npm"):
            missing.append("npm")
        
        return missing
    
    def export(self, project_path: Path) -> ExportResult:
        """Export project as Electron desktop application."""
        start_time = time.time()
        result = ExportResult(
            success=False,
            target=self.config.target,
            output_path=self.config.output_path,
            build_time=0
        )
        
        try:
            # Validate requirements
            missing = self.validate_requirements()
            if missing:
                raise ExportValidationError(
                    f"Missing requirements: {', '.join(missing)}",
                    self.config.target
                )
            
            # Prepare output directory
            output_path = self.prepare_output_directory()
            
            # Create Electron project structure
            self._create_electron_project(project_path, output_path)
            
            # Install dependencies
            if not self.run_build_commands(["npm install"], output_path):
                raise ExportBuildError("Failed to install dependencies", self.config.target)
            
            # Build Electron app
            if not self.run_build_commands(["npm run build"], output_path):
                raise ExportBuildError("Failed to build Electron app", self.config.target)
            
            # Calculate build info
            build_time = time.time() - start_time
            dist_dir = output_path / "dist"
            file_size = self.calculate_build_size(dist_dir) if dist_dir.exists() else 0
            
            result.success = True
            result.build_time = build_time
            result.file_size = file_size
            result.artifacts = list(dist_dir.rglob("*")) if dist_dir.exists() else []
            result.build_info = self.create_build_info(project_path)
            result.logs.append(f"Electron export completed in {build_time:.2f}s")
            
        except Exception as e:
            result.errors.append(str(e))
            result.build_time = time.time() - start_time
            self.logger.error(f"Electron export failed: {e}")
        
        return result
    
    def _create_electron_project(self, project_path: Path, output_path: Path):
        """Create Electron project structure."""
        
        # Create package.json
        package_json = {
            "name": self.config.project_name.lower().replace(" ", "-"),
            "version": self.config.version,
            "description": f"Desktop application for {self.config.project_name}",
            "main": "main.js",
            "scripts": {
                "start": "electron .",
                "build": "electron-builder",
                "dist": "electron-builder --publish=never"
            },
            "devDependencies": {
                "electron": "^22.0.0",
                "electron-builder": "^23.6.0"
            },
            "build": {
                "appId": f"com.arcanum.{self.config.project_name.lower().replace(' ', '')}",
                "productName": self.config.project_name,
                "directories": {
                    "output": "dist"
                },
                "files": [
                    "main.js",
                    "renderer.js",
                    "index.html",
                    "styles/",
                    "project/"
                ]
            }
        }
        
        with open(output_path / "package.json", "w") as f:
            json.dump(package_json, f, indent=2)
        
        # Create main.js (Electron main process)
        main_js = f'''const {{ app, BrowserWindow }} = require('electron');
const path = require('path');

function createWindow() {{
    const mainWindow = new BrowserWindow({{
        width: 1200,
        height: 800,
        webPreferences: {{
            nodeIntegration: true,
            contextIsolation: false
        }},
        title: '{self.config.project_name}',
        icon: path.join(__dirname, 'assets/icon.png')
    }});

    mainWindow.loadFile('index.html');
    
    // Open DevTools in development
    if (process.env.NODE_ENV === 'development') {{
        mainWindow.webContents.openDevTools();
    }}
}}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {{
    if (process.platform !== 'darwin') {{
        app.quit();
    }}
}});

app.on('activate', () => {{
    if (BrowserWindow.getAllWindows().length === 0) {{
        createWindow();
    }}
}});
'''
        
        with open(output_path / "main.js", "w") as f:
            f.write(main_js)
        
        # Create index.html
        html_content = f'''<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{self.config.project_name}</title>
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div id="app">
        <h1>{self.config.project_name}</h1>
        <p>Desktop application powered by Electron and Arcanum</p>
    </div>
    <script src="renderer.js"></script>
</body>
</html>'''
        
        with open(output_path / "index.html", "w") as f:
            f.write(html_content)
        
        # Create renderer.js (Electron renderer process)
        renderer_js = '''// Electron renderer process
console.log('Arcanum Electron app loaded');

// Load project configuration and render UI
document.addEventListener('DOMContentLoaded', () => {
    // This would load the actual Arcanum project and render it
    console.log('DOM loaded, initializing Arcanum app...');
});
'''
        
        with open(output_path / "renderer.js", "w") as f:
            f.write(renderer_js)
        
        # Create styles directory
        styles_dir = output_path / "styles"
        styles_dir.mkdir(exist_ok=True)
        
        # Create main.css
        css_content = '''body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}

#app {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
    color: #333;
    text-align: center;
    margin-bottom: 20px;
}

p {
    color: #666;
    text-align: center;
    font-size: 16px;
}
'''
        
        with open(styles_dir / "main.css", "w") as f:
            f.write(css_content)
        
        # Copy project files
        project_dest = output_path / "project"
        if project_dest.exists():
            shutil.rmtree(project_dest)
        shutil.copytree(project_path, project_dest)


class TauriExporter(DesktopExporter):
    """Exporter using Tauri for desktop applications."""
    
    def validate_requirements(self) -> List[str]:
        """Validate Tauri requirements."""
        missing = super().validate_requirements()
        
        # Check for Rust and Cargo
        if not shutil.which("cargo"):
            missing.append("Rust/Cargo")
        if not shutil.which("node"):
            missing.append("Node.js")
        
        return missing
    
    def export(self, project_path: Path) -> ExportResult:
        """Export project as Tauri desktop application."""
        # TODO: Implement Tauri export
        raise NotImplementedError("Tauri export not yet implemented")

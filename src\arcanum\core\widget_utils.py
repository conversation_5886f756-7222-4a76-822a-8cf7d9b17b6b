"""
Utility functions for widget management, validation, and auto-generation.
"""

import re
import uuid
from typing import Dict, Any, List, Optional, Set
from ..schemas.widgets import BaseWidget, WidgetPosition


def generate_widget_id(widget_type: str, existing_ids: Set[str] = None) -> str:
    """
    Generate a unique widget ID based on widget type.
    
    Args:
        widget_type: The type of widget (e.g., 'button', 'text_input')
        existing_ids: Set of existing widget IDs to avoid conflicts
        
    Returns:
        Unique widget ID
    """
    if existing_ids is None:
        existing_ids = set()
    
    # Clean widget type for ID
    clean_type = re.sub(r'[^a-zA-Z0-9_]', '_', widget_type.lower())
    
    # Try simple ID first
    base_id = clean_type
    if base_id not in existing_ids:
        return base_id
    
    # Add counter if needed
    counter = 1
    while f"{base_id}_{counter}" in existing_ids:
        counter += 1
    
    return f"{base_id}_{counter}"


def auto_generate_widget_id(widget_data: Dict[str, Any], existing_ids: Set[str] = None) -> str:
    """
    Auto-generate widget ID if not provided.
    
    Args:
        widget_data: Widget data dictionary
        existing_ids: Set of existing widget IDs
        
    Returns:
        Generated or existing widget ID
    """
    if widget_data.get('id'):
        return widget_data['id']
    
    widget_type = widget_data.get('widget', 'widget')
    return generate_widget_id(widget_type, existing_ids)


def ensure_widget_position(widget_data: Dict[str, Any], default_x: int = 50, default_y: int = 50) -> Dict[str, Any]:
    """
    Ensure widget has position data, adding defaults if missing.
    
    Args:
        widget_data: Widget data dictionary
        default_x: Default x position
        default_y: Default y position
        
    Returns:
        Widget data with position information
    """
    if 'position' not in widget_data:
        widget_data['position'] = {}
    
    position = widget_data['position']
    
    # Set defaults for missing position fields
    defaults = {
        'x': default_x,
        'y': default_y,
        'width': get_default_width(widget_data.get('widget', 'text')),
        'height': get_default_height(widget_data.get('widget', 'text')),
        'snap_to_grid': True,
        'locked': False,
        'resizable': True,
        'z_index': 10
    }
    
    for key, default_value in defaults.items():
        if key not in position:
            position[key] = default_value
    
    return widget_data


def get_default_width(widget_type: str) -> int:
    """Get default width for widget type."""
    width_map = {
        'button': 120,
        'text': 200,
        'textarea': 300,
        'label': 150,
        'checkbox': 150,
        'dropdown': 180,
        'slider': 200,
        'number': 120,
        'date': 150,
        'color': 100,
        'file_upload': 200,
    }
    return width_map.get(widget_type, 200)


def get_default_height(widget_type: str) -> int:
    """Get default height for widget type."""
    height_map = {
        'button': 35,
        'text': 35,
        'textarea': 80,
        'label': 25,
        'checkbox': 25,
        'dropdown': 35,
        'slider': 35,
        'number': 35,
        'date': 35,
        'color': 35,
        'file_upload': 35,
    }
    return height_map.get(widget_type, 35)


def validate_widget_requirements(widget_data: Dict[str, Any]) -> List[str]:
    """
    Validate widget for required fields and common issues.

    Args:
        widget_data: Widget data to validate

    Returns:
        List of validation warnings/errors
    """
    warnings = []
    widget_type = widget_data.get('widget', '')

    # Check for missing ID (now required)
    if not widget_data.get('id'):
        warnings.append("Widget missing required 'id' field - please provide a unique identifier")

    # Check for missing label on input widgets
    input_widgets = ['text', 'textarea', 'number', 'dropdown', 'checkbox', 'slider', 'date', 'color']
    if widget_type in input_widgets and not widget_data.get('label'):
        warnings.append(f"Input widget '{widget_type}' missing 'label' field for user display")

    # Check for missing text on label widgets
    if widget_type == 'label' and not widget_data.get('text'):
        warnings.append("Label widget missing 'text' field - what should be displayed?")

    # Check for missing action on button widgets
    if widget_type == 'button' and not widget_data.get('action'):
        warnings.append("Button widget missing 'action' field - what should happen when clicked?")

    # Check for missing binds_to on input widgets
    data_widgets = ['text', 'textarea', 'number', 'dropdown', 'checkbox', 'slider', 'date', 'color']
    if widget_type in data_widgets and not widget_data.get('binds_to'):
        warnings.append(f"Data widget '{widget_type}' missing 'binds_to' field - where should data be stored?")

    # Check for missing src on image widgets
    if widget_type == 'image' and not widget_data.get('src'):
        warnings.append("Image widget missing 'src' field - which image should be displayed?")

    # Check for missing options on dropdown widgets
    if widget_type == 'dropdown' and not widget_data.get('options'):
        warnings.append("Dropdown widget missing 'options' field - what choices should be available?")

    return warnings


def strip_gui_metadata(widget_data: Dict[str, Any], keep_position: bool = False) -> Dict[str, Any]:
    """
    Strip GUI-only metadata from widget data for export.
    
    Args:
        widget_data: Widget data to clean
        keep_position: Whether to keep position data
        
    Returns:
        Cleaned widget data
    """
    cleaned = widget_data.copy()
    
    # Remove GUI-only fields
    gui_fields = ['_minimized', '_selected', '_hover']
    for field in gui_fields:
        cleaned.pop(field, None)
    
    # Optionally remove position data
    if not keep_position:
        cleaned.pop('position', None)
    
    return cleaned


def snap_to_grid(x: int, y: int, grid_size: int = 10) -> tuple[int, int]:
    """
    Snap coordinates to grid.
    
    Args:
        x: X coordinate
        y: Y coordinate
        grid_size: Grid size in pixels
        
    Returns:
        Snapped coordinates
    """
    snapped_x = round(x / grid_size) * grid_size
    snapped_y = round(y / grid_size) * grid_size
    return snapped_x, snapped_y


def get_widget_bounds(widget_data: Dict[str, Any]) -> tuple[int, int, int, int]:
    """
    Get widget bounding box (x, y, width, height).
    
    Args:
        widget_data: Widget data with position
        
    Returns:
        Tuple of (x, y, width, height)
    """
    position = widget_data.get('position', {})
    x = position.get('x', 0)
    y = position.get('y', 0)
    width = position.get('width', 200)
    height = position.get('height', 35)
    return x, y, width, height


def check_widget_overlap(widget1: Dict[str, Any], widget2: Dict[str, Any]) -> bool:
    """
    Check if two widgets overlap.
    
    Args:
        widget1: First widget data
        widget2: Second widget data
        
    Returns:
        True if widgets overlap
    """
    x1, y1, w1, h1 = get_widget_bounds(widget1)
    x2, y2, w2, h2 = get_widget_bounds(widget2)
    
    # Check for overlap
    return not (x1 + w1 <= x2 or x2 + w2 <= x1 or y1 + h1 <= y2 or y2 + h2 <= y1)


def find_free_position(existing_widgets: List[Dict[str, Any]], 
                      width: int = 200, height: int = 35,
                      start_x: int = 50, start_y: int = 50,
                      grid_size: int = 10) -> tuple[int, int]:
    """
    Find a free position for a new widget.
    
    Args:
        existing_widgets: List of existing widgets
        width: Width of new widget
        height: Height of new widget
        start_x: Starting x position
        start_y: Starting y position
        grid_size: Grid size for snapping
        
    Returns:
        Free position (x, y)
    """
    x, y = start_x, start_y
    
    # Try positions in a grid pattern
    for row in range(20):  # Try up to 20 rows
        for col in range(10):  # Try up to 10 columns
            test_x = start_x + (col * (width + grid_size))
            test_y = start_y + (row * (height + grid_size))
            
            # Snap to grid
            test_x, test_y = snap_to_grid(test_x, test_y, grid_size)
            
            # Create test widget
            test_widget = {
                'position': {
                    'x': test_x,
                    'y': test_y,
                    'width': width,
                    'height': height
                }
            }
            
            # Check for overlaps
            overlaps = any(check_widget_overlap(test_widget, widget) for widget in existing_widgets)
            
            if not overlaps:
                return test_x, test_y
    
    # Fallback to original position if no free space found
    return snap_to_grid(start_x, start_y, grid_size)

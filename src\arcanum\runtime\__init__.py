"""
Arcanum Runtime - Minimal runtime engine for exported applications.

This module contains only the essential components needed to run exported
Arcanum applications. It excludes development tools like the GUI builder,
logic editor, and export system.

Runtime Components:
- Configuration loading and validation
- Widget registry and layout engine
- Rendering backends (PyQt, Web)
- Database integration
- Schema validation

Development tools are located in arcanum.dev and are not included in exports.
"""

__version__ = "1.0.1"

from pathlib import Path

# Core runtime imports
from ..core.config import ArcanumConfig
from ..core.project import ArcanumProject
from ..core.registry import WidgetRegistry
from ..core.layout import LayoutEngine

# Rendering
from ..renderers.base import BaseRenderer, RenderContext
try:
    from ..renderers.qt_renderer import QtRenderer
    QT_AVAILABLE = True
except ImportError:
    QT_AVAILABLE = False

# Database (if needed)
try:
    from ..database.manager import DatabaseManager
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False

# Schemas
from ..schemas.config import ArcanumConfigSchema
from ..schemas.layout import PageLayout, BaseLayout
from ..schemas.widgets import BaseWidget, WidgetDefinition

# Admin mode
from .admin import AdminModeManager, check_admin_mode

__all__ = [
    # Core
    "ArcanumConfig",
    "ArcanumProject", 
    "WidgetRegistry",
    "LayoutEngine",
    
    # Rendering
    "BaseRenderer",
    "RenderContext",
    
    # Schemas
    "ArcanumConfigSchema",
    "PageLayout",
    "BaseLayout",
    "BaseWidget",
    "WidgetDefinition",
    
    # Admin mode
    "AdminModeManager",
    "check_admin_mode",

    # Version
    "__version__",
]

# Conditional exports
if QT_AVAILABLE:
    __all__.append("QtRenderer")

if DATABASE_AVAILABLE:
    __all__.append("DatabaseManager")


class ArcanumRuntime:
    """
    Main runtime class for exported Arcanum applications.
    
    This provides a simplified interface for running Arcanum apps
    without the full development environment.
    """
    
    def __init__(self, project_path: str):
        """
        Initialize the runtime with a project path.

        Args:
            project_path: Path to the Arcanum project directory.
        """
        self.project_path = Path(project_path)
        self.project = None
        self.config = None
        self.renderer = None
        self.admin_manager = check_admin_mode(project_path)
        
    def load_project(self) -> ArcanumConfig:
        """Load and validate the project configuration."""
        self.project = ArcanumProject(self.project_path)
        self.config = self.project.load()
        return self.config
        
    def create_renderer(self, renderer_type: str = "qt") -> BaseRenderer:
        """
        Create a renderer for the specified type.
        
        Args:
            renderer_type: Type of renderer ("qt", "web", etc.)
            
        Returns:
            Configured renderer instance.
        """
        if renderer_type == "qt":
            if not QT_AVAILABLE:
                raise ImportError("PyQt5 not available for Qt rendering")
            self.renderer = QtRenderer()
        else:
            raise ValueError(f"Unsupported renderer type: {renderer_type}")
            
        return self.renderer
        
    def run(self, renderer_type: str = "qt", page: str = None) -> int:
        """
        Run the application with the specified renderer.
        
        Args:
            renderer_type: Type of renderer to use.
            page: Specific page to load (uses start_layout if None).
            
        Returns:
            Exit code.
        """
        try:
            # Check admin mode
            if self.admin_manager.is_admin_mode_enabled():
                print("🔧 Admin mode enabled - development features available")
                capabilities = self.admin_manager.get_admin_capabilities()
                if capabilities:
                    print(f"Available capabilities: {', '.join(capabilities.keys())}")

            # Load project
            config = self.load_project()

            # Create renderer
            renderer = self.create_renderer(renderer_type)
            renderer.initialize()

            # Set template variables
            renderer.context.set_variable("app_name", config.app_name)
            renderer.context.set_variable("version", config.version)
            renderer.context.set_variable("description", config.description)

            # Determine page to load
            if page is None:
                page = config.start_layout

            # Load and render page
            page_path = self.project_path / "pages" / f"{page}.yaml"
            if not page_path.exists():
                page_path = self.project_path / "pages" / page

            if page_path.exists():
                # Load the page layout
                page_layout = renderer.layout_engine.load_page(page_path)
                renderer.render_page(page_layout)
                renderer.show()
                return renderer.run()
            else:
                print(f"Error: Page not found: {page}")
                return 1
                
        except Exception as e:
            print(f"Runtime error: {e}")
            import traceback
            traceback.print_exc()
            return 1


# Convenience function for simple app launching
def run_app(project_path: str, renderer: str = "qt", page: str = None) -> int:
    """
    Convenience function to run an Arcanum application.
    
    Args:
        project_path: Path to the project directory.
        renderer: Renderer type to use.
        page: Page to load.
        
    Returns:
        Exit code.
    """
    runtime = ArcanumRuntime(project_path)
    return runtime.run(renderer, page)

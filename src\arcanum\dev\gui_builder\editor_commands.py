"""
Professional editor commands and undo/redo system for Arcanum GUI Builder.
"""

from typing import Any, Dict, List, Optional, Union
from abc import ABC, abstractmethod
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtWidgets import <PERSON><PERSON><PERSON><PERSON>om<PERSON>, QUndoStack
import copy


class EditorCommand(QUndoCommand):
    """Base class for all editor commands that support undo/redo."""
    
    def __init__(self, description: str, parent=None):
        super().__init__(description, parent)
        self.editor = None
        
    def set_editor(self, editor):
        """Set the editor reference for command execution."""
        self.editor = editor


class AddWidgetCommand(EditorCommand):
    """Command for adding a widget to the canvas."""
    
    def __init__(self, widget_data: Dict[str, Any], position: tuple, parent=None):
        super().__init__(f"Add {widget_data.get('widget', 'Widget')}", parent)
        self.widget_data = copy.deepcopy(widget_data)
        self.position = position
        self.widget_id = None
        
    def redo(self):
        """Execute the add widget command."""
        if self.editor and self.editor.design_canvas:
            # Add widget to canvas
            item = self.editor.design_canvas.add_widget_from_data(
                self.widget_data, self.position
            )
            if item:
                self.widget_id = item.item_data.get('id')
                
    def undo(self):
        """Undo the add widget command."""
        if self.editor and self.editor.design_canvas and self.widget_id:
            # Remove widget from canvas
            self.editor.design_canvas.remove_widget_by_id(self.widget_id)


class DeleteWidgetCommand(EditorCommand):
    """Command for deleting a widget from the canvas."""
    
    def __init__(self, widget_data: Dict[str, Any], parent=None):
        super().__init__(f"Delete {widget_data.get('id', 'Widget')}", parent)
        self.widget_data = copy.deepcopy(widget_data)
        self.widget_id = widget_data.get('id')
        self.position = (
            widget_data.get('position', {}).get('x', 0),
            widget_data.get('position', {}).get('y', 0)
        )
        
    def redo(self):
        """Execute the delete widget command."""
        if self.editor and self.editor.design_canvas and self.widget_id:
            self.editor.design_canvas.remove_widget_by_id(self.widget_id)
            
    def undo(self):
        """Undo the delete widget command."""
        if self.editor and self.editor.design_canvas:
            self.editor.design_canvas.add_widget_from_data(
                self.widget_data, self.position
            )


class MoveWidgetCommand(EditorCommand):
    """Command for moving a widget on the canvas."""
    
    def __init__(self, widget_id: str, old_pos: tuple, new_pos: tuple, parent=None):
        super().__init__(f"Move {widget_id}", parent)
        self.widget_id = widget_id
        self.old_position = old_pos
        self.new_position = new_pos
        
    def redo(self):
        """Execute the move widget command."""
        if self.editor and self.editor.design_canvas:
            self.editor.design_canvas.move_widget_to_position(
                self.widget_id, self.new_position
            )
            
    def undo(self):
        """Undo the move widget command."""
        if self.editor and self.editor.design_canvas:
            self.editor.design_canvas.move_widget_to_position(
                self.widget_id, self.old_position
            )


class ResizeWidgetCommand(EditorCommand):
    """Command for resizing a widget on the canvas."""
    
    def __init__(self, widget_id: str, old_size: tuple, new_size: tuple, parent=None):
        super().__init__(f"Resize {widget_id}", parent)
        self.widget_id = widget_id
        self.old_size = old_size
        self.new_size = new_size
        
    def redo(self):
        """Execute the resize widget command."""
        if self.editor and self.editor.design_canvas:
            self.editor.design_canvas.resize_widget_to_size(
                self.widget_id, self.new_size
            )
            
    def undo(self):
        """Undo the resize widget command."""
        if self.editor and self.editor.design_canvas:
            self.editor.design_canvas.resize_widget_to_size(
                self.widget_id, self.old_size
            )


class PropertyChangeCommand(EditorCommand):
    """Command for changing widget properties."""
    
    def __init__(self, widget_id: str, property_path: str, old_value: Any, new_value: Any, parent=None):
        super().__init__(f"Change {property_path} of {widget_id}", parent)
        self.widget_id = widget_id
        self.property_path = property_path
        self.old_value = copy.deepcopy(old_value)
        self.new_value = copy.deepcopy(new_value)
        
    def redo(self):
        """Execute the property change command."""
        if self.editor and self.editor.design_canvas:
            self.editor.design_canvas.set_widget_property(
                self.widget_id, self.property_path, self.new_value
            )
            
    def undo(self):
        """Undo the property change command."""
        if self.editor and self.editor.design_canvas:
            self.editor.design_canvas.set_widget_property(
                self.widget_id, self.property_path, self.old_value
            )


class EditorCommandManager(QObject):
    """Manages editor commands and provides undo/redo functionality."""
    
    # Signals
    can_undo_changed = pyqtSignal(bool)
    can_redo_changed = pyqtSignal(bool)
    undo_text_changed = pyqtSignal(str)
    redo_text_changed = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.undo_stack = QUndoStack(self)
        self.editor = None
        
        # Connect signals
        self.undo_stack.canUndoChanged.connect(self.can_undo_changed)
        self.undo_stack.canRedoChanged.connect(self.can_redo_changed)
        self.undo_stack.undoTextChanged.connect(self.undo_text_changed)
        self.undo_stack.redoTextChanged.connect(self.redo_text_changed)
        
    def set_editor(self, editor):
        """Set the editor reference for command execution."""
        self.editor = editor
        
    def execute_command(self, command: EditorCommand):
        """Execute a command and add it to the undo stack."""
        command.set_editor(self.editor)
        self.undo_stack.push(command)
        
    def undo(self):
        """Undo the last command."""
        self.undo_stack.undo()
        
    def redo(self):
        """Redo the last undone command."""
        self.undo_stack.redo()
        
    def clear(self):
        """Clear the undo stack."""
        self.undo_stack.clear()
        
    def can_undo(self) -> bool:
        """Check if undo is available."""
        return self.undo_stack.canUndo()
        
    def can_redo(self) -> bool:
        """Check if redo is available."""
        return self.undo_stack.canRedo()
        
    def undo_text(self) -> str:
        """Get the text for the undo action."""
        return self.undo_stack.undoText()
        
    def redo_text(self) -> str:
        """Get the text for the redo action."""
        return self.undo_stack.redoText()


class ClipboardManager(QObject):
    """Manages copy/paste operations for widgets."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.clipboard_data: List[Dict[str, Any]] = []
        
    def copy_widgets(self, widget_data_list: List[Dict[str, Any]]):
        """Copy widgets to clipboard."""
        self.clipboard_data = [copy.deepcopy(data) for data in widget_data_list]
        
    def paste_widgets(self, offset: tuple = (20, 20)) -> List[Dict[str, Any]]:
        """Paste widgets from clipboard with optional offset."""
        if not self.clipboard_data:
            return []
            
        pasted_widgets = []
        for widget_data in self.clipboard_data:
            # Create a copy and modify position
            new_widget = copy.deepcopy(widget_data)
            
            # Update position if it exists
            if 'position' in new_widget:
                new_widget['position']['x'] += offset[0]
                new_widget['position']['y'] += offset[1]
                
            # Generate new ID to avoid conflicts
            if 'id' in new_widget:
                base_id = new_widget['id']
                new_widget['id'] = f"{base_id}_copy"
                
            pasted_widgets.append(new_widget)
            
        return pasted_widgets
        
    def has_clipboard_data(self) -> bool:
        """Check if clipboard has data."""
        return len(self.clipboard_data) > 0
        
    def clear_clipboard(self):
        """Clear clipboard data."""
        self.clipboard_data.clear()

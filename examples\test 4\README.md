# test 4

This is an Arcanum application project built with the modular, schema-driven, offline-first application builder.

## Quick Start

```bash
# Validate your project
arcanum validate

# Run in development mode
arcanum run

# Launch the visual GUI builder
arcanum builder

# Launch the logic graph editor
arcanum logic

# Export your application
arcanum export --target qt
```

## Development Workflow

1. **Design Layouts**: Edit YAML files in `pages/` or use the visual GUI builder
2. **Create Logic**: Use the visual logic graph editor for event handling
3. **Customize UI**: Add custom widgets in `ui/widgets/` and themes in `ui/themes/`
4. **Test & Validate**: Run `arcanum validate` to check your configuration
5. **Export**: Create standalone applications with `arcanum export`

## Project Structure

- `Arcanum.yaml` - Main configuration file with export settings
- `pages/` - Layout definitions (YAML format)
- `ui/` - UI components, themes, and assets
  - `widgets/` - Custom widget definitions
  - `themes/` - Theme configurations
  - `layouts/` - Reusable layout components
- `schemas/` - Data validation schemas
- `user_data/` - Application data and database
- `plugins/` - Local plugins and extensions
- `docs/` - Project documentation

## Runtime vs Development

Arcanum separates runtime and development concerns:

- **Runtime**: Minimal components needed for exported applications
- **Development**: Full toolset including GUI builder, logic editor, and export system

When you export your application, only the runtime components are bundled for optimal performance.

## Export Options

- **Desktop (Qt)**: Standalone executable with PyInstaller
- **Web (Static)**: HTML/CSS/JS for web deployment
- **Web (React)**: Modern React application
- **Admin Mode**: Include development tools in exports for debugging

## Admin Mode

Exported applications can include admin mode for debugging:

```bash
# Enable admin mode with default password
python admin_launcher.py

# Or set environment variable
export ARCANUM_ADMIN_TOKEN=your_token
python launcher.py
```

Default admin password: `arcanum_dev_2024`

## Commands Reference

### Development
- `arcanum init <name>` - Create new project
- `arcanum validate` - Validate project configuration
- `arcanum run` - Run application in development mode
- `arcanum builder` - Launch visual GUI builder
- `arcanum logic` - Launch logic graph editor

### Export
- `arcanum export --target qt` - Export desktop application
- `arcanum export --target html` - Export static web application
- `arcanum export-list` - List available export targets
- `arcanum export-all` - Export to all configured targets

### Plugins
- `arcanum plugin list` - List installed plugins
- `arcanum plugin install <name>` - Install plugin from registry
- `arcanum plugin create <name>` - Create new plugin template

## Learn More

- [Arcanum Documentation](https://arcanum.dev/docs)
- [Widget Reference](https://arcanum.dev/widgets)
- [Logic Graph Guide](https://arcanum.dev/logic)
- [Export Guide](https://arcanum.dev/export)

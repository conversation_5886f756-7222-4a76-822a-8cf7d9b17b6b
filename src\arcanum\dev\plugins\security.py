"""
Plugin security and validation system for Arcanum.
"""

import ast
import hashlib
import os
import re
from typing import Dict, List, Optional, Set, Any
from pathlib import Path
import logging
import zipfile
import tarfile

from .manifest import PluginManifest, PluginPermission

logger = logging.getLogger(__name__)


class SecurityViolation(Exception):
    """Raised when a security violation is detected."""
    pass


class PluginSecurity:
    """Handles plugin security validation and sandboxing."""
    
    # Dangerous Python modules and functions
    DANGEROUS_MODULES = {
        'os', 'sys', 'subprocess', 'shutil', 'tempfile',
        'socket', 'urllib', 'requests', 'http',
        'eval', 'exec', 'compile', '__import__',
        'open', 'file', 'input', 'raw_input'
    }
    
    # Dangerous file patterns
    DANGEROUS_PATTERNS = [
        r'\.\./',  # Directory traversal
        r'__pycache__',  # Python cache
        r'\.pyc$',  # Compiled Python
        r'\.pyo$',  # Optimized Python
        r'\.exe$',  # Executables
        r'\.dll$',  # Dynamic libraries
        r'\.so$',   # Shared objects
        r'\.dylib$',  # macOS libraries
    ]
    
    # Maximum file sizes (in bytes)
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    MAX_TOTAL_SIZE = 50 * 1024 * 1024  # 50MB
    
    def __init__(self):
        """Initialize security system."""
        self.trusted_plugins: Set[str] = set()
        self.blocked_plugins: Set[str] = set()
    
    def validate_plugin(self, plugin_path: Path, manifest: PluginManifest) -> bool:
        """
        Validate plugin security.
        
        Args:
            plugin_path: Path to plugin directory.
            manifest: Plugin manifest.
            
        Returns:
            True if plugin is safe to install.
        """
        try:
            # Check if plugin is blocked
            if manifest.name in self.blocked_plugins:
                raise SecurityViolation(f"Plugin {manifest.name} is blocked")
            
            # Validate manifest
            self._validate_manifest(manifest)
            
            # Validate file structure
            self._validate_file_structure(plugin_path)
            
            # Validate Python code
            self._validate_python_code(plugin_path, manifest)
            
            # Check file sizes
            self._validate_file_sizes(plugin_path)
            
            logger.info(f"Plugin {manifest.name} passed security validation")
            return True
            
        except SecurityViolation as e:
            logger.error(f"Security violation in plugin {manifest.name}: {e}")
            return False
        except Exception as e:
            logger.error(f"Security validation failed for plugin {manifest.name}: {e}")
            return False
    
    def request_permissions(self, manifest: PluginManifest) -> bool:
        """
        Request user approval for plugin permissions.
        
        Args:
            manifest: Plugin manifest with permissions.
            
        Returns:
            True if permissions approved.
        """
        permissions = manifest.permissions
        
        if not permissions.requires_approval():
            return True
        
        # In a real implementation, this would show a UI dialog
        # For now, we'll log the permissions and auto-approve for development
        logger.warning(f"Plugin {manifest.name} requests permissions:")
        
        if permissions.filesystem:
            logger.warning("  - Filesystem access")
        if permissions.network:
            logger.warning("  - Network access")
        if permissions.database:
            logger.warning("  - Database access")
        if permissions.system:
            logger.warning("  - System access")
        if permissions.user_data:
            logger.warning("  - User data access")
        
        # TODO: Implement actual permission dialog
        # For development, auto-approve
        return True
    
    def sandbox_plugin(self, plugin_name: str) -> Dict[str, Any]:
        """
        Create sandbox environment for plugin.
        
        Args:
            plugin_name: Name of plugin to sandbox.
            
        Returns:
            Sandbox configuration.
        """
        # TODO: Implement plugin sandboxing
        return {
            "restricted_modules": list(self.DANGEROUS_MODULES),
            "allowed_paths": [],
            "network_allowed": False,
            "filesystem_readonly": True
        }
    
    def calculate_plugin_hash(self, plugin_path: Path) -> str:
        """
        Calculate hash of plugin files for integrity checking.
        
        Args:
            plugin_path: Path to plugin directory.
            
        Returns:
            SHA256 hash of plugin contents.
        """
        hasher = hashlib.sha256()
        
        for file_path in sorted(plugin_path.rglob("*")):
            if file_path.is_file():
                with open(file_path, 'rb') as f:
                    hasher.update(f.read())
        
        return hasher.hexdigest()
    
    def _validate_manifest(self, manifest: PluginManifest):
        """Validate plugin manifest for security issues."""
        # Check for suspicious entry points
        if manifest.entry_point:
            if any(dangerous in manifest.entry_point for dangerous in self.DANGEROUS_MODULES):
                raise SecurityViolation(f"Suspicious entry point: {manifest.entry_point}")
        
        # Check capabilities
        for capability in manifest.capabilities:
            if any(dangerous in capability.entry_point for dangerous in self.DANGEROUS_MODULES):
                raise SecurityViolation(f"Suspicious capability entry point: {capability.entry_point}")
        
        # Check dependencies
        for dep in manifest.python_dependencies:
            if any(dangerous in dep.lower() for dangerous in ['subprocess', 'os', 'sys']):
                logger.warning(f"Potentially dangerous dependency: {dep}")
    
    def _validate_file_structure(self, plugin_path: Path):
        """Validate plugin file structure."""
        total_size = 0
        file_count = 0
        
        for file_path in plugin_path.rglob("*"):
            if file_path.is_file():
                file_count += 1
                
                # Check file patterns
                relative_path = file_path.relative_to(plugin_path)
                for pattern in self.DANGEROUS_PATTERNS:
                    if re.search(pattern, str(relative_path)):
                        raise SecurityViolation(f"Dangerous file pattern: {relative_path}")
                
                # Check file size
                file_size = file_path.stat().st_size
                if file_size > self.MAX_FILE_SIZE:
                    raise SecurityViolation(f"File too large: {relative_path} ({file_size} bytes)")
                
                total_size += file_size
        
        # Check total size
        if total_size > self.MAX_TOTAL_SIZE:
            raise SecurityViolation(f"Plugin too large: {total_size} bytes")
        
        # Check file count (prevent zip bombs)
        if file_count > 1000:
            raise SecurityViolation(f"Too many files: {file_count}")
    
    def _validate_python_code(self, plugin_path: Path, manifest: PluginManifest):
        """Validate Python code for security issues."""
        for py_file in plugin_path.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Parse AST
                tree = ast.parse(content, filename=str(py_file))
                
                # Check for dangerous patterns
                self._check_ast_security(tree, py_file)
                
            except SyntaxError as e:
                raise SecurityViolation(f"Syntax error in {py_file}: {e}")
            except Exception as e:
                logger.warning(f"Failed to validate {py_file}: {e}")
    
    def _check_ast_security(self, tree: ast.AST, file_path: Path):
        """Check AST for security violations."""
        class SecurityVisitor(ast.NodeVisitor):
            def __init__(self, security_checker):
                self.security_checker = security_checker
                self.file_path = file_path
            
            def visit_Import(self, node):
                for alias in node.names:
                    if alias.name in self.security_checker.DANGEROUS_MODULES:
                        raise SecurityViolation(
                            f"Dangerous import in {self.file_path}: {alias.name}"
                        )
                self.generic_visit(node)
            
            def visit_ImportFrom(self, node):
                if node.module and node.module in self.security_checker.DANGEROUS_MODULES:
                    raise SecurityViolation(
                        f"Dangerous import in {self.file_path}: {node.module}"
                    )
                self.generic_visit(node)
            
            def visit_Call(self, node):
                # Check for dangerous function calls
                if isinstance(node.func, ast.Name):
                    if node.func.id in ['eval', 'exec', 'compile', '__import__']:
                        raise SecurityViolation(
                            f"Dangerous function call in {self.file_path}: {node.func.id}"
                        )
                self.generic_visit(node)
        
        visitor = SecurityVisitor(self)
        visitor.visit(tree)
    
    def _validate_file_sizes(self, plugin_path: Path):
        """Validate file sizes to prevent resource exhaustion."""
        total_size = 0
        
        for file_path in plugin_path.rglob("*"):
            if file_path.is_file():
                size = file_path.stat().st_size
                total_size += size
                
                if size > self.MAX_FILE_SIZE:
                    raise SecurityViolation(f"File too large: {file_path}")
        
        if total_size > self.MAX_TOTAL_SIZE:
            raise SecurityViolation(f"Plugin total size too large: {total_size}")


class PluginSandbox:
    """Sandbox environment for running plugins safely."""
    
    def __init__(self, plugin_name: str, permissions: PluginPermission):
        """
        Initialize plugin sandbox.
        
        Args:
            plugin_name: Name of the plugin.
            permissions: Plugin permissions.
        """
        self.plugin_name = plugin_name
        self.permissions = permissions
        self.restricted_globals = self._create_restricted_globals()
    
    def execute_plugin_code(self, code: str, globals_dict: Dict[str, Any] = None) -> Any:
        """
        Execute plugin code in sandbox.
        
        Args:
            code: Python code to execute.
            globals_dict: Global variables to provide.
            
        Returns:
            Execution result.
        """
        # Merge restricted globals with provided globals
        execution_globals = self.restricted_globals.copy()
        if globals_dict:
            execution_globals.update(globals_dict)
        
        # Execute in restricted environment
        try:
            return exec(code, execution_globals)
        except Exception as e:
            logger.error(f"Plugin {self.plugin_name} execution failed: {e}")
            raise
    
    def _create_restricted_globals(self) -> Dict[str, Any]:
        """Create restricted global namespace for plugin execution."""
        # Start with safe builtins
        safe_builtins = {
            'len', 'str', 'int', 'float', 'bool', 'list', 'dict', 'tuple', 'set',
            'min', 'max', 'sum', 'abs', 'round', 'sorted', 'reversed',
            'enumerate', 'zip', 'range', 'isinstance', 'hasattr', 'getattr',
            'print'  # Allow print for debugging
        }
        
        restricted_globals = {
            '__builtins__': {name: __builtins__[name] for name in safe_builtins if name in __builtins__}
        }
        
        # Add safe modules based on permissions
        if self.permissions.filesystem:
            # Allow limited filesystem access
            import os
            restricted_globals['os'] = self._create_restricted_os()
        
        if self.permissions.network:
            # Allow network access
            import requests
            restricted_globals['requests'] = requests
        
        return restricted_globals
    
    def _create_restricted_os(self):
        """Create restricted os module."""
        # TODO: Implement restricted os module
        # This would provide limited filesystem access
        class RestrictedOS:
            @staticmethod
            def listdir(path):
                # Only allow listing in plugin directory
                return []
        
        return RestrictedOS()

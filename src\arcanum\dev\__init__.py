"""
Arcanum Development Tools - G<PERSON> Builder, Logic Editor, Export System, and Plugins.

This module contains all development-time tools and is excluded from exported
applications to keep them lightweight.

Development Components:
- G<PERSON> Builder (visual application designer)
- Logic Graph Editor (visual programming)
- Export System (multi-platform deployment)
- Plugin System (extensibility framework)
- CLI Tools (command-line interface)

These tools are only available during development and are not included
in exported applications.
"""

# Development tool imports
try:
    from .gui_builder.builder import main as gui_builder_main
    GUI_BUILDER_AVAILABLE = True
except ImportError:
    GUI_BUILDER_AVAILABLE = False

try:
    from .logic.editor import LogicGraphEditor
    LOGIC_EDITOR_AVAILABLE = True
except ImportError:
    LOGIC_EDITOR_AVAILABLE = False

try:
    from .export.manager import ExportManager
    from .export.base import ExportTarget, ExportConfig, ExportResult
    EXPORT_SYSTEM_AVAILABLE = True
except ImportError:
    EXPORT_SYSTEM_AVAILABLE = False

try:
    from .plugins.manager import PluginManager
    from .plugins.registry import PluginRegistry
    PLUGIN_SYSTEM_AVAILABLE = True
except ImportError:
    PLUGIN_SYSTEM_AVAILABLE = False

__all__ = [
    # Availability flags
    "GUI_BUILDER_AVAILABLE",
    "LOGIC_EDITOR_AVAILABLE", 
    "EXPORT_SYSTEM_AVAILABLE",
    "PLUGIN_SYSTEM_AVAILABLE",
]

# Conditional exports
if GUI_BUILDER_AVAILABLE:
    __all__.extend(["gui_builder_main"])

if LOGIC_EDITOR_AVAILABLE:
    __all__.extend(["LogicGraphEditor"])

if EXPORT_SYSTEM_AVAILABLE:
    __all__.extend(["ExportManager", "ExportTarget", "ExportConfig", "ExportResult"])

if PLUGIN_SYSTEM_AVAILABLE:
    __all__.extend(["PluginManager", "PluginRegistry"])


class ArcanumDevelopmentSuite:
    """
    Main development suite providing access to all development tools.
    
    This class provides a unified interface to launch and manage
    development tools like the GUI builder, logic editor, etc.
    """
    
    def __init__(self):
        """Initialize the development suite."""
        self.check_dependencies()
        
    def check_dependencies(self) -> dict:
        """
        Check which development tools are available.
        
        Returns:
            Dictionary of tool availability.
        """
        return {
            "gui_builder": GUI_BUILDER_AVAILABLE,
            "logic_editor": LOGIC_EDITOR_AVAILABLE,
            "export_system": EXPORT_SYSTEM_AVAILABLE,
            "plugin_system": PLUGIN_SYSTEM_AVAILABLE,
        }
        
    def launch_gui_builder(self, project_path: str = None) -> int:
        """
        Launch the GUI Builder.
        
        Args:
            project_path: Optional project to open.
            
        Returns:
            Exit code.
        """
        if not GUI_BUILDER_AVAILABLE:
            raise ImportError("GUI Builder not available. Install PyQt5.")
            
        return gui_builder_main()
        
    def launch_logic_editor(self) -> int:
        """
        Launch the Logic Graph Editor.
        
        Returns:
            Exit code.
        """
        if not LOGIC_EDITOR_AVAILABLE:
            raise ImportError("Logic Editor not available. Install PyQt5.")
            
        import sys
        from PyQt5.QtWidgets import QApplication
        
        app = QApplication(sys.argv)
        app.setApplicationName("Arcanum Logic Editor")
        
        editor = LogicGraphEditor()
        editor.setWindowTitle("Arcanum Logic Graph Editor")
        editor.resize(1200, 800)
        editor.show()
        
        return app.exec_()
        
    def create_export_manager(self, project_path: str) -> 'ExportManager':
        """
        Create an export manager for the specified project.
        
        Args:
            project_path: Path to the project directory.
            
        Returns:
            Export manager instance.
        """
        if not EXPORT_SYSTEM_AVAILABLE:
            raise ImportError("Export System not available.")
            
        return ExportManager(project_path)
        
    def create_plugin_manager(self, project_path: str = None) -> 'PluginManager':
        """
        Create a plugin manager.
        
        Args:
            project_path: Optional project path for project-specific plugins.
            
        Returns:
            Plugin manager instance.
        """
        if not PLUGIN_SYSTEM_AVAILABLE:
            raise ImportError("Plugin System not available.")
            
        return PluginManager(project_path)


# Convenience functions
def launch_development_suite() -> ArcanumDevelopmentSuite:
    """
    Launch the Arcanum Development Suite.
    
    Returns:
        Development suite instance.
    """
    return ArcanumDevelopmentSuite()


def check_development_environment() -> dict:
    """
    Check the development environment and return status.
    
    Returns:
        Dictionary with environment status.
    """
    suite = ArcanumDevelopmentSuite()
    dependencies = suite.check_dependencies()
    
    # Check PyQt5 specifically
    try:
        import PyQt5
        pyqt_version = PyQt5.QtCore.QT_VERSION_STR
        pyqt_available = True
    except ImportError:
        pyqt_version = None
        pyqt_available = False
    
    return {
        "tools": dependencies,
        "pyqt5": {
            "available": pyqt_available,
            "version": pyqt_version
        },
        "ready_for_development": all(dependencies.values()) and pyqt_available
    }

"""
Plugin manifest schema and validation for Arcanum plugins.
"""

from typing import Dict, List, Optional, Any, Literal
from pydantic import BaseModel, Field, validator
from pathlib import Path
from datetime import datetime
import re


class PluginDependency(BaseModel):
    """Represents a plugin dependency."""
    name: str
    version: str = "*"
    optional: bool = False
    source: Optional[str] = None  # GitHub URL or local path
    
    @validator("name")
    def validate_name(cls, v):
        """Validate plugin name format."""
        if not re.match(r"^[a-zA-Z0-9_-]+$", v):
            raise ValueError("Plugin name must contain only letters, numbers, underscores, and hyphens")
        return v


class PluginAuthor(BaseModel):
    """Plugin author information."""
    name: str
    email: Optional[str] = None
    url: Optional[str] = None


class PluginCapability(BaseModel):
    """Defines what a plugin can extend."""
    type: Literal["widget", "renderer", "layout", "logic_node", "command", "theme", "export"]
    name: str
    description: Optional[str] = None
    entry_point: str  # Python module path or file path
    config: Dict[str, Any] = Field(default_factory=dict)


class PluginPermission(BaseModel):
    """Plugin permission requirements."""
    filesystem: bool = False
    network: bool = False
    database: bool = False
    system: bool = False
    user_data: bool = False
    
    def requires_approval(self) -> bool:
        """Check if plugin requires user approval."""
        return any([self.filesystem, self.network, self.system])


class PluginManifest(BaseModel):
    """Complete plugin manifest schema."""
    
    # Basic metadata
    name: str
    version: str
    description: str
    author: PluginAuthor
    license: str = "MIT"
    homepage: Optional[str] = None
    repository: Optional[str] = None
    
    # Compatibility
    arcanum_version: str = ">=1.0.0"
    python_version: str = ">=3.10"
    
    # Plugin capabilities
    capabilities: List[PluginCapability] = Field(default_factory=list)
    
    # Dependencies
    dependencies: List[PluginDependency] = Field(default_factory=list)
    python_dependencies: List[str] = Field(default_factory=list)
    
    # Security and permissions
    permissions: PluginPermission = Field(default_factory=PluginPermission)
    
    # Installation and lifecycle
    install_script: Optional[str] = None
    uninstall_script: Optional[str] = None
    entry_point: Optional[str] = None  # Main plugin entry point
    
    # Metadata
    tags: List[str] = Field(default_factory=list)
    keywords: List[str] = Field(default_factory=list)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    # Configuration schema
    config_schema: Dict[str, Any] = Field(default_factory=dict)
    default_config: Dict[str, Any] = Field(default_factory=dict)
    
    @validator("name")
    def validate_name(cls, v):
        """Validate plugin name format."""
        if not re.match(r"^[a-zA-Z0-9_-]+$", v):
            raise ValueError("Plugin name must contain only letters, numbers, underscores, and hyphens")
        if len(v) < 3 or len(v) > 50:
            raise ValueError("Plugin name must be between 3 and 50 characters")
        return v
    
    @validator("version")
    def validate_version(cls, v):
        """Validate semantic version format."""
        if not re.match(r"^\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?$", v):
            raise ValueError("Version must follow semantic versioning (e.g., 1.0.0)")
        return v
    
    @validator("capabilities")
    def validate_capabilities(cls, v):
        """Validate plugin capabilities."""
        if not v:
            raise ValueError("Plugin must define at least one capability")
        
        # Check for duplicate capability names
        names = [cap.name for cap in v]
        if len(names) != len(set(names)):
            raise ValueError("Capability names must be unique")
        
        return v
    
    def get_capability(self, name: str) -> Optional[PluginCapability]:
        """Get a specific capability by name."""
        for cap in self.capabilities:
            if cap.name == name:
                return cap
        return None
    
    def has_capability_type(self, capability_type: str) -> bool:
        """Check if plugin has a specific capability type."""
        return any(cap.type == capability_type for cap in self.capabilities)
    
    def requires_permissions(self) -> bool:
        """Check if plugin requires any special permissions."""
        return self.permissions.requires_approval()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert manifest to dictionary."""
        return self.dict()
    
    @classmethod
    def from_file(cls, manifest_path: Path) -> "PluginManifest":
        """Load manifest from YAML file."""
        import yaml
        
        if not manifest_path.exists():
            raise FileNotFoundError(f"Plugin manifest not found: {manifest_path}")
        
        try:
            with open(manifest_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            return cls(**data)
            
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML in manifest: {e}")
        except Exception as e:
            raise ValueError(f"Failed to parse manifest: {e}")
    
    def save_to_file(self, manifest_path: Path):
        """Save manifest to YAML file."""
        import yaml
        
        manifest_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Convert to dict and clean up None values
        data = self.dict(exclude_none=True)
        
        with open(manifest_path, 'w', encoding='utf-8') as f:
            yaml.dump(data, f, default_flow_style=False, sort_keys=False, indent=2)


class PluginInfo(BaseModel):
    """Runtime plugin information."""
    manifest: PluginManifest
    path: Path
    installed: bool = False
    enabled: bool = False
    loaded: bool = False
    error: Optional[str] = None
    install_date: Optional[datetime] = None
    last_used: Optional[datetime] = None
    
    class Config:
        arbitrary_types_allowed = True
    
    @property
    def name(self) -> str:
        """Get plugin name."""
        return self.manifest.name
    
    @property
    def version(self) -> str:
        """Get plugin version."""
        return self.manifest.version
    
    @property
    def description(self) -> str:
        """Get plugin description."""
        return self.manifest.description
    
    def is_compatible(self, arcanum_version: str) -> bool:
        """Check if plugin is compatible with Arcanum version."""
        # Simple version check - in production, use proper semver comparison
        return True  # TODO: Implement proper version comparison
    
    def can_load(self) -> bool:
        """Check if plugin can be loaded."""
        return self.installed and self.enabled and not self.error


# Plugin registry entry for remote plugins
class PluginRegistryEntry(BaseModel):
    """Plugin entry in the remote registry."""
    name: str
    version: str
    description: str
    author: str
    repository: str
    download_url: str
    manifest_url: str
    tags: List[str] = Field(default_factory=list)
    downloads: int = 0
    rating: float = 0.0
    verified: bool = False
    created_at: datetime
    updated_at: datetime
    
    def to_dependency(self) -> PluginDependency:
        """Convert to plugin dependency."""
        return PluginDependency(
            name=self.name,
            version=self.version,
            source=self.repository
        )


# Template for creating new plugin manifests
PLUGIN_MANIFEST_TEMPLATE = """# Plugin Manifest for {name}
name: "{name}"
version: "1.0.0"
description: "{description}"

author:
  name: "{author_name}"
  email: "{author_email}"

license: "MIT"
arcanum_version: ">=1.0.0"
python_version: ">=3.10"

capabilities:
  - type: "widget"
    name: "{name}_widget"
    description: "Custom widget provided by {name}"
    entry_point: "widgets.{name}_widget"

permissions:
  filesystem: false
  network: false
  database: false
  system: false
  user_data: false

dependencies: []
python_dependencies: []

tags: []
keywords: []

config_schema: {{}}
default_config: {{}}
"""


def create_plugin_manifest_template(
    name: str,
    description: str = "",
    author_name: str = "",
    author_email: str = ""
) -> str:
    """Create a plugin manifest template."""
    return PLUGIN_MANIFEST_TEMPLATE.format(
        name=name,
        description=description or f"A plugin for Arcanum: {name}",
        author_name=author_name or "Plugin Author",
        author_email=author_email or "<EMAIL>"
    )

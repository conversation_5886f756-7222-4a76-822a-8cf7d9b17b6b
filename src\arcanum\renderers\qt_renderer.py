"""
PyQt5 renderer for desktop applications.
"""

import sys
from typing import Any, Dict, List, Optional, Union
from pathlib import Path

try:
    from PyQt5.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
        QGridLayout, QLabel, QPushButton, QLineEdit, QTextEdit, QCheckBox,
        QComboBox, QSlider, QTabWidget, QScrollArea, QFrame, QSizePolicy
    )
    from PyQt5.QtCore import Qt, pyqtSignal, QObject
    from PyQt5.QtGui import QPixmap, QFont
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

from .base import BaseRenderer, RenderContext, RenderingError
from ..schemas.layout import (
    PageLayout, BaseLayout, RowLayout, ColumnLayout, GridLayout, 
    TabLayout, AccordionLayout, LayoutType
)
from ..schemas.widgets import (
    BaseWidget, WidgetType, TextWidget, ButtonWidget, LabelWidget,
    ImageWidget, CheckboxWidget, DropdownWidget, <PERSON>liderWidget
)


class QtRenderer(BaseRenderer):
    """
    PyQt5 renderer for desktop applications.
    """
    
    def __init__(self, layout_engine=None):
        """Initialize the Qt renderer."""
        if not PYQT_AVAILABLE:
            raise ImportError("PyQt5 is required for Qt rendering")
            
        super().__init__(layout_engine)
        self.app: Optional[QApplication] = None
        self.main_window: Optional[QMainWindow] = None
        self.current_page: Optional[QWidget] = None
        
    def initialize(self, app_name: str = "Arcanum App", **kwargs) -> None:
        """
        Initialize the Qt application.
        
        Args:
            app_name: Application name for the window title.
            **kwargs: Additional Qt-specific parameters.
        """
        if QApplication.instance() is None:
            self.app = QApplication(sys.argv)
        else:
            self.app = QApplication.instance()
            
        self.app.setApplicationName(app_name)
        
        # Create main window
        self.main_window = QMainWindow()
        self.main_window.setWindowTitle(app_name)
        self.main_window.resize(1024, 768)
        
        # Set up central widget
        central_widget = QWidget()
        self.main_window.setCentralWidget(central_widget)
        
        self._initialized = True
        
    def render_page(self, page: PageLayout) -> QWidget:
        """
        Render a complete page layout.
        
        Args:
            page: Page layout to render.
            
        Returns:
            QWidget containing the rendered page.
        """
        self.validate_initialization()
        
        # Process template variables
        if page.title:
            # Ensure title is a string
            title_str = str(page.title) if not isinstance(page.title, str) else page.title
            processed_title = self.process_template_variables(title_str)
            self.main_window.setWindowTitle(processed_title)
        
        # Create page widget
        page_widget = QWidget()
        page_layout = QVBoxLayout(page_widget)
        
        # Render the main layout
        layout_widget = self.render_layout(page.layout, page_widget)
        page_layout.addWidget(layout_widget)
        
        # Apply page-level styling
        if page.style:
            self.apply_style(page_widget, page.style)
            
        self.current_page = page_widget
        self.main_window.setCentralWidget(page_widget)
        
        return page_widget
        
    def render_layout(self, layout: BaseLayout, parent: Any = None) -> QWidget:
        """
        Render a layout container.
        
        Args:
            layout: Layout to render.
            parent: Parent widget.
            
        Returns:
            QWidget containing the rendered layout.
        """
        container = QWidget(parent)
        
        if layout.type == LayoutType.COLUMN:
            qt_layout = QVBoxLayout(container)
            self._render_column_layout(layout, qt_layout, container)
        elif layout.type == LayoutType.ROW:
            qt_layout = QHBoxLayout(container)
            self._render_row_layout(layout, qt_layout, container)
        elif layout.type == LayoutType.GRID:
            qt_layout = QGridLayout(container)
            self._render_grid_layout(layout, qt_layout, container)
        elif layout.type == LayoutType.TAB:
            return self._render_tab_layout(layout, parent)
        else:
            # Default to vertical layout
            qt_layout = QVBoxLayout(container)
            self._render_column_layout(layout, qt_layout, container)
            
        # Apply layout styling
        if layout.style:
            self.apply_style(container, layout.style)
            
        return container
        
    def _render_column_layout(self, layout: ColumnLayout, qt_layout: QVBoxLayout, container: QWidget) -> None:
        """Render column layout content."""
        for item in layout.content:
            if isinstance(item, dict):
                if "widget" in item:
                    # This is a widget
                    widget = self.layout_engine.widget_registry.create_widget(item)
                    qt_widget = self.render_widget(widget, container)
                    qt_layout.addWidget(qt_widget)
                elif "type" in item:
                    # This is a nested layout
                    nested_layout = self.layout_engine.create_layout(item)
                    nested_widget = self.render_layout(nested_layout, container)
                    qt_layout.addWidget(nested_widget)
                    
    def _render_row_layout(self, layout: RowLayout, qt_layout: QHBoxLayout, container: QWidget) -> None:
        """Render row layout content."""
        for item in layout.content:
            if isinstance(item, dict):
                if "widget" in item:
                    # This is a widget
                    widget = self.layout_engine.widget_registry.create_widget(item)
                    qt_widget = self.render_widget(widget, container)
                    qt_layout.addWidget(qt_widget)
                elif "type" in item:
                    # This is a nested layout
                    nested_layout = self.layout_engine.create_layout(item)
                    nested_widget = self.render_layout(nested_layout, container)
                    qt_layout.addWidget(nested_widget)
                    
    def _render_grid_layout(self, layout: GridLayout, qt_layout: QGridLayout, container: QWidget) -> None:
        """Render grid layout content."""
        row, col = 0, 0
        max_cols = layout.columns or 12
        
        for item in layout.content:
            if isinstance(item, dict):
                if "widget" in item:
                    # This is a widget
                    widget = self.layout_engine.widget_registry.create_widget(item)
                    qt_widget = self.render_widget(widget, container)
                    qt_layout.addWidget(qt_widget, row, col)
                elif "type" in item:
                    # This is a nested layout
                    nested_layout = self.layout_engine.create_layout(item)
                    nested_widget = self.render_layout(nested_layout, container)
                    qt_layout.addWidget(nested_widget, row, col)
                    
                # Move to next position
                col += 1
                if col >= max_cols:
                    col = 0
                    row += 1
                    
    def _render_tab_layout(self, layout: TabLayout, parent: Any = None) -> QTabWidget:
        """Render tab layout."""
        tab_widget = QTabWidget(parent)
        
        for tab in layout.tabs:
            tab_content = QWidget()
            tab_layout = QVBoxLayout(tab_content)
            
            # Render tab content
            for item in tab.content:
                if isinstance(item, dict):
                    if "widget" in item:
                        widget = self.layout_engine.widget_registry.create_widget(item)
                        qt_widget = self.render_widget(widget, tab_content)
                        tab_layout.addWidget(qt_widget)
                    elif "type" in item:
                        nested_layout = self.layout_engine.create_layout(item)
                        nested_widget = self.render_layout(nested_layout, tab_content)
                        tab_layout.addWidget(nested_widget)
                        
            tab_widget.addTab(tab_content, tab.title)
            
        return tab_widget

    def render_widget(self, widget: BaseWidget, parent: Any = None) -> QWidget:
        """
        Render a widget.

        Args:
            widget: Widget to render.
            parent: Parent widget.

        Returns:
            QWidget representing the widget.
        """
        if widget.widget == WidgetType.LABEL:
            return self._render_label_widget(widget, parent)
        elif widget.widget == WidgetType.BUTTON:
            return self._render_button_widget(widget, parent)
        elif widget.widget == WidgetType.TEXT:
            return self._render_text_widget(widget, parent)
        elif widget.widget == WidgetType.TEXTAREA:
            return self._render_textarea_widget(widget, parent)
        elif widget.widget == WidgetType.CHECKBOX:
            return self._render_checkbox_widget(widget, parent)
        elif widget.widget == WidgetType.DROPDOWN:
            return self._render_dropdown_widget(widget, parent)
        elif widget.widget == WidgetType.SLIDER:
            return self._render_slider_widget(widget, parent)
        elif widget.widget == WidgetType.IMAGE:
            return self._render_image_widget(widget, parent)
        else:
            # Fallback to label
            fallback_label = QLabel(f"Unsupported widget: {widget.widget}", parent)
            return fallback_label

    def _render_label_widget(self, widget: LabelWidget, parent: Any = None) -> QLabel:
        """Render a label widget."""
        text = self.process_template_variables(widget.text)
        label = QLabel(text, parent)

        if widget.style:
            self.apply_style(label, widget.style)

        return label

    def _render_button_widget(self, widget: ButtonWidget, parent: Any = None) -> QPushButton:
        """Render a button widget."""
        text = self.process_template_variables(widget.label or "Button")
        button = QPushButton(text, parent)

        # Set button type styling
        if widget.variant == "primary":
            button.setStyleSheet("QPushButton { background-color: #007bff; color: white; }")
        elif widget.variant == "secondary":
            button.setStyleSheet("QPushButton { background-color: #6c757d; color: white; }")
        elif widget.variant == "success":
            button.setStyleSheet("QPushButton { background-color: #28a745; color: white; }")
        elif widget.variant == "warning":
            button.setStyleSheet("QPushButton { background-color: #ffc107; color: black; }")
        elif widget.variant == "danger":
            button.setStyleSheet("QPushButton { background-color: #dc3545; color: white; }")

        if widget.style:
            self.apply_style(button, widget.style)

        return button

    def _render_text_widget(self, widget: TextWidget, parent: Any = None) -> QLineEdit:
        """Render a text input widget."""
        line_edit = QLineEdit(parent)

        if widget.placeholder:
            line_edit.setPlaceholderText(self.process_template_variables(widget.placeholder))

        if widget.value:
            line_edit.setText(self.process_template_variables(str(widget.value)))

        if widget.style:
            self.apply_style(line_edit, widget.style)

        return line_edit

    def _render_textarea_widget(self, widget, parent: Any = None) -> QTextEdit:
        """Render a textarea widget."""
        text_edit = QTextEdit(parent)

        if hasattr(widget, 'value') and widget.value:
            text_edit.setPlainText(self.process_template_variables(str(widget.value)))

        if hasattr(widget, 'style') and widget.style:
            self.apply_style(text_edit, widget.style)

        return text_edit

    def _render_checkbox_widget(self, widget: CheckboxWidget, parent: Any = None) -> QCheckBox:
        """Render a checkbox widget."""
        text = self.process_template_variables(widget.label or "")
        checkbox = QCheckBox(text, parent)

        if widget.checked:
            checkbox.setChecked(True)

        if widget.style:
            self.apply_style(checkbox, widget.style)

        return checkbox

    def _render_dropdown_widget(self, widget: DropdownWidget, parent: Any = None) -> QComboBox:
        """Render a dropdown widget."""
        combo_box = QComboBox(parent)

        for option in widget.options:
            if isinstance(option, dict):
                combo_box.addItem(option.get("label", ""), option.get("value"))
            else:
                combo_box.addItem(str(option), option)

        if widget.value:
            index = combo_box.findData(widget.value)
            if index >= 0:
                combo_box.setCurrentIndex(index)

        if widget.style:
            self.apply_style(combo_box, widget.style)

        return combo_box

    def _render_slider_widget(self, widget: SliderWidget, parent: Any = None) -> QSlider:
        """Render a slider widget."""
        slider = QSlider(Qt.Horizontal, parent)

        slider.setMinimum(int(widget.min_value or 0))
        slider.setMaximum(int(widget.max_value or 100))
        slider.setValue(int(widget.value or 0))

        if widget.step:
            slider.setSingleStep(int(widget.step))

        if widget.style:
            self.apply_style(slider, widget.style)

        return slider

    def _render_image_widget(self, widget: ImageWidget, parent: Any = None) -> QLabel:
        """Render an image widget."""
        label = QLabel(parent)

        # Try to load the image
        try:
            pixmap = QPixmap(widget.src)
            if not pixmap.isNull():
                # Scale image if dimensions specified
                if widget.width or widget.height:
                    w = int(widget.width) if widget.width else pixmap.width()
                    h = int(widget.height) if widget.height else pixmap.height()
                    pixmap = pixmap.scaled(w, h, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                label.setPixmap(pixmap)
            else:
                label.setText(widget.alt or f"Image: {widget.src}")
        except Exception:
            label.setText(widget.alt or f"Image: {widget.src}")

        if widget.style:
            self.apply_style(label, widget.style)

        return label

    def apply_style(self, element: QWidget, style: Union[str, Dict[str, Any]]) -> None:
        """
        Apply styling to a Qt widget.

        Args:
            element: Qt widget to style.
            style: Style definition (CSS string or style dict).
        """
        if isinstance(style, str):
            # Apply as Qt stylesheet
            element.setStyleSheet(style)
        elif isinstance(style, dict):
            # Convert style dict to Qt stylesheet
            stylesheet_parts = []

            for prop, value in style.items():
                if prop == "background_color":
                    stylesheet_parts.append(f"background-color: {value};")
                elif prop == "color":
                    stylesheet_parts.append(f"color: {value};")
                elif prop == "font_size":
                    stylesheet_parts.append(f"font-size: {value};")
                elif prop == "font_weight":
                    stylesheet_parts.append(f"font-weight: {value};")
                elif prop == "padding":
                    stylesheet_parts.append(f"padding: {value};")
                elif prop == "margin":
                    stylesheet_parts.append(f"margin: {value};")
                elif prop == "border":
                    stylesheet_parts.append(f"border: {value};")
                elif prop == "border_radius":
                    stylesheet_parts.append(f"border-radius: {value};")

            if stylesheet_parts:
                stylesheet = " ".join(stylesheet_parts)
                element.setStyleSheet(stylesheet)

    def show(self) -> None:
        """Display the rendered application."""
        self.validate_initialization()
        if self.main_window:
            self.main_window.show()

    def close(self) -> None:
        """Close the rendered application."""
        if self.main_window:
            self.main_window.close()

    def run(self) -> int:
        """
        Run the Qt application event loop.

        Returns:
            Application exit code.
        """
        self.validate_initialization()
        if self.app:
            return self.app.exec_()
        return 0

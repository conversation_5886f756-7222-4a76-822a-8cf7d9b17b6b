"""
Base classes for the export system.
"""

from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import logging
from pydantic import BaseModel, Field
from datetime import datetime

logger = logging.getLogger(__name__)


class ExportTarget(str, Enum):
    """Supported export targets."""
    
    # Web targets
    STATIC_HTML = "static_html"
    REACT_APP = "react_app"
    VUE_APP = "vue_app"
    ANGULAR_APP = "angular_app"
    
    # Desktop targets
    PYINSTALLER = "pyinstaller"
    ELECTRON = "electron"
    TAURI = "tauri"
    
    # Mobile targets
    CORDOVA = "cordova"
    REACT_NATIVE = "react_native"
    FLUTTER = "flutter"
    
    # Cloud targets
    DOCKER = "docker"
    LAMBDA = "lambda"
    VERCEL = "vercel"
    NETLIFY = "netlify"


class ExportConfig(BaseModel):
    """Configuration for export operations."""
    
    target: ExportTarget
    output_path: Path
    project_name: str
    version: str = "1.0.0"
    
    # Build options
    minify: bool = True
    optimize: bool = True
    include_source_maps: bool = False
    
    # Asset options
    bundle_assets: bool = True
    compress_images: bool = True
    include_fonts: bool = True
    
    # Platform-specific options
    platform_config: Dict[str, Any] = Field(default_factory=dict)
    
    # Environment variables
    env_vars: Dict[str, str] = Field(default_factory=dict)
    
    # Custom build commands
    pre_build_commands: List[str] = Field(default_factory=list)
    post_build_commands: List[str] = Field(default_factory=list)
    
    # Dependencies
    include_dependencies: List[str] = Field(default_factory=list)
    exclude_dependencies: List[str] = Field(default_factory=list)


class ExportResult(BaseModel):
    """Result of an export operation."""
    
    success: bool
    target: ExportTarget
    output_path: Path
    build_time: float
    file_size: Optional[int] = None
    
    # Build artifacts
    artifacts: List[Path] = Field(default_factory=list)
    
    # Logs and messages
    logs: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    errors: List[str] = Field(default_factory=list)
    
    # Metadata
    build_timestamp: datetime = Field(default_factory=datetime.now)
    build_info: Dict[str, Any] = Field(default_factory=dict)


class BaseExporter(ABC):
    """Base class for all exporters."""
    
    def __init__(self, config: ExportConfig):
        """
        Initialize exporter.
        
        Args:
            config: Export configuration.
        """
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @abstractmethod
    def export(self, project_path: Path) -> ExportResult:
        """
        Export the project.
        
        Args:
            project_path: Path to the Arcanum project.
            
        Returns:
            Export result.
        """
        pass
    
    @abstractmethod
    def validate_requirements(self) -> List[str]:
        """
        Validate that all requirements are met for this export target.
        
        Returns:
            List of missing requirements or empty list if all requirements met.
        """
        pass
    
    def prepare_output_directory(self) -> Path:
        """
        Prepare the output directory for export.
        
        Returns:
            Path to the prepared output directory.
        """
        output_path = self.config.output_path
        
        # Create output directory
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Clean existing files if needed
        if self.config.platform_config.get("clean_output", True):
            import shutil
            for item in output_path.iterdir():
                if item.is_dir():
                    shutil.rmtree(item)
                else:
                    item.unlink()
        
        return output_path
    
    def run_build_commands(self, commands: List[str], cwd: Path) -> bool:
        """
        Run build commands.
        
        Args:
            commands: List of commands to run.
            cwd: Working directory for commands.
            
        Returns:
            True if all commands succeeded.
        """
        import subprocess
        
        for command in commands:
            try:
                self.logger.info(f"Running command: {command}")
                result = subprocess.run(
                    command,
                    shell=True,
                    cwd=cwd,
                    capture_output=True,
                    text=True,
                    timeout=300  # 5 minute timeout
                )
                
                if result.returncode != 0:
                    self.logger.error(f"Command failed: {command}")
                    self.logger.error(f"Error output: {result.stderr}")
                    return False
                
                if result.stdout:
                    self.logger.info(f"Command output: {result.stdout}")
                    
            except subprocess.TimeoutExpired:
                self.logger.error(f"Command timed out: {command}")
                return False
            except Exception as e:
                self.logger.error(f"Error running command {command}: {e}")
                return False
        
        return True
    
    def copy_assets(self, project_path: Path, output_path: Path) -> bool:
        """
        Copy project assets to output directory.
        
        Args:
            project_path: Source project path.
            output_path: Destination output path.
            
        Returns:
            True if assets copied successfully.
        """
        import shutil
        
        try:
            # Copy assets directory if it exists
            assets_dir = project_path / "assets"
            if assets_dir.exists():
                output_assets = output_path / "assets"
                if output_assets.exists():
                    shutil.rmtree(output_assets)
                shutil.copytree(assets_dir, output_assets)
                self.logger.info("Copied assets directory")
            
            # Copy static files if they exist
            static_dir = project_path / "static"
            if static_dir.exists():
                output_static = output_path / "static"
                if output_static.exists():
                    shutil.rmtree(output_static)
                shutil.copytree(static_dir, output_static)
                self.logger.info("Copied static directory")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error copying assets: {e}")
            return False
    
    def calculate_build_size(self, output_path: Path) -> int:
        """
        Calculate total size of build output.
        
        Args:
            output_path: Path to build output.
            
        Returns:
            Total size in bytes.
        """
        total_size = 0
        
        for file_path in output_path.rglob("*"):
            if file_path.is_file():
                total_size += file_path.stat().st_size
        
        return total_size
    
    def create_build_info(self, project_path: Path) -> Dict[str, Any]:
        """
        Create build information metadata.
        
        Args:
            project_path: Path to the project.
            
        Returns:
            Build information dictionary.
        """
        from ..core.project import ArcanumProject
        
        try:
            project = ArcanumProject(project_path)
            config = project.load()
            
            return {
                "project_name": config.app_name,
                "project_version": config.version,
                "export_target": self.config.target.value,
                "build_timestamp": datetime.now().isoformat(),
                "arcanum_version": "1.0.1",
                "build_config": self.config.model_dump()
            }
            
        except Exception as e:
            self.logger.warning(f"Could not create build info: {e}")
            return {
                "export_target": self.config.target.value,
                "build_timestamp": datetime.now().isoformat(),
                "error": str(e)
            }


class ExportError(Exception):
    """Exception raised during export operations."""
    
    def __init__(self, message: str, target: ExportTarget, details: Optional[Dict[str, Any]] = None):
        """
        Initialize export error.
        
        Args:
            message: Error message.
            target: Export target that failed.
            details: Additional error details.
        """
        super().__init__(message)
        self.target = target
        self.details = details or {}


class ExportValidationError(ExportError):
    """Exception raised when export validation fails."""
    pass


class ExportBuildError(ExportError):
    """Exception raised when export build fails."""
    pass


def get_available_exporters() -> Dict[ExportTarget, type]:
    """
    Get all available exporters.

    Returns:
        Dictionary mapping export targets to exporter classes.
    """
    from .web import StaticWebExporter, ReactExporter
    from .desktop import PyInstallerExporter, ElectronExporter

    return {
        ExportTarget.STATIC_HTML: StaticWebExporter,
        ExportTarget.REACT_APP: ReactExporter,
        ExportTarget.PYINSTALLER: PyInstallerExporter,
        ExportTarget.ELECTRON: ElectronExporter,
    }


def create_exporter(target: ExportTarget, config: ExportConfig) -> BaseExporter:
    """
    Create an exporter for the specified target.

    Args:
        target: Export target.
        config: Export configuration.

    Returns:
        Exporter instance.

    Raises:
        ValueError: If target is not supported.
    """
    exporters = get_available_exporters()

    if target not in exporters:
        raise ValueError(f"Unsupported export target: {target}")

    exporter_class = exporters[target]
    return exporter_class(config)

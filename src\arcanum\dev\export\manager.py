"""
Export manager for coordinating export operations.
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

from .base import (
    ExportTarget, ExportConfig, ExportResult, BaseExporter,
    get_available_exporters, create_exporter, ExportError
)

logger = logging.getLogger(__name__)


class ExportManager:
    """
    Manages export operations for Arcanum projects.
    """
    
    def __init__(self, project_path: Path):
        """
        Initialize export manager.
        
        Args:
            project_path: Path to the Arcanum project.
        """
        self.project_path = project_path
        self.export_history_file = project_path / ".arcanum" / "export_history.json"
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def get_available_targets(self) -> List[ExportTarget]:
        """
        Get list of available export targets.
        
        Returns:
            List of available export targets.
        """
        return list(get_available_exporters().keys())
    
    def validate_target(self, target: ExportTarget) -> List[str]:
        """
        Validate requirements for an export target.
        
        Args:
            target: Export target to validate.
            
        Returns:
            List of missing requirements or empty list if all requirements met.
        """
        try:
            # Create a temporary config for validation
            temp_config = ExportConfig(
                target=target,
                output_path=Path("/tmp"),
                project_name="temp"
            )
            
            exporter = create_exporter(target, temp_config)
            return exporter.validate_requirements()
            
        except Exception as e:
            self.logger.error(f"Error validating target {target}: {e}")
            return [f"Validation error: {e}"]
    
    def export_project(self, config: ExportConfig) -> ExportResult:
        """
        Export project using the specified configuration.
        
        Args:
            config: Export configuration.
            
        Returns:
            Export result.
        """
        self.logger.info(f"Starting export to {config.target} at {config.output_path}")
        
        try:
            # Create exporter
            exporter = create_exporter(config.target, config)
            
            # Perform export
            result = exporter.export(self.project_path)
            
            # Save to history
            self._save_export_history(config, result)
            
            if result.success:
                self.logger.info(f"Export completed successfully in {result.build_time:.2f}s")
            else:
                self.logger.error(f"Export failed: {', '.join(result.errors)}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Export failed with exception: {e}")
            
            # Create error result
            result = ExportResult(
                success=False,
                target=config.target,
                output_path=config.output_path,
                build_time=0
            )
            result.errors.append(str(e))
            
            return result
    
    def get_export_history(self) -> List[Dict[str, Any]]:
        """
        Get export history for this project.
        
        Returns:
            List of export history entries.
        """
        if not self.export_history_file.exists():
            return []
        
        try:
            with open(self.export_history_file, "r") as f:
                data = json.load(f)
                return data.get("exports", [])
        except Exception as e:
            self.logger.warning(f"Failed to load export history: {e}")
            return []
    
    def clear_export_history(self):
        """Clear export history for this project."""
        if self.export_history_file.exists():
            self.export_history_file.unlink()
        self.logger.info("Export history cleared")
    
    def get_last_export(self, target: Optional[ExportTarget] = None) -> Optional[Dict[str, Any]]:
        """
        Get the last export for a specific target or overall.
        
        Args:
            target: Optional target to filter by.
            
        Returns:
            Last export entry or None.
        """
        history = self.get_export_history()
        
        if target:
            # Filter by target
            filtered = [entry for entry in history if entry.get("target") == target.value]
            return filtered[-1] if filtered else None
        else:
            # Return overall last export
            return history[-1] if history else None
    
    def create_export_config(
        self,
        target: ExportTarget,
        output_path: Path,
        **kwargs
    ) -> ExportConfig:
        """
        Create export configuration with project defaults.
        
        Args:
            target: Export target.
            output_path: Output path for export.
            **kwargs: Additional configuration options.
            
        Returns:
            Export configuration.
        """
        # Load project to get defaults
        try:
            from ...core.project import ArcanumProject
            project = ArcanumProject(self.project_path)
            config = project.load()
            
            project_name = config.app_name
            version = config.version
            
        except Exception as e:
            self.logger.warning(f"Could not load project config: {e}")
            project_name = self.project_path.name
            version = "1.0.0"
        
        # Create configuration
        export_config = ExportConfig(
            target=target,
            output_path=output_path,
            project_name=project_name,
            version=version,
            **kwargs
        )
        
        return export_config
    
    def _save_export_history(self, config: ExportConfig, result: ExportResult):
        """
        Save export to history.
        
        Args:
            config: Export configuration.
            result: Export result.
        """
        try:
            # Ensure directory exists
            self.export_history_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Load existing history
            history = {"exports": self.get_export_history()}
            
            # Create history entry
            entry = {
                "timestamp": datetime.now().isoformat(),
                "target": config.target.value,
                "output_path": str(config.output_path),
                "project_name": config.project_name,
                "version": config.version,
                "success": result.success,
                "build_time": result.build_time,
                "file_size": result.file_size,
                "errors": result.errors,
                "warnings": result.warnings,
                "config": config.model_dump()
            }
            
            # Add to history
            history["exports"].append(entry)
            
            # Keep only last 50 exports
            if len(history["exports"]) > 50:
                history["exports"] = history["exports"][-50:]
            
            # Save to file
            with open(self.export_history_file, "w") as f:
                json.dump(history, f, indent=2)
                
        except Exception as e:
            self.logger.warning(f"Failed to save export history: {e}")
    
    def get_export_summary(self) -> Dict[str, Any]:
        """
        Get summary of export capabilities and history.
        
        Returns:
            Export summary information.
        """
        available_targets = self.get_available_targets()
        history = self.get_export_history()
        
        # Count exports by target
        target_counts = {}
        successful_exports = 0
        
        for entry in history:
            target = entry.get("target")
            if target:
                target_counts[target] = target_counts.get(target, 0) + 1
            
            if entry.get("success"):
                successful_exports += 1
        
        # Check requirements for each target
        target_status = {}
        for target in available_targets:
            missing = self.validate_target(target)
            target_status[target.value] = {
                "available": len(missing) == 0,
                "missing_requirements": missing
            }
        
        return {
            "available_targets": [t.value for t in available_targets],
            "target_status": target_status,
            "export_history": {
                "total_exports": len(history),
                "successful_exports": successful_exports,
                "failed_exports": len(history) - successful_exports,
                "exports_by_target": target_counts,
                "last_export": self.get_last_export()
            }
        }


class BatchExportManager:
    """
    Manager for batch export operations.
    """
    
    def __init__(self, project_path: Path):
        """
        Initialize batch export manager.
        
        Args:
            project_path: Path to the Arcanum project.
        """
        self.project_path = project_path
        self.export_manager = ExportManager(project_path)
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def export_multiple(
        self,
        targets: List[ExportTarget],
        base_output_path: Path,
        **common_config
    ) -> Dict[ExportTarget, ExportResult]:
        """
        Export to multiple targets.
        
        Args:
            targets: List of export targets.
            base_output_path: Base output path (subdirectories will be created).
            **common_config: Common configuration for all exports.
            
        Returns:
            Dictionary mapping targets to results.
        """
        results = {}
        
        for target in targets:
            self.logger.info(f"Exporting to {target.value}...")
            
            # Create target-specific output path
            target_output = base_output_path / target.value
            
            # Create configuration
            config = self.export_manager.create_export_config(
                target=target,
                output_path=target_output,
                **common_config
            )
            
            # Perform export
            result = self.export_manager.export_project(config)
            results[target] = result
            
            if result.success:
                self.logger.info(f"✓ {target.value} export completed")
            else:
                self.logger.error(f"✗ {target.value} export failed")
        
        return results
    
    def export_all_available(
        self,
        base_output_path: Path,
        **common_config
    ) -> Dict[ExportTarget, ExportResult]:
        """
        Export to all available targets.
        
        Args:
            base_output_path: Base output path.
            **common_config: Common configuration for all exports.
            
        Returns:
            Dictionary mapping targets to results.
        """
        available_targets = []
        
        # Check which targets are available
        for target in self.export_manager.get_available_targets():
            missing = self.export_manager.validate_target(target)
            if not missing:
                available_targets.append(target)
            else:
                self.logger.warning(f"Skipping {target.value}: missing {', '.join(missing)}")
        
        if not available_targets:
            self.logger.error("No export targets are available")
            return {}
        
        return self.export_multiple(available_targets, base_output_path, **common_config)

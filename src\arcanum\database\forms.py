"""
Form data handling and submission management.
"""

import json
import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from .models import DatabaseModel, TableSchema, ColumnType, ColumnConstraint, ConstraintType

logger = logging.getLogger(__name__)


class SubmissionStatus(Enum):
    """Form submission status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    REJECTED = "rejected"


@dataclass
class FormField:
    """Represents a form field definition."""
    name: str
    field_type: str  # text, email, number, boolean, select, etc.
    label: str = ""
    required: bool = False
    default_value: Any = None
    validation_rules: Dict[str, Any] = field(default_factory=dict)
    options: List[str] = field(default_factory=list)  # For select fields
    description: str = ""
    
    def validate_value(self, value: Any) -> List[str]:
        """Validate a field value."""
        errors = []
        
        # Check required
        if self.required and (value is None or value == ""):
            errors.append(f"Field '{self.name}' is required")
            return errors
            
        # Skip validation if value is empty and not required
        if value is None or value == "":
            return errors
            
        # Type validation
        if self.field_type == "email":
            if not self._is_valid_email(str(value)):
                errors.append(f"Field '{self.name}' must be a valid email address")
                
        elif self.field_type == "number":
            try:
                float(value)
            except (ValueError, TypeError):
                errors.append(f"Field '{self.name}' must be a number")
                
        elif self.field_type == "boolean":
            if value not in [True, False, 0, 1, "true", "false", "True", "False"]:
                errors.append(f"Field '{self.name}' must be a boolean value")
                
        elif self.field_type == "select":
            if self.options and str(value) not in self.options:
                errors.append(f"Field '{self.name}' must be one of: {', '.join(self.options)}")
                
        # Custom validation rules
        for rule_name, rule_value in self.validation_rules.items():
            if rule_name == "min_length" and len(str(value)) < rule_value:
                errors.append(f"Field '{self.name}' must be at least {rule_value} characters")
                
            elif rule_name == "max_length" and len(str(value)) > rule_value:
                errors.append(f"Field '{self.name}' must be no more than {rule_value} characters")
                
            elif rule_name == "min_value" and float(value) < rule_value:
                errors.append(f"Field '{self.name}' must be at least {rule_value}")
                
            elif rule_name == "max_value" and float(value) > rule_value:
                errors.append(f"Field '{self.name}' must be no more than {rule_value}")
                
            elif rule_name == "pattern":
                import re
                if not re.match(rule_value, str(value)):
                    errors.append(f"Field '{self.name}' format is invalid")
                    
        return errors
        
    def _is_valid_email(self, email: str) -> bool:
        """Basic email validation."""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None


@dataclass
class FormDefinition:
    """Defines a form structure and validation rules."""
    name: str
    title: str = ""
    description: str = ""
    fields: List[FormField] = field(default_factory=list)
    submit_action: str = "save"  # save, email, webhook, etc.
    submit_config: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    
    def add_field(self, name: str, field_type: str, label: str = "", 
                 required: bool = False, **kwargs) -> FormField:
        """Add a field to the form."""
        field = FormField(
            name=name,
            field_type=field_type,
            label=label or name.replace('_', ' ').title(),
            required=required,
            **kwargs
        )
        self.fields.append(field)
        return field
        
    def get_field(self, name: str) -> Optional[FormField]:
        """Get a field by name."""
        return next((f for f in self.fields if f.name == name), None)
        
    def validate_submission(self, data: Dict[str, Any]) -> List[str]:
        """Validate form submission data."""
        errors = []
        
        for field in self.fields:
            value = data.get(field.name)
            field_errors = field.validate_value(value)
            errors.extend(field_errors)
            
        return errors
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert form definition to dictionary."""
        return {
            "name": self.name,
            "title": self.title,
            "description": self.description,
            "fields": [
                {
                    "name": f.name,
                    "type": f.field_type,
                    "label": f.label,
                    "required": f.required,
                    "default_value": f.default_value,
                    "validation_rules": f.validation_rules,
                    "options": f.options,
                    "description": f.description
                }
                for f in self.fields
            ],
            "submit_action": self.submit_action,
            "submit_config": self.submit_config,
            "created_at": self.created_at.isoformat()
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FormDefinition':
        """Create form definition from dictionary."""
        form = cls(
            name=data["name"],
            title=data.get("title", ""),
            description=data.get("description", ""),
            submit_action=data.get("submit_action", "save"),
            submit_config=data.get("submit_config", {}),
            created_at=datetime.fromisoformat(data.get("created_at", datetime.now().isoformat()))
        )
        
        for field_data in data.get("fields", []):
            field = FormField(
                name=field_data["name"],
                field_type=field_data["type"],
                label=field_data.get("label", ""),
                required=field_data.get("required", False),
                default_value=field_data.get("default_value"),
                validation_rules=field_data.get("validation_rules", {}),
                options=field_data.get("options", []),
                description=field_data.get("description", "")
            )
            form.fields.append(field)
            
        return form


@dataclass
class FormSubmission:
    """Represents a form submission."""
    id: Optional[int] = None
    form_name: str = ""
    form_data: Dict[str, Any] = field(default_factory=dict)
    user_id: Optional[int] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    status: SubmissionStatus = SubmissionStatus.PENDING
    submitted_at: datetime = field(default_factory=datetime.now)
    processed_at: Optional[datetime] = None
    validation_errors: List[str] = field(default_factory=list)
    processing_notes: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert submission to dictionary."""
        return {
            "id": self.id,
            "form_name": self.form_name,
            "form_data": self.form_data,
            "user_id": self.user_id,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "status": self.status.value,
            "submitted_at": self.submitted_at.isoformat(),
            "processed_at": self.processed_at.isoformat() if self.processed_at else None,
            "validation_errors": self.validation_errors,
            "processing_notes": self.processing_notes
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'FormSubmission':
        """Create submission from dictionary."""
        return cls(
            id=data.get("id"),
            form_name=data.get("form_name", ""),
            form_data=data.get("form_data", {}),
            user_id=data.get("user_id"),
            ip_address=data.get("ip_address"),
            user_agent=data.get("user_agent"),
            status=SubmissionStatus(data.get("status", "pending")),
            submitted_at=datetime.fromisoformat(data.get("submitted_at", datetime.now().isoformat())),
            processed_at=datetime.fromisoformat(data["processed_at"]) if data.get("processed_at") else None,
            validation_errors=data.get("validation_errors", []),
            processing_notes=data.get("processing_notes", "")
        )


class FormDataHandler:
    """Handles form data processing and storage."""
    
    def __init__(self, database_manager):
        self.db = database_manager
        self.forms: Dict[str, FormDefinition] = {}
        
    def register_form(self, form_definition: FormDefinition):
        """Register a form definition."""
        self.forms[form_definition.name] = form_definition
        logger.info(f"Registered form: {form_definition.name}")
        
    def create_form(self, name: str, title: str = "", description: str = "") -> FormDefinition:
        """Create a new form definition."""
        form = FormDefinition(name=name, title=title, description=description)
        self.register_form(form)
        return form
        
    def get_form(self, name: str) -> Optional[FormDefinition]:
        """Get a form definition by name."""
        return self.forms.get(name)
        
    def submit_form(self, form_name: str, data: Dict[str, Any], 
                   user_id: int = None, ip_address: str = None, 
                   user_agent: str = None) -> FormSubmission:
        """Submit form data."""
        # Get form definition
        form_def = self.get_form(form_name)
        if not form_def:
            raise ValueError(f"Form '{form_name}' not found")
            
        # Create submission
        submission = FormSubmission(
            form_name=form_name,
            form_data=data,
            user_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        # Validate submission
        validation_errors = form_def.validate_submission(data)
        submission.validation_errors = validation_errors
        
        if validation_errors:
            submission.status = SubmissionStatus.REJECTED
            logger.warning(f"Form submission rejected: {validation_errors}")
        else:
            submission.status = SubmissionStatus.PENDING
            
        # Save to database
        self._save_submission(submission)
        
        # Process submission if valid
        if not validation_errors:
            self._process_submission(submission, form_def)
            
        return submission
        
    def _save_submission(self, submission: FormSubmission):
        """Save submission to database."""
        if not self.db.table_exists("form_submissions"):
            logger.error("Form submissions table does not exist")
            return
            
        data = {
            "form_name": submission.form_name,
            "form_data": json.dumps(submission.form_data),
            "user_id": submission.user_id,
            "ip_address": submission.ip_address,
            "user_agent": submission.user_agent,
            "status": submission.status.value,
            "submitted_at": submission.submitted_at.isoformat()
        }
        
        result = self.db.insert("form_submissions", data)
        if result.success:
            submission.id = result.last_insert_id
            logger.info(f"Form submission saved with ID: {submission.id}")
        else:
            logger.error(f"Failed to save form submission: {result.error}")
            
    def _process_submission(self, submission: FormSubmission, form_def: FormDefinition):
        """Process a valid form submission."""
        submission.status = SubmissionStatus.PROCESSING
        
        try:
            action = form_def.submit_action
            
            if action == "save":
                # Just save to database (already done)
                submission.status = SubmissionStatus.COMPLETED
                submission.processing_notes = "Saved to database"
                
            elif action == "email":
                # Send email notification
                self._send_email_notification(submission, form_def)
                submission.status = SubmissionStatus.COMPLETED
                submission.processing_notes = "Email notification sent"
                
            elif action == "webhook":
                # Send to webhook
                self._send_webhook(submission, form_def)
                submission.status = SubmissionStatus.COMPLETED
                submission.processing_notes = "Webhook called"
                
            else:
                submission.status = SubmissionStatus.FAILED
                submission.processing_notes = f"Unknown action: {action}"
                
        except Exception as e:
            submission.status = SubmissionStatus.FAILED
            submission.processing_notes = f"Processing failed: {e}"
            logger.error(f"Form processing failed: {e}")
            
        finally:
            submission.processed_at = datetime.now()
            self._update_submission_status(submission)
            
    def _send_email_notification(self, submission: FormSubmission, form_def: FormDefinition):
        """Send email notification for form submission."""
        # TODO: Implement email sending
        logger.info(f"Email notification for form '{submission.form_name}' (not implemented)")
        
    def _send_webhook(self, submission: FormSubmission, form_def: FormDefinition):
        """Send form data to webhook."""
        # TODO: Implement webhook sending
        logger.info(f"Webhook for form '{submission.form_name}' (not implemented)")
        
    def _update_submission_status(self, submission: FormSubmission):
        """Update submission status in database."""
        if not submission.id:
            return
            
        data = {
            "status": submission.status.value,
            "processed_at": submission.processed_at.isoformat() if submission.processed_at else None,
            "processing_notes": submission.processing_notes
        }
        
        result = self.db.update("form_submissions", data, "id", submission.id)
        if not result.success:
            logger.error(f"Failed to update submission status: {result.error}")
            
    def get_submissions(self, form_name: str = None, status: SubmissionStatus = None,
                       limit: int = 100) -> List[FormSubmission]:
        """Get form submissions."""
        query = self.db.query("form_submissions").select().limit(limit)
        
        if form_name:
            query.where("form_name", "=", form_name)

        if status:
            query.where("status", "=", status.value)
            
        query.order_by_desc("submitted_at")
        
        result = self.db.execute_query(query)
        
        submissions = []
        if result.success:
            for row in result.data:
                # Parse form_data JSON
                form_data = json.loads(row.get("form_data", "{}"))
                
                submission = FormSubmission(
                    id=row["id"],
                    form_name=row["form_name"],
                    form_data=form_data,
                    user_id=row.get("user_id"),
                    ip_address=row.get("ip_address"),
                    user_agent=row.get("user_agent"),
                    status=SubmissionStatus(row.get("status", "pending")),
                    submitted_at=datetime.fromisoformat(row["submitted_at"]),
                    processed_at=datetime.fromisoformat(row["processed_at"]) if row.get("processed_at") else None,
                    processing_notes=row.get("processing_notes", "")
                )
                submissions.append(submission)
                
        return submissions
        
    def get_submission_stats(self, form_name: str = None) -> Dict[str, Any]:
        """Get submission statistics."""
        stats = {
            "total": 0,
            "by_status": {},
            "by_form": {},
            "recent_count": 0
        }
        
        # Total count
        total_query = self.db.query("form_submissions").select("COUNT(*) as count")
        if form_name:
            total_query.where("form_name", "=", form_name)

        result = self.db.execute_query(total_query)
        if result.success and result.data:
            stats["total"] = result.data[0]["count"]

        # By status
        status_query = self.db.query("form_submissions").select("status", "COUNT(*) as count").group_by("status")
        if form_name:
            status_query.where("form_name", "=", form_name)
            
        result = self.db.execute_query(status_query)
        if result.success:
            for row in result.data:
                stats["by_status"][row["status"]] = row["count"]
                
        # By form (if not filtering by form)
        if not form_name:
            form_query = self.db.query("form_submissions").select("form_name", "COUNT(*) as count").group_by("form_name")
            result = self.db.execute_query(form_query)
            if result.success:
                for row in result.data:
                    stats["by_form"][row["form_name"]] = row["count"]
                    
        return stats

"""
Main GUI Builder application for Arcanum.
"""

import sys
from typing import Optional, Dict, Any
from pathlib import Path

# Add the src directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

try:
    from PyQt5.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout,
        QSplitter, QAction, QToolBar,
        QMessageBox, QFileDialog, QTabWidget, QDialog
    )
    from PyQt5.QtCore import Qt, pyqtSignal, QTimer
    from PyQt5.QtGui import QKeySequence
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False

from .widget_palette import WidgetPalette
from .design_canvas import DesignCanvas
from .property_editor import PropertyEditor
from .yaml_editor import YamlEditor
from .page_navigator import PageNavigator
from .project_manager import (
    ModernDashboard, NewProjectDialog, ProjectValidator,
    RecentProjectsManager
)
from .editor_commands import <PERSON><PERSON><PERSON>mandManager, ClipboardManager
from .error_handling import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rror<PERSON>ialog, safe_execute
from .keyboard_shortcuts import KeyboardShortcutManager
from .visual_feedback import FeedbackManager
from .editor_toolbar import EditorToolbar
from ...core.registry import WidgetRegistry
from ...core.layout import LayoutEngine
from ...core.project import ArcanumProject


class ArcanumBuilder(QMainWindow):
    """
    Main GUI Builder application for creating Arcanum applications.
    """
    
    # Signals
    project_changed = pyqtSignal()
    selection_changed = pyqtSignal(object)
    
    def __init__(self):
        """Initialize the Arcanum Builder."""
        if not PYQT_AVAILABLE:
            raise ImportError("PyQt5 is required for the GUI Builder")
            
        super().__init__()
        
        # Core components
        self.widget_registry = WidgetRegistry()
        self.layout_engine = LayoutEngine(self.widget_registry)
        self.current_project: Optional[ArcanumProject] = None
        self.current_page_path: Optional[Path] = None
        
        # UI components
        self.widget_palette: Optional[WidgetPalette] = None
        self.design_canvas: Optional[DesignCanvas] = None
        self.property_editor: Optional[PropertyEditor] = None
        self.yaml_editor: Optional[YamlEditor] = None
        self.editor_toolbar: Optional[EditorToolbar] = None

        # Project management
        self.recent_manager = RecentProjectsManager()
        self.grid_enabled = True
        self.snap_enabled = True

        # Professional editor features
        self.error_handler = ErrorHandler(self)
        self.command_manager = EditorCommandManager(self)
        self.clipboard_manager = ClipboardManager(self)
        self.shortcut_manager = KeyboardShortcutManager(self)
        self.feedback_manager = FeedbackManager(self)

        # Connect error handler signals
        self.error_handler.error_occurred.connect(self.show_error_dialog)
        self.error_handler.warning_occurred.connect(self.show_warning_dialog)

        # Auto-save timer
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self.auto_save)
        self.auto_save_timer.start(30000)  # Auto-save every 30 seconds
        
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """Set up the user interface."""
        self.setWindowTitle("Arcanum Builder")
        self.setMinimumSize(800, 500)
        self.resize(1000, 600)
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create toolbar
        self.create_toolbar()

        # Create central widget with splitters
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # Create modern dashboard (initially visible)
        self.dashboard_widget = ModernDashboard()
        self.dashboard_widget.project_selected.connect(self.open_project_by_path)
        self.dashboard_widget.new_project_requested.connect(self.new_project)
        self.dashboard_widget.open_project_requested.connect(self.open_project)
        main_layout.addWidget(self.dashboard_widget)

        # Create main editor (initially hidden)
        self.editor_widget = self.create_editor_interface()
        self.editor_widget.hide()
        main_layout.addWidget(self.editor_widget)
        
        # Status bar
        self.statusBar().showMessage("Ready")



    def create_editor_interface(self) -> QWidget:
        """Create the main editor interface."""
        editor_widget = QWidget()
        editor_layout = QVBoxLayout(editor_widget)
        editor_layout.setContentsMargins(0, 0, 0, 0)
        editor_layout.setSpacing(0)

        # Create and add editor toolbar
        self.editor_toolbar = EditorToolbar()
        self.editor_toolbar.mode_changed.connect(self.on_editor_mode_changed)
        self.editor_toolbar.tool_activated.connect(self.on_editor_tool_activated)
        editor_layout.addWidget(self.editor_toolbar)

        # Main horizontal splitter
        main_splitter = QSplitter(Qt.Horizontal)

        # Left panel - Compact Widget Palette
        self.widget_palette = WidgetPalette(self.widget_registry)
        self.widget_palette.setMaximumWidth(200)  # More compact
        self.widget_palette.setMinimumWidth(180)
        main_splitter.addWidget(self.widget_palette)

        # Center panel - Design Canvas and YAML Editor
        center_splitter = QSplitter(Qt.Vertical)

        # Design Canvas
        self.design_canvas = DesignCanvas(self.layout_engine)
        center_splitter.addWidget(self.design_canvas)

        # YAML Editor and Page Navigator (tabbed with other views)
        editor_tabs = QTabWidget()
        self.yaml_editor = YamlEditor()
        editor_tabs.addTab(self.yaml_editor, "YAML Source")

        # Add page navigator
        self.page_navigator = PageNavigator()
        editor_tabs.addTab(self.page_navigator, "Pages")

        center_splitter.addWidget(editor_tabs)

        # Set center splitter proportions
        center_splitter.setSizes([400, 200])
        main_splitter.addWidget(center_splitter)

        # Right panel - Property Editor
        self.property_editor = PropertyEditor()
        self.property_editor.setMaximumWidth(300)
        main_splitter.addWidget(self.property_editor)

        # Set main splitter proportions (more space for center, less for sides)
        main_splitter.setSizes([180, 600, 250])

        editor_layout.addWidget(main_splitter)

        return editor_widget

    def show_editor(self):
        """Show the main editor and hide dashboard."""
        self.dashboard_widget.hide()
        self.editor_widget.show()

    def show_dashboard(self):
        """Show the dashboard and hide editor."""
        self.editor_widget.hide()
        self.dashboard_widget.show()
        self.dashboard_widget.refresh_projects()

    def on_editor_mode_changed(self, mode: str):
        """Handle editor mode changes."""
        if self.design_canvas:
            self.design_canvas.set_interaction_mode(mode)
        self.statusBar().showMessage(f"Mode: {mode.title()}")

    def on_editor_tool_activated(self, tool: str):
        """Handle editor tool activation."""
        if tool == 'copy' and self.design_canvas:
            self.design_canvas.copy_selected()
        elif tool == 'paste' and self.design_canvas:
            self.design_canvas.paste()
        elif tool == 'delete' and self.design_canvas:
            self.design_canvas.delete_selected()
        elif tool == 'grid_toggle' and self.design_canvas:
            self.design_canvas.toggle_grid()
        elif tool == 'snap_toggle' and self.design_canvas:
            self.design_canvas.toggle_snap()
        elif tool == 'zoom_fit' and self.design_canvas:
            self.design_canvas.zoom_to_fit()
        elif tool.startswith('align_') and self.design_canvas:
            alignment = tool.replace('align_', '')
            self.design_canvas.align_selected(alignment)
        elif tool == 'distribute_horizontal' and self.design_canvas:
            self.design_canvas.distribute_selected('horizontal')

        self.statusBar().showMessage(f"Tool: {tool.replace('_', ' ').title()}", 2000)

    def create_menu_bar(self):
        """Create the menu bar."""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("&File")
        
        new_action = QAction("&New Project", self)
        new_action.setShortcut(QKeySequence.New)
        new_action.triggered.connect(self.new_project)
        file_menu.addAction(new_action)
        
        open_action = QAction("&Open Project", self)
        open_action.setShortcut(QKeySequence.Open)
        open_action.triggered.connect(self.open_project)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        save_action = QAction("&Save", self)
        save_action.setShortcut(QKeySequence.Save)
        save_action.triggered.connect(self.save_current)
        file_menu.addAction(save_action)
        
        save_as_action = QAction("Save &As...", self)
        save_as_action.setShortcut(QKeySequence.SaveAs)
        save_as_action.triggered.connect(self.save_as)
        file_menu.addAction(save_as_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("E&xit", self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Edit menu
        edit_menu = menubar.addMenu("&Edit")
        
        undo_action = QAction("&Undo", self)
        undo_action.setShortcut(QKeySequence.Undo)
        undo_action.triggered.connect(self.command_manager.undo)
        edit_menu.addAction(undo_action)

        redo_action = QAction("&Redo", self)
        redo_action.setShortcut(QKeySequence.Redo)
        redo_action.triggered.connect(self.command_manager.redo)
        edit_menu.addAction(redo_action)

        edit_menu.addSeparator()

        # Copy/Paste actions
        copy_action = QAction("&Copy", self)
        copy_action.setShortcut(QKeySequence.Copy)
        copy_action.triggered.connect(self.copy_selected_widgets)
        edit_menu.addAction(copy_action)

        paste_action = QAction("&Paste", self)
        paste_action.setShortcut(QKeySequence.Paste)
        paste_action.triggered.connect(self.paste_widgets)
        edit_menu.addAction(paste_action)

        edit_menu.addSeparator()

        # Select All action
        select_all_action = QAction("Select &All", self)
        select_all_action.setShortcut(QKeySequence.SelectAll)
        select_all_action.triggered.connect(self.select_all_widgets)
        edit_menu.addAction(select_all_action)
        
        # View menu
        view_menu = menubar.addMenu("&View")
        
        preview_action = QAction("&Preview", self)
        preview_action.setShortcut("F5")
        preview_action.triggered.connect(self.preview_application)
        view_menu.addAction(preview_action)
        
        # Tools menu
        tools_menu = menubar.addMenu("&Tools")
        
        validate_action = QAction("&Validate Project", self)
        validate_action.triggered.connect(self.validate_project)
        tools_menu.addAction(validate_action)
        
        export_action = QAction("&Export Application", self)
        export_action.triggered.connect(self.export_application)
        tools_menu.addAction(export_action)

        tools_menu.addSeparator()

        logic_editor_action = QAction("&Logic Graph Editor", self)
        logic_editor_action.triggered.connect(self.open_logic_editor)
        tools_menu.addAction(logic_editor_action)
        
    def create_toolbar(self):
        """Create the toolbar."""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # Add common actions to toolbar
        new_action = QAction("New", self)
        new_action.triggered.connect(self.new_project)
        toolbar.addAction(new_action)
        
        open_action = QAction("Open", self)
        open_action.triggered.connect(self.open_project)
        toolbar.addAction(open_action)
        
        save_action = QAction("Save", self)
        save_action.triggered.connect(self.save_current)
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        preview_action = QAction("Preview", self)
        preview_action.triggered.connect(self.preview_application)
        toolbar.addAction(preview_action)

        toolbar.addSeparator()

        # Grid controls
        grid_action = QAction("Toggle Grid", self)
        grid_action.triggered.connect(self.toggle_grid)
        toolbar.addAction(grid_action)

        snap_action = QAction("Toggle Snap", self)
        snap_action.triggered.connect(self.toggle_snap)
        toolbar.addAction(snap_action)
        
    def setup_connections(self):
        """Set up signal connections between components."""
        # Connect widget palette to design canvas
        if self.widget_palette and self.design_canvas:
            self.widget_palette.widget_selected.connect(self.design_canvas.add_widget)
            
        # Connect design canvas to property editor
        if self.design_canvas and self.property_editor:
            self.design_canvas.selection_changed.connect(self.property_editor.set_selected_item)
            
        # Connect property editor changes to design canvas
        if self.property_editor and self.design_canvas:
            self.property_editor.property_changed.connect(self.design_canvas.update_selected_item)
            self.property_editor.widget_deleted.connect(self.delete_selected_widget)
            
        # Connect design canvas changes to YAML editor
        if self.design_canvas and self.yaml_editor:
            self.design_canvas.layout_changed.connect(self.update_yaml_editor)
            
        # Connect YAML editor changes to design canvas
        if self.yaml_editor and self.design_canvas:
            self.yaml_editor.yaml_changed.connect(self.update_design_canvas)

        # Connect YAML editor widget selection to property editor
        if self.yaml_editor and self.property_editor:
            self.yaml_editor.widget_selected_from_yaml.connect(self.property_editor.set_selected_item)

        # Connect page navigator
        if self.page_navigator:
            self.page_navigator.page_selected.connect(self.load_page)
            self.page_navigator.page_created.connect(self.on_page_created)
            self.page_navigator.page_deleted.connect(self.on_page_deleted)
            self.page_navigator.page_renamed.connect(self.on_page_renamed)

        # Connect keyboard shortcuts
        self.setup_keyboard_shortcuts()
            
    def new_project(self):
        """Create a new project using the enhanced dialog."""
        dialog = NewProjectDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            try:
                # Create new project with default structure
                self.current_project = ArcanumProject.create(
                    dialog.project_path,
                    dialog.project_name,
                    dialog.project_description
                )
                self.current_page_path = None

                # Create helpful welcome page
                self._create_helpful_welcome_page()

                # Add to recent projects
                self.recent_manager.add_recent_project(dialog.project_path, dialog.project_name)

                # Initialize page navigator with project
                if hasattr(self, 'page_navigator') and self.page_navigator:
                    self.page_navigator.set_project(self.current_project)

                self.statusBar().showMessage(f"Created new project: {dialog.project_path}")
                self.project_changed.emit()
                self.show_editor()

            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to create project: {e}")

    def _create_helpful_welcome_page(self):
        """Create a helpful welcome page with widgets demonstrating functionality."""
        # Create a comprehensive welcome page with helpful widgets
        welcome_page = {
            'id': 'welcome_page',
            'title': 'Welcome to Arcanum',
            'layout': {
                'type': 'column',
                'spacing': 20,
                'padding': 30,
                'content': [
                    {
                        'widget': 'label',
                        'id': 'title_label',
                        'text': 'Welcome to Arcanum!',
                        'style': 'font-size: 28px; font-weight: bold; color: #2563eb; text-align: center; margin-bottom: 10px;'
                    },
                    {
                        'widget': 'label',
                        'id': 'subtitle_label',
                        'text': 'Your visual application builder is ready to use.',
                        'style': 'font-size: 16px; color: #64748b; text-align: center; margin-bottom: 20px;'
                    },
                    {
                        'widget': 'text_input',
                        'id': 'sample_input',
                        'label': 'Try typing here (live preview):',
                        'placeholder': 'Type something to test the live preview...',
                        'style': 'margin-bottom: 15px;'
                    },
                    {
                        'widget': 'button',
                        'id': 'sample_button',
                        'label': 'Click me (interactive)',
                        'style': 'background-color: #059669; color: white; padding: 10px 20px; border-radius: 5px; margin-bottom: 15px;'
                    },
                    {
                        'widget': 'label',
                        'id': 'help_title',
                        'text': 'Getting Started:',
                        'style': 'font-size: 18px; font-weight: bold; color: #374151; margin-top: 20px; margin-bottom: 10px;'
                    },
                    {
                        'widget': 'label',
                        'id': 'help_1',
                        'text': '• Select widgets from the palette on the left',
                        'style': 'color: #4b5563; margin-bottom: 5px;'
                    },
                    {
                        'widget': 'label',
                        'id': 'help_2',
                        'text': '• Drag them onto the canvas to add them',
                        'style': 'color: #4b5563; margin-bottom: 5px;'
                    },
                    {
                        'widget': 'label',
                        'id': 'help_3',
                        'text': '• Click widgets to edit their properties on the right',
                        'style': 'color: #4b5563; margin-bottom: 5px;'
                    },
                    {
                        'widget': 'label',
                        'id': 'help_4',
                        'text': '• Use the page navigator to add more pages',
                        'style': 'color: #4b5563; margin-bottom: 5px;'
                    },
                    {
                        'widget': 'label',
                        'id': 'help_5',
                        'text': '• See your changes instantly in the live preview',
                        'style': 'color: #4b5563; margin-bottom: 20px;'
                    },
                    {
                        'widget': 'label',
                        'id': 'footer_label',
                        'text': 'Happy building! 🚀',
                        'style': 'font-size: 16px; color: #059669; text-align: center; font-weight: bold; margin-top: 20px;'
                    }
                ]
            }
        }

        # Convert to YAML
        import yaml
        yaml_content = yaml.dump(welcome_page, default_flow_style=False, sort_keys=False)

        # Save to file
        project_path = self.current_project.project_path
        pages_dir = project_path / "pages"
        pages_dir.mkdir(exist_ok=True)

        welcome_page_path = pages_dir / "welcome_page.yaml"
        with open(welcome_page_path, 'w', encoding='utf-8') as f:
            f.write(yaml_content)

        # Load the page in the editor
        self.load_page(welcome_page_path)

    def _create_default_page(self):
        """Create a default page for the project."""
        if not self.current_project:
            return

        # Create default page content
        default_page = {
            "id": "welcome_page",
            "title": "Welcome",
            "layout": {
                "type": "column",
                "content": [
                    {
                        "widget": "label",
                        "label": "Welcome to Arcanum!",
                        "style": {"font-size": "24px", "text-align": "center"}
                    },
                    {
                        "widget": "button",
                        "label": "Click Me",
                        "action": "show_message"
                    }
                ]
            }
        }

        # Convert to YAML
        import yaml
        yaml_content = yaml.dump(default_page, default_flow_style=False, sort_keys=False)

        # Save to file
        project_path = self.current_project.project_path
        pages_dir = project_path / "pages"
        pages_dir.mkdir(exist_ok=True)

        welcome_page_path = pages_dir / "welcome_page.yaml"
        with open(welcome_page_path, 'w', encoding='utf-8') as f:
            f.write(yaml_content)

        self.current_page_path = welcome_page_path

        # Update editors
        if self.yaml_editor:
            self.yaml_editor.set_content(yaml_content)

        if self.design_canvas:
            self.design_canvas.load_from_yaml(yaml_content)

        # Update page navigator
        if hasattr(self, 'page_navigator') and self.page_navigator:
            self.page_navigator.refresh_page_list()
            self.page_navigator.set_current_page(welcome_page_path)

        # Set current page path for saving
        project_path = self.current_project.project_path
        pages_dir = project_path / "pages"
        pages_dir.mkdir(exist_ok=True)
        self.current_page_path = pages_dir / "main.yaml"
        
    def open_project(self):
        """Open an existing project using file dialog."""
        # Start in a sensible default location
        start_dir = str(Path.home())
        if hasattr(self, 'recent_manager'):
            recent_projects = self.recent_manager.load_recent_projects()
            if recent_projects:
                # Start in the directory of the most recent project
                recent_path = Path(recent_projects[0]['path'])
                if recent_path.parent.exists():
                    start_dir = str(recent_path.parent)

        project_dir = QFileDialog.getExistingDirectory(
            self, "Open Arcanum Project", start_dir, QFileDialog.ShowDirsOnly
        )

        if project_dir:
            self.open_project_by_path(project_dir)

    def open_project_by_path(self, project_path: str):
        """Open a project by its path with validation."""
        project_path = Path(project_path)

        # Validate project
        is_valid, error_msg = ProjectValidator.is_valid_project(project_path)
        if not is_valid:
            QMessageBox.critical(
                self, "Invalid Project",
                f"Cannot open project at '{project_path}':\n\n{error_msg}\n\n"
                "Please ensure the directory contains a valid Arcanum.yaml file."
            )
            return

        try:
            # Load project
            self.current_project = ArcanumProject(project_path)
            self.current_project.load()

            # Get project info for recent projects
            project_info = ProjectValidator.get_project_info(project_path)
            project_name = project_info['name'] if project_info else project_path.name

            # Add to recent projects
            self.recent_manager.add_recent_project(project_path, project_name)

            # Initialize page navigator with project
            if hasattr(self, 'page_navigator') and self.page_navigator:
                self.page_navigator.set_project(self.current_project)

            # Load the first page if available
            self._load_first_page()

            self.statusBar().showMessage(f"Opened project: {project_name}")
            self.project_changed.emit()
            self.show_editor()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open project: {e}")

    def _load_first_page(self):
        """Load the welcome page or first available page from the current project."""
        if not self.current_project:
            return

        # Look for pages in the project
        project_path = self.current_project.project_path
        pages_dir = project_path / "pages"

        if pages_dir.exists():
            yaml_files = list(pages_dir.glob("*.yaml")) + list(pages_dir.glob("*.yml"))
            if yaml_files:
                # Prioritize welcome page
                welcome_page = None
                for page_file in yaml_files:
                    if page_file.stem in ['welcome_page', 'welcome_screen', 'welcome']:
                        welcome_page = page_file
                        break

                # Use welcome page if found, otherwise use first page
                first_page = welcome_page if welcome_page else yaml_files[0]
                self.load_page(first_page)
        else:
            # Create a default page if no pages exist
            self._create_default_page()

    def load_page(self, page_path: Path):
        """Load a specific page into the editor."""
        if not page_path.exists():
            QMessageBox.warning(self, "Warning", f"Page file not found: {page_path}")
            return

        self.current_page_path = page_path

        # Reset property editor when changing pages
        if self.property_editor:
            self.property_editor.reset_properties()

        # Load the page content
        try:
            with open(page_path, 'r', encoding='utf-8') as f:
                yaml_content = f.read()

            # Update YAML editor
            if self.yaml_editor:
                self.yaml_editor.set_content(yaml_content)

            # Update design canvas
            if self.design_canvas:
                self.design_canvas.load_from_yaml(yaml_content)

            # Update page navigator selection
            if hasattr(self, 'page_navigator') and self.page_navigator:
                self.page_navigator.set_current_page(page_path)

            self.statusBar().showMessage(f"Loaded page: {page_path.name}")

        except Exception as e:
            QMessageBox.warning(self, "Warning", f"Failed to load page {page_path.name}: {e}")

    def on_page_created(self, page_path: Path):
        """Handle page creation from navigator."""
        self.statusBar().showMessage(f"Created new page: {page_path.name}")

        # Update page navigator if it exists
        if hasattr(self, 'page_navigator') and self.page_navigator:
            self.page_navigator.refresh_page_list()

    def on_page_deleted(self, page_path: Path):
        """Handle page deletion from navigator."""
        self.statusBar().showMessage(f"Deleted page: {page_path.name}")

        # If the deleted page was currently loaded, clear the editor
        if self.current_page_path == page_path:
            self.current_page_path = None
            if self.yaml_editor:
                self.yaml_editor.set_content("")
            if self.design_canvas:
                self.design_canvas.clear_canvas()

        # Update page navigator if it exists
        if hasattr(self, 'page_navigator') and self.page_navigator:
            self.page_navigator.refresh_page_list()

    def on_page_renamed(self, new_page_path: Path, new_name: str):
        """Handle page rename from navigator."""
        self.statusBar().showMessage(f"Renamed page to: {new_name}")

        # Update current page path if it was the renamed page
        if self.current_page_path and self.current_page_path.stem == new_page_path.stem.replace(new_name, self.current_page_path.stem):
            self.current_page_path = new_page_path

        # Update page navigator if it exists
        if hasattr(self, 'page_navigator') and self.page_navigator:
            self.page_navigator.refresh_page_list()
                
    def save_current(self):
        """Save the current page/project."""
        if self.current_project and self.current_page_path:
            try:
                # Get YAML content from editor
                yaml_content = self.yaml_editor.get_content()
                
                # Save to file
                with open(self.current_page_path, 'w') as f:
                    f.write(yaml_content)
                    
                self.statusBar().showMessage("Saved successfully")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to save: {e}")
        else:
            self.save_as()
            
    def save_project(self):
        """Save the entire project."""
        if self.current_project:
            try:
                # Save current page first
                self.save_current()

                # Save project metadata
                self.current_project.save()

                self.statusBar().showMessage("Project saved successfully")
                self.feedback_manager.set_status("success")

            except Exception as e:
                error_msg = f"Failed to save project: {e}"
                QMessageBox.critical(self, "Error", error_msg)
                self.feedback_manager.set_status("error")
        else:
            QMessageBox.warning(self, "Warning", "No project is currently open")

    def save_as(self):
        """Save the current page with a new name."""
        # TODO: Implement save as dialog
        self.statusBar().showMessage("Save as not implemented yet")
        
    def auto_save(self):
        """Auto-save the current work."""
        if self.current_project and self.current_page_path:
            try:
                self.save_current()
                self.statusBar().showMessage("Auto-saved", 2000)
            except Exception:
                pass  # Silent auto-save failure
                
    def preview_application(self):
        """Preview the current application."""
        # TODO: Implement preview functionality
        self.statusBar().showMessage("Preview not implemented yet")

    def toggle_grid(self):
        """Toggle grid visibility."""
        if hasattr(self, 'design_canvas') and self.design_canvas:
            self.design_canvas.toggle_grid()

    def toggle_snap(self):
        """Toggle snap to grid."""
        if hasattr(self, 'design_canvas') and self.design_canvas:
            self.design_canvas.toggle_snap_to_grid()
        
    def validate_project(self):
        """Validate the current project."""
        # TODO: Implement validation
        self.statusBar().showMessage("Validation not implemented yet")
        
    def export_application(self):
        """Export the application."""
        # TODO: Implement export functionality
        self.statusBar().showMessage("Export not implemented yet")

    def open_logic_editor(self):
        """Open the logic graph editor."""
        try:
            from ..logic.editor import LogicGraphEditor

            # Create logic editor window
            self.logic_editor = LogicGraphEditor()
            self.logic_editor.setWindowTitle("Arcanum Logic Graph Editor")
            self.logic_editor.resize(1200, 800)
            self.logic_editor.show()

            self.statusBar().showMessage("Logic Graph Editor opened")

        except ImportError as e:
            QMessageBox.warning(self, "Logic Editor Error",
                              f"Could not open logic editor: {e}")
        except Exception as e:
            QMessageBox.critical(self, "Error",
                               f"Failed to open logic editor: {e}")
        
    def update_yaml_editor(self, layout_data: Dict[str, Any]):
        """Update the YAML editor with new layout data."""
        if self.yaml_editor:
            self.yaml_editor.set_content(layout_data)
            
    def update_design_canvas(self, yaml_content: str):
        """Update the design canvas with new YAML content."""
        if self.design_canvas:
            self.design_canvas.load_from_yaml(yaml_content)

    def delete_selected_widget(self):
        """Delete the currently selected widget."""
        if self.design_canvas and self.design_canvas.selected_item:
            self.design_canvas.delete_widget(self.design_canvas.selected_item)

    def copy_selected_widgets(self):
        """Copy selected widgets to clipboard."""
        if self.design_canvas:
            selected_widgets = self.design_canvas.get_selected_widgets()
            if selected_widgets:
                widget_data_list = [widget.item_data for widget in selected_widgets]
                self.clipboard_manager.copy_widgets(widget_data_list)

    def paste_widgets(self):
        """Paste widgets from clipboard."""
        if self.design_canvas and self.clipboard_manager.has_clipboard_data():
            pasted_widgets = self.clipboard_manager.paste_widgets()
            for widget_data in pasted_widgets:
                self.design_canvas.add_widget_from_data(widget_data, (
                    widget_data.get('position', {}).get('x', 50),
                    widget_data.get('position', {}).get('y', 50)
                ))

    def select_all_widgets(self):
        """Select all widgets on the canvas."""
        if self.design_canvas:
            self.design_canvas.select_all_widgets()

    def show_error_dialog(self, title: str, message: str):
        """Show an error dialog."""
        ErrorDialog.show_error(self, title, message)

    def show_warning_dialog(self, title: str, message: str):
        """Show a warning dialog."""
        ErrorDialog.show_warning(self, title, message)

    def setup_keyboard_shortcuts(self):
        """Set up keyboard shortcuts for all actions."""
        # File operations
        self.shortcut_manager.connect_shortcut("new_project", self.new_project)
        self.shortcut_manager.connect_shortcut("open_project", self.open_project)
        self.shortcut_manager.connect_shortcut("save_project", self.save_project)
        self.shortcut_manager.connect_shortcut("quit", self.close)

        # Edit operations
        self.shortcut_manager.connect_shortcut("undo", self.command_manager.undo)
        self.shortcut_manager.connect_shortcut("redo", self.command_manager.redo)
        self.shortcut_manager.connect_shortcut("copy", self.copy_selected_widgets)
        self.shortcut_manager.connect_shortcut("paste", self.paste_widgets)
        self.shortcut_manager.connect_shortcut("select_all", self.select_all_widgets)
        self.shortcut_manager.connect_shortcut("delete", self.delete_selected_widget)

        # View operations
        self.shortcut_manager.connect_shortcut("preview", self.preview_application)

        # Grid operations
        self.shortcut_manager.connect_shortcut("toggle_grid", self.toggle_grid)
        self.shortcut_manager.connect_shortcut("toggle_snap", self.toggle_snap)


def main():
    """Main entry point for the GUI Builder."""
    app = QApplication(sys.argv)
    app.setApplicationName("Arcanum Builder")
    
    builder = ArcanumBuilder()
    builder.show()
    
    return app.exec_()


if __name__ == "__main__":
    sys.exit(main())

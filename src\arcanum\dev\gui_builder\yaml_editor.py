"""
YAML Editor for the Arcanum GUI Builder.
"""

import yaml
from typing import Dict, Any, Optional
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, QPushButton,
    QLabel, QMessageBox, QSplitter, QFrame, QMenu, QAction
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QTextCharFormat, QColor, QSyntaxHighlighter, QTextDocument


class YamlSyntaxHighlighter(QSyntaxHighlighter):
    """
    Simple YAML syntax highlighter.
    """
    
    def __init__(self, parent: QTextDocument):
        super().__init__(parent)
        
        # Define formats
        self.key_format = QTextCharFormat()
        self.key_format.setForeground(QColor("#0066cc"))
        self.key_format.setFontWeight(QFont.Bold)
        
        self.string_format = QTextCharFormat()
        self.string_format.setForeground(QColor("#009900"))
        
        self.comment_format = QTextCharFormat()
        self.comment_format.setForeground(QColor("#999999"))
        self.comment_format.setFontItalic(True)
        
        self.number_format = QTextCharFormat()
        self.number_format.setForeground(QColor("#ff6600"))
        
        self.boolean_format = QTextCharFormat()
        self.boolean_format.setForeground(QColor("#cc0066"))
        self.boolean_format.setFontWeight(QFont.Bold)
        
    def highlightBlock(self, text: str):
        """Highlight a block of text."""
        # Highlight comments
        if text.strip().startswith('#'):
            self.setFormat(0, len(text), self.comment_format)
            return
            
        # Highlight keys (text before colon)
        colon_index = text.find(':')
        if colon_index > 0:
            key_text = text[:colon_index].strip()
            if key_text and not key_text.startswith('-'):
                self.setFormat(0, colon_index, self.key_format)
                
        # Highlight strings (quoted text)
        for quote in ['"', "'"]:
            start = 0
            while True:
                start = text.find(quote, start)
                if start == -1:
                    break
                end = text.find(quote, start + 1)
                if end == -1:
                    break
                self.setFormat(start, end - start + 1, self.string_format)
                start = end + 1
                
        # Highlight numbers
        words = text.split()
        for word in words:
            if word.replace('.', '').replace('-', '').isdigit():
                start = text.find(word)
                if start != -1:
                    self.setFormat(start, len(word), self.number_format)
                    
        # Highlight booleans
        for boolean in ['true', 'false', 'True', 'False', 'yes', 'no', 'Yes', 'No']:
            start = 0
            while True:
                start = text.find(boolean, start)
                if start == -1:
                    break
                # Check if it's a whole word
                if (start == 0 or not text[start-1].isalnum()) and \
                   (start + len(boolean) == len(text) or not text[start + len(boolean)].isalnum()):
                    self.setFormat(start, len(boolean), self.boolean_format)
                start += len(boolean)


class EnhancedTextEdit(QTextEdit):
    """Enhanced text editor with context menu."""

    def __init__(self, parent=None):
        super().__init__(parent)

    def contextMenuEvent(self, event):
        """Create custom context menu."""
        menu = self.createStandardContextMenu()

        # Add separator
        menu.addSeparator()

        # Add custom actions
        format_action = QAction("Format YAML", self)
        format_action.triggered.connect(self.parent().format_yaml)
        menu.addAction(format_action)

        validate_action = QAction("Validate YAML", self)
        validate_action.triggered.connect(self.parent().validate_yaml)
        menu.addAction(validate_action)

        menu.addSeparator()

        # Add widget insertion actions
        insert_menu = QMenu("Insert Widget", self)

        widgets = [
            ("Button", "button"),
            ("Text Input", "text"),
            ("Label", "label"),
            ("Checkbox", "checkbox"),
            ("Dropdown", "dropdown"),
            ("Image", "image"),
        ]

        for widget_name, widget_type in widgets:
            action = QAction(widget_name, self)
            action.triggered.connect(lambda checked, wt=widget_type: self.parent().insert_widget_template(wt))
            insert_menu.addAction(action)

        menu.addMenu(insert_menu)

        # Show menu
        menu.exec_(event.globalPos())


class YamlEditor(QWidget):
    """
    YAML editor with syntax highlighting and validation.
    """

    # Signals
    yaml_changed = pyqtSignal(str)
    validation_changed = pyqtSignal(bool, str)  # is_valid, error_message
    widget_selected_from_yaml = pyqtSignal(dict)  # widget_data
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.current_content: str = ""
        self.is_valid: bool = True
        self.validation_timer = QTimer()
        self.validation_timer.setSingleShot(True)
        self.validation_timer.timeout.connect(self.validate_yaml)
        self.widgets_minimized: bool = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the YAML editor UI."""
        layout = QVBoxLayout(self)
        
        # Header with title and validation status
        header_layout = QHBoxLayout()
        
        title_label = QLabel("YAML Source")
        title_font = QFont()
        title_font.setBold(True)
        title_label.setFont(title_font)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Validation status
        self.validation_label = QLabel("✓ Valid")
        self.validation_label.setStyleSheet("color: green; font-weight: bold;")
        header_layout.addWidget(self.validation_label)
        
        # Format button
        format_button = QPushButton("Format")
        format_button.clicked.connect(self.format_yaml)
        header_layout.addWidget(format_button)

        # Minimize widgets button
        minimize_button = QPushButton("Toggle Minimize")
        minimize_button.clicked.connect(self.toggle_widget_minimization)
        header_layout.addWidget(minimize_button)
        
        layout.addLayout(header_layout)
        
        # Enhanced text editor with context menu
        self.text_edit = EnhancedTextEdit()
        self.text_edit.setFont(QFont("Consolas", 10))
        self.text_edit.textChanged.connect(self.on_text_changed)
        self.text_edit.cursorPositionChanged.connect(self.on_cursor_changed)
        
        # Add syntax highlighting
        self.highlighter = YamlSyntaxHighlighter(self.text_edit.document())
        
        layout.addWidget(self.text_edit)
        
        # Error display
        self.error_frame = QFrame()
        self.error_frame.setFrameStyle(QFrame.Box)
        self.error_frame.setStyleSheet("background-color: #ffe6e6; border: 1px solid #ff9999;")
        self.error_frame.hide()
        
        error_layout = QVBoxLayout(self.error_frame)
        self.error_label = QLabel()
        self.error_label.setWordWrap(True)
        self.error_label.setStyleSheet("color: #cc0000; padding: 8px;")
        error_layout.addWidget(self.error_label)
        
        layout.addWidget(self.error_frame)
        
        # Set default content
        self.set_default_content()
        
    def set_default_content(self):
        """Set default YAML content."""
        default_yaml = """id: new_page
title: New Page
layout:
  type: column
  content:
    - widget: label
      text: Welcome to your new page!
    - widget: button
      label: Get Started
      variant: primary
"""
        self.text_edit.setPlainText(default_yaml)
        self.current_content = default_yaml
        
    def on_text_changed(self):
        """Handle text changes."""
        self.current_content = self.text_edit.toPlainText()

        # Start validation timer (debounced)
        self.validation_timer.start(500)

        # Emit change signal
        self.yaml_changed.emit(self.current_content)

    def on_cursor_changed(self):
        """Handle cursor position changes to detect widget selection."""
        try:
            cursor = self.text_edit.textCursor()
            current_line = cursor.blockNumber()

            # Parse YAML to find widget at cursor position
            widget_data = self.find_widget_at_line(current_line)
            if widget_data:
                self.widget_selected_from_yaml.emit(widget_data)
        except Exception:
            # Ignore errors during cursor tracking
            pass
        
    def validate_yaml(self):
        """Validate the current YAML content."""
        try:
            yaml.safe_load(self.current_content)
            self.set_validation_status(True, "")
        except yaml.YAMLError as e:
            error_msg = str(e)
            self.set_validation_status(False, error_msg)
        except Exception as e:
            error_msg = f"Unexpected error: {e}"
            self.set_validation_status(False, error_msg)
            
    def set_validation_status(self, is_valid: bool, error_message: str):
        """Set the validation status."""
        self.is_valid = is_valid
        
        if is_valid:
            self.validation_label.setText("✓ Valid")
            self.validation_label.setStyleSheet("color: green; font-weight: bold;")
            self.error_frame.hide()
        else:
            self.validation_label.setText("✗ Invalid")
            self.validation_label.setStyleSheet("color: red; font-weight: bold;")
            self.error_label.setText(f"Error: {error_message}")
            self.error_frame.show()
            
        self.validation_changed.emit(is_valid, error_message)
        
    def format_yaml(self):
        """Format the YAML content."""
        try:
            # Parse and reformat
            data = yaml.safe_load(self.current_content)
            formatted_yaml = yaml.dump(data, default_flow_style=False, indent=2, sort_keys=False)
            
            # Update editor
            cursor_pos = self.text_edit.textCursor().position()
            self.text_edit.setPlainText(formatted_yaml)
            
            # Try to restore cursor position
            cursor = self.text_edit.textCursor()
            cursor.setPosition(min(cursor_pos, len(formatted_yaml)))
            self.text_edit.setTextCursor(cursor)
            
        except yaml.YAMLError as e:
            QMessageBox.warning(self, "Format Error", f"Cannot format invalid YAML: {e}")
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to format YAML: {e}")
            
    def get_content(self) -> str:
        """Get the current YAML content."""
        return self.current_content
        
    def set_content(self, content: Any):
        """Set the YAML content."""
        if isinstance(content, dict):
            # Convert dict to YAML string
            yaml_str = yaml.dump(content, default_flow_style=False, indent=2, sort_keys=False)
        else:
            yaml_str = str(content)
            
        self.text_edit.setPlainText(yaml_str)
        self.current_content = yaml_str
        
    def get_parsed_content(self) -> Optional[Dict[str, Any]]:
        """Get the parsed YAML content as a dictionary."""
        if not self.is_valid:
            return None
            
        try:
            return yaml.safe_load(self.current_content)
        except Exception:
            return None
            
    def insert_text(self, text: str):
        """Insert text at the current cursor position."""
        cursor = self.text_edit.textCursor()
        cursor.insertText(text)
        
    def clear_content(self):
        """Clear the editor content."""
        self.text_edit.clear()
        self.current_content = ""

    def find_widget_at_line(self, line_number: int) -> Optional[Dict[str, Any]]:
        """Find the widget definition that contains the given line number."""
        try:
            import yaml
            data = yaml.safe_load(self.current_content)
            if not data or 'layout' not in data:
                return None

            content = data['layout'].get('content', [])
            lines = self.current_content.split('\n')

            # Find widget blocks by scanning for 'widget:' lines
            widget_blocks = []
            for i, line in enumerate(lines):
                stripped = line.strip()
                if stripped.startswith('- widget:') or (stripped.startswith('widget:') and ':' in stripped):
                    # Found a widget definition, find its extent
                    widget_start = i
                    widget_end = i
                    base_indent = len(line) - len(line.lstrip())

                    # Find the end of this widget block
                    for j in range(i + 1, len(lines)):
                        next_line = lines[j]
                        if next_line.strip() == '':
                            continue
                        next_indent = len(next_line) - len(next_line.lstrip())

                        # If we hit a line with same or less indentation that starts a new item
                        if (next_indent <= base_indent and
                            (next_line.strip().startswith('- ') or
                             next_line.strip().startswith('widget:') or
                             next_line.strip().startswith('layout:'))):
                            break
                        widget_end = j

                    widget_blocks.append((widget_start, widget_end, i))

            # Find which widget block contains our line
            for start, end, widget_index in widget_blocks:
                if start <= line_number <= end:
                    # Extract widget data from the content list
                    if widget_index < len(content):
                        widget_data = content[widget_index]
                        if isinstance(widget_data, dict) and 'widget' in widget_data:
                            return widget_data
                    break

            return None
        except Exception:
            return None

    def toggle_widget_minimization(self):
        """Toggle between minimized and expanded widget view."""
        self.widgets_minimized = not self.widgets_minimized

        if self.widgets_minimized:
            self.minimize_widgets()
        else:
            self.expand_widgets()

    def minimize_widgets(self):
        """Minimize widgets to show only widget type and id."""
        try:
            import yaml
            data = yaml.safe_load(self.current_content)
            if not data or 'layout' not in data:
                return

            content = data['layout'].get('content', [])
            minimized_content = []

            for widget in content:
                if isinstance(widget, dict) and 'widget' in widget:
                    widget_type = widget.get('widget', 'unknown')
                    widget_id = widget.get('id', 'no_id')
                    minimized_widget = {
                        'widget': widget_type,
                        'id': widget_id,
                        '_minimized': True
                    }
                    minimized_content.append(minimized_widget)
                else:
                    minimized_content.append(widget)

            data['layout']['content'] = minimized_content
            minimized_yaml = yaml.dump(data, default_flow_style=False, sort_keys=False)

            # Update editor content
            self.text_edit.setPlainText(minimized_yaml)
            self.current_content = minimized_yaml

        except Exception as e:
            QMessageBox.warning(self, "Minimization Error", f"Could not minimize widgets: {e}")

    def expand_widgets(self):
        """Restore widgets from minimized state."""
        # For now, just emit yaml_changed to trigger a refresh from the design canvas
        # In a full implementation, we'd store the original widget data
        self.yaml_changed.emit(self.current_content)

    def insert_widget_template(self, widget_type: str):
        """Insert a widget template at the cursor position."""
        templates = {
            "button": """    - widget: button
      label: "Click Me"
      variant: primary
      action:
        type: show_message
        message: "Button clicked!"
""",
            "text": """    - widget: text
      placeholder: "Enter text here..."
      required: false
      max_length: 100
""",
            "label": """    - widget: label
      text: "Your text here"
      style: "font-size: 16px;"
""",
            "checkbox": """    - widget: checkbox
      label: "Check me"
      checked: false
""",
            "dropdown": """    - widget: dropdown
      label: "Select option"
      options:
        - "Option 1"
        - "Option 2"
        - "Option 3"
""",
            "image": """    - widget: image
      src: "path/to/image.png"
      alt: "Image description"
      width: 200
      height: 150
""",
        }

        template = templates.get(widget_type, "")
        if template:
            cursor = self.text_edit.textCursor()
            cursor.insertText(template)

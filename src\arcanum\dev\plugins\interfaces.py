"""
Plugin interfaces for extending Arcanum functionality.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Type
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class PluginInterface(ABC):
    """Base interface for all Arcanum plugins."""
    
    def __init__(self):
        """Initialize plugin."""
        self.name = ""
        self.version = ""
        self.enabled = True
        self.config = {}
    
    @abstractmethod
    def initialize(self, config: Dict[str, Any]) -> bool:
        """
        Initialize the plugin with configuration.
        
        Args:
            config: Plugin configuration dictionary.
            
        Returns:
            True if initialization successful.
        """
        pass
    
    def cleanup(self):
        """Clean up plugin resources."""
        pass
    
    def get_info(self) -> Dict[str, Any]:
        """Get plugin information."""
        return {
            "name": self.name,
            "version": self.version,
            "enabled": self.enabled
        }


class WidgetPlugin(PluginInterface):
    """Interface for plugins that provide custom widgets."""
    
    @abstractmethod
    def get_widget_definitions(self) -> List[Dict[str, Any]]:
        """
        Get widget definitions provided by this plugin.
        
        Returns:
            List of widget definition dictionaries.
        """
        pass
    
    @abstractmethod
    def create_widget(self, widget_type: str, config: Dict[str, Any]) -> Any:
        """
        Create a widget instance.
        
        Args:
            widget_type: Type of widget to create.
            config: Widget configuration.
            
        Returns:
            Widget instance.
        """
        pass
    
    def get_widget_schema(self, widget_type: str) -> Optional[Dict[str, Any]]:
        """
        Get schema for a specific widget type.
        
        Args:
            widget_type: Widget type name.
            
        Returns:
            Widget schema dictionary or None.
        """
        return None


class RendererPlugin(PluginInterface):
    """Interface for plugins that provide custom renderers."""
    
    @abstractmethod
    def get_supported_targets(self) -> List[str]:
        """
        Get list of supported render targets.
        
        Returns:
            List of target names (e.g., ['custom_web', 'mobile']).
        """
        pass
    
    @abstractmethod
    def create_renderer(self, target: str, config: Dict[str, Any]) -> Any:
        """
        Create a renderer instance.
        
        Args:
            target: Render target name.
            config: Renderer configuration.
            
        Returns:
            Renderer instance.
        """
        pass
    
    @abstractmethod
    def render_layout(self, layout: Dict[str, Any], target: str) -> Any:
        """
        Render a layout for the specified target.
        
        Args:
            layout: Layout definition.
            target: Render target.
            
        Returns:
            Rendered output.
        """
        pass


class LogicNodePlugin(PluginInterface):
    """Interface for plugins that provide custom logic nodes."""
    
    @abstractmethod
    def get_node_types(self) -> List[Dict[str, Any]]:
        """
        Get logic node types provided by this plugin.
        
        Returns:
            List of node type definitions.
        """
        pass
    
    @abstractmethod
    def create_node(self, node_type: str, config: Dict[str, Any]) -> Any:
        """
        Create a logic node instance.
        
        Args:
            node_type: Type of node to create.
            config: Node configuration.
            
        Returns:
            Node instance.
        """
        pass
    
    @abstractmethod
    def execute_node(self, node: Any, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a logic node.
        
        Args:
            node: Node instance.
            inputs: Input values.
            
        Returns:
            Output values.
        """
        pass


class CommandPlugin(PluginInterface):
    """Interface for plugins that provide CLI commands."""
    
    @abstractmethod
    def get_commands(self) -> List[Dict[str, Any]]:
        """
        Get CLI commands provided by this plugin.
        
        Returns:
            List of command definitions.
        """
        pass
    
    @abstractmethod
    def execute_command(self, command: str, args: List[str], kwargs: Dict[str, Any]) -> int:
        """
        Execute a CLI command.
        
        Args:
            command: Command name.
            args: Command arguments.
            kwargs: Command options.
            
        Returns:
            Exit code (0 for success).
        """
        pass


class ThemePlugin(PluginInterface):
    """Interface for plugins that provide custom themes."""
    
    @abstractmethod
    def get_themes(self) -> List[Dict[str, Any]]:
        """
        Get themes provided by this plugin.
        
        Returns:
            List of theme definitions.
        """
        pass
    
    @abstractmethod
    def apply_theme(self, theme_name: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply a theme and return theme configuration.
        
        Args:
            theme_name: Name of theme to apply.
            config: Base configuration.
            
        Returns:
            Updated configuration with theme applied.
        """
        pass


class ExportPlugin(PluginInterface):
    """Interface for plugins that provide export functionality."""
    
    @abstractmethod
    def get_export_targets(self) -> List[str]:
        """
        Get export targets supported by this plugin.
        
        Returns:
            List of export target names.
        """
        pass
    
    @abstractmethod
    def export_project(self, project_path: Path, target: str, output_path: Path, config: Dict[str, Any]) -> bool:
        """
        Export a project to the specified target.
        
        Args:
            project_path: Path to Arcanum project.
            target: Export target name.
            output_path: Output directory.
            config: Export configuration.
            
        Returns:
            True if export successful.
        """
        pass


class LayoutPlugin(PluginInterface):
    """Interface for plugins that provide custom layout types."""
    
    @abstractmethod
    def get_layout_types(self) -> List[str]:
        """
        Get layout types provided by this plugin.
        
        Returns:
            List of layout type names.
        """
        pass
    
    @abstractmethod
    def create_layout(self, layout_type: str, config: Dict[str, Any]) -> Any:
        """
        Create a layout instance.
        
        Args:
            layout_type: Type of layout to create.
            config: Layout configuration.
            
        Returns:
            Layout instance.
        """
        pass
    
    @abstractmethod
    def render_layout(self, layout: Any, renderer: Any) -> Any:
        """
        Render a layout using the specified renderer.
        
        Args:
            layout: Layout instance.
            renderer: Renderer to use.
            
        Returns:
            Rendered layout.
        """
        pass


# Plugin capability type mapping
PLUGIN_INTERFACES = {
    "widget": WidgetPlugin,
    "renderer": RendererPlugin,
    "logic_node": LogicNodePlugin,
    "command": CommandPlugin,
    "theme": ThemePlugin,
    "export": ExportPlugin,
    "layout": LayoutPlugin
}


def get_plugin_interface(capability_type: str) -> Optional[Type[PluginInterface]]:
    """
    Get the plugin interface class for a capability type.
    
    Args:
        capability_type: Type of capability.
        
    Returns:
        Plugin interface class or None.
    """
    return PLUGIN_INTERFACES.get(capability_type)


def validate_plugin_interface(plugin_instance: Any, capability_type: str) -> bool:
    """
    Validate that a plugin instance implements the required interface.
    
    Args:
        plugin_instance: Plugin instance to validate.
        capability_type: Expected capability type.
        
    Returns:
        True if plugin implements the interface.
    """
    interface_class = get_plugin_interface(capability_type)
    if not interface_class:
        return False
    
    return isinstance(plugin_instance, interface_class)


# Example plugin implementations for reference
class ExampleWidgetPlugin(WidgetPlugin):
    """Example widget plugin implementation."""
    
    def __init__(self):
        super().__init__()
        self.name = "example_widget"
        self.version = "1.0.0"
    
    def initialize(self, config: Dict[str, Any]) -> bool:
        """Initialize the plugin."""
        self.config = config
        return True
    
    def get_widget_definitions(self) -> List[Dict[str, Any]]:
        """Get widget definitions."""
        return [
            {
                "type": "custom_button",
                "name": "Custom Button",
                "description": "A custom button widget",
                "properties": {
                    "text": {"type": "string", "default": "Click me"},
                    "color": {"type": "string", "default": "#007bff"}
                }
            }
        ]
    
    def create_widget(self, widget_type: str, config: Dict[str, Any]) -> Any:
        """Create widget instance."""
        if widget_type == "custom_button":
            return CustomButton(config)
        return None


class CustomButton:
    """Example custom button widget."""
    
    def __init__(self, config: Dict[str, Any]):
        self.text = config.get("text", "Click me")
        self.color = config.get("color", "#007bff")
    
    def render(self, renderer: Any) -> str:
        """Render the button."""
        return f'<button style="background-color: {self.color}">{self.text}</button>'

#!/usr/bin/env python3
"""
Arcanum - Modular, <PERSON><PERSON>a-Driven, Offline-First Application Builder
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="arcanum",
    version="1.0.1",
    author="Arcanum Development Team",
    author_email="<EMAIL>",
    description="Modular, schema-driven, offline-first application builder",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/arcanum/arcanum",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Software Development :: Libraries :: Application Frameworks",
        "Topic :: Software Development :: User Interfaces",
    ],
    python_requires=">=3.10",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
            "pre-commit>=3.0.0",
        ],
        "gui": [
            "PyQt5>=5.15.0",
            "PyQt6>=6.4.0",
        ],
        "web": [
            "fastapi>=0.100.0",
            "uvicorn>=0.23.0",
            "jinja2>=3.1.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "arcanum=arcanum.cli.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "arcanum": [
            "templates/**/*",
            "themes/**/*",
            "widgets/**/*",
            "schemas/**/*",
        ],
    },
)

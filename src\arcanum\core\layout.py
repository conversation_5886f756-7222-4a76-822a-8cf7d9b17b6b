"""
Layout engine for processing and rendering Arcanum layouts.
"""

import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from pydantic import ValidationError

from .registry import WidgetRegistry
from ..schemas.layout import (
    PageLayout, BaseLayout, LayoutType, LAYOUT_TYPE_MAP
)
from ..schemas.widgets import BaseWidget


class LayoutEngine:
    """
    Processes layout definitions and manages page rendering.
    """
    
    def __init__(self, widget_registry: Optional[WidgetRegistry] = None):
        """
        Initialize layout engine.
        
        Args:
            widget_registry: Widget registry for widget creation.
        """
        self.widget_registry = widget_registry or WidgetRegistry()
        self._pages: Dict[str, PageLayout] = {}
        self._layout_cache: Dict[str, Any] = {}
    
    def load_page(self, page_path: Path) -> PageLayout:
        """
        Load a page layout from YAML file.
        
        Args:
            page_path: Path to page YAML file.
            
        Returns:
            Loaded page layout.
            
        Raises:
            FileNotFoundError: If page file doesn't exist.
            ValidationError: If page validation fails.
        """
        if not page_path.exists():
            raise FileNotFoundError(f"Page file not found: {page_path}")
        
        try:
            with open(page_path, 'r', encoding='utf-8') as f:
                page_data = yaml.safe_load(f)
            
            if not page_data:
                raise ValueError("Empty page file")
            
            # Process layout data
            page_data = self._process_layout_data(page_data)
            
            # Create page layout
            page_layout = PageLayout(**page_data)
            
            # Cache the page
            self._pages[page_layout.id] = page_layout
            
            return page_layout
            
        except yaml.YAMLError as e:
            raise yaml.YAMLError(f"Failed to parse page YAML: {e}")
        except ValidationError as e:
            raise ValidationError(f"Page validation failed: {e}")
    
    def _process_layout_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process raw layout data to ensure proper structure.
        
        Args:
            data: Raw layout data from YAML.
            
        Returns:
            Processed layout data.
        """
        # Ensure layout is properly structured
        if "layout" in data and isinstance(data["layout"], dict):
            data["layout"] = self._process_layout_node(data["layout"])
        
        return data
    
    def _process_layout_node(self, layout_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single layout node recursively.
        
        Args:
            layout_data: Layout node data.
            
        Returns:
            Processed layout node.
        """
        # Process content recursively
        if "content" in layout_data and isinstance(layout_data["content"], list):
            processed_content = []
            
            for item in layout_data["content"]:
                if isinstance(item, dict):
                    if "widget" in item:
                        # This is a widget
                        processed_content.append(item)
                    elif "type" in item:
                        # This is a nested layout
                        processed_content.append(self._process_layout_node(item))
                    else:
                        # Unknown item type
                        processed_content.append(item)
                else:
                    processed_content.append(item)
            
            layout_data["content"] = processed_content
        
        # Process tab content
        if "tabs" in layout_data and isinstance(layout_data["tabs"], list):
            for tab in layout_data["tabs"]:
                if "content" in tab:
                    tab["content"] = [
                        self._process_layout_node(item) if isinstance(item, dict) and "type" in item else item
                        for item in tab["content"]
                    ]
        
        # Process accordion content
        if "items" in layout_data and isinstance(layout_data["items"], list):
            for item in layout_data["items"]:
                if "content" in item:
                    item["content"] = [
                        self._process_layout_node(content_item) if isinstance(content_item, dict) and "type" in content_item else content_item
                        for content_item in item["content"]
                    ]
        
        return layout_data
    
    def create_layout(self, layout_data: Dict[str, Any]) -> BaseLayout:
        """
        Create a layout instance from data.
        
        Args:
            layout_data: Layout configuration data.
            
        Returns:
            Layout instance.
            
        Raises:
            ValueError: If layout type is unknown or data is invalid.
        """
        layout_type_str = layout_data.get("type")
        if not layout_type_str:
            raise ValueError("Layout type not specified")
        
        try:
            layout_type = LayoutType(layout_type_str)
        except ValueError:
            raise ValueError(f"Unknown layout type: {layout_type_str}")
        
        layout_class = LAYOUT_TYPE_MAP.get(layout_type)
        if not layout_class:
            raise ValueError(f"No layout class registered for type: {layout_type}")
        
        try:
            return layout_class(**layout_data)
        except ValidationError as e:
            raise ValueError(f"Layout validation failed: {e}")
    
    def extract_widgets(self, layout: Union[BaseLayout, PageLayout]) -> List[BaseWidget]:
        """
        Extract all widgets from a layout recursively.
        
        Args:
            layout: Layout to extract widgets from.
            
        Returns:
            List of widget instances.
        """
        widgets = []
        
        if isinstance(layout, PageLayout):
            layout = layout.layout
        
        def extract_from_content(content: List[Any]) -> None:
            """Recursively extract widgets from content."""
            for item in content:
                if isinstance(item, dict):
                    if "widget" in item:
                        # This is a widget
                        try:
                            widget = self.widget_registry.create_widget(item)
                            widgets.append(widget)
                        except ValueError as e:
                            print(f"Warning: Failed to create widget: {e}")
                    elif "type" in item:
                        # This is a nested layout
                        try:
                            nested_layout = self.create_layout(item)
                            widgets.extend(self.extract_widgets(nested_layout))
                        except ValueError as e:
                            print(f"Warning: Failed to create nested layout: {e}")
                elif isinstance(item, BaseLayout):
                    widgets.extend(self.extract_widgets(item))
        
        # Extract from main content
        if hasattr(layout, 'content'):
            extract_from_content(layout.content)
        
        # Extract from tabs
        if hasattr(layout, 'tabs'):
            for tab in layout.tabs:
                extract_from_content(tab.content)
        
        # Extract from accordion items
        if hasattr(layout, 'items'):
            for item in layout.items:
                extract_from_content(item.content)
        
        return widgets
    
    def validate_layout(self, layout_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate layout data and return validation results.
        
        Args:
            layout_data: Layout data to validate.
            
        Returns:
            Validation results.
        """
        results = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "widget_count": 0,
            "layout_count": 0
        }
        
        try:
            # Try to create the layout
            if "layout" in layout_data:
                layout = self.create_layout(layout_data["layout"])
                results["layout_count"] = 1
                
                # Extract and validate widgets
                widgets = self.extract_widgets(layout)
                results["widget_count"] = len(widgets)
                
            elif "type" in layout_data:
                layout = self.create_layout(layout_data)
                widgets = self.extract_widgets(layout)
                results["widget_count"] = len(widgets)
                results["layout_count"] = 1
            else:
                # Try as page layout
                page = PageLayout(**layout_data)
                widgets = self.extract_widgets(page)
                results["widget_count"] = len(widgets)
                results["layout_count"] = 1
                
        except (ValueError, ValidationError) as e:
            results["valid"] = False
            results["errors"].append(str(e))
        
        return results
    
    def get_page(self, page_id: str) -> Optional[PageLayout]:
        """
        Get a cached page by ID.
        
        Args:
            page_id: Page identifier.
            
        Returns:
            Page layout or None if not found.
        """
        return self._pages.get(page_id)
    
    def get_all_pages(self) -> Dict[str, PageLayout]:
        """Get all cached pages."""
        return self._pages.copy()
    
    def clear_cache(self) -> None:
        """Clear the layout cache."""
        self._pages.clear()
        self._layout_cache.clear()

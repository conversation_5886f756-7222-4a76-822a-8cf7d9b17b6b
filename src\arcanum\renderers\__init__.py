"""
Rendering backends for different platforms (PyQt, Web, Headless).
"""

from .base import BaseRenderer, RenderContext, RendererError, RenderingError, StyleError

try:
    from .qt_renderer import QtRenderer
    QT_AVAILABLE = True
except ImportError:
    QT_AVAILABLE = False

__all__ = [
    "BaseRenderer",
    "RenderContext",
    "RendererError",
    "RenderingError",
    "StyleError",
]

if QT_AVAILABLE:
    __all__.append("QtRenderer")

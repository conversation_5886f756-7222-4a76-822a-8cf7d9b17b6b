# Welcome Screen Layout
id: welcome_screen
title: "Welcome to {{ app_name }}"
description: "Main welcome screen for the application"

layout:
  type: column
  style:
    spacing: 20
    padding: 40
    background_color: "#f8fafc"
  
  content:
    - widget: image
      id: app_logo
      src: "/ui/assets/logo.png"
      alt: "Application Logo"
      style:
        width: 200
        height: 100
        margin: "0 auto"
      
    - widget: label
      id: welcome_title
      text: "Welcome to {{ app_name }}!"
      style: "font-size: 28px; font-weight: bold; text-align: center; color: #1e293b; margin-bottom: 10px;"
      
    - widget: label
      id: welcome_subtitle
      text: "Get started by exploring the features below or creating your first layout."
      style: "text-align: center; color: #64748b; font-size: 16px; margin-bottom: 30px;"
      
    - type: row
      style:
        justify_content: center
        spacing: 15
      content:
        - widget: button
          id: get_started_btn
          label: "Get Started"
          variant: "primary"
          action:
            type: link_to
            target: "pages/getting_started.yaml"
          style: "padding: 12px 24px; font-size: 16px;"
          
        - widget: button
          id: examples_btn
          label: "View Examples"
          variant: "secondary"
          action:
            type: link_to
            target: "pages/examples.yaml"
          style: "padding: 12px 24px; font-size: 16px;"
    
    - type: grid
      style:
        columns: 3
        gap: 20
        margin_top: 40
      content:
        - type: column
          style:
            padding: 20
            background_color: "#ffffff"
            border_radius: "8px"
            border: "1px solid #e2e8f0"
          content:
            - widget: label
              text: "🎨 Design"
              style: "font-size: 20px; font-weight: bold; margin-bottom: 10px;"
            - widget: label
              text: "Create beautiful layouts with our drag-and-drop designer"
              style: "color: #64748b; line-height: 1.5;"
        
        - type: column
          style:
            padding: 20
            background_color: "#ffffff"
            border_radius: "8px"
            border: "1px solid #e2e8f0"
          content:
            - widget: label
              text: "⚡ Logic"
              style: "font-size: 20px; font-weight: bold; margin-bottom: 10px;"
            - widget: label
              text: "Add interactive behavior with visual logic graphs"
              style: "color: #64748b; line-height: 1.5;"
        
        - type: column
          style:
            padding: 20
            background_color: "#ffffff"
            border_radius: "8px"
            border: "1px solid #e2e8f0"
          content:
            - widget: label
              text: "🚀 Export"
              style: "font-size: 20px; font-weight: bold; margin-bottom: 10px;"
            - widget: label
              text: "Deploy to web, desktop, or mobile platforms"
              style: "color: #64748b; line-height: 1.5;"

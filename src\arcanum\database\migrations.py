"""
Database migration system for Arcanum.
"""

import time
import logging
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


@dataclass
class Migration:
    """Represents a database migration."""
    name: str
    version: str
    description: str = ""
    up_sql: List[str] = field(default_factory=list)
    down_sql: List[str] = field(default_factory=list)
    up_function: Optional[Callable] = None
    down_function: Optional[Callable] = None
    dependencies: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        # Validation will be done when migration is executed
        pass

    def validate(self):
        """Validate that migration has required components."""
        if not self.up_sql and not self.up_function:
            raise ValueError("Migration must have either up_sql or up_function")
            
    def add_up_sql(self, sql: str):
        """Add SQL for the up migration."""
        self.up_sql.append(sql)
        
    def add_down_sql(self, sql: str):
        """Add SQL for the down migration."""
        self.down_sql.append(sql)
        
    def set_up_function(self, func: Callable):
        """Set the up migration function."""
        self.up_function = func
        
    def set_down_function(self, func: Callable):
        """Set the down migration function."""
        self.down_function = func


class MigrationManager:
    """Manages database migrations."""
    
    def __init__(self, database_manager):
        self.db = database_manager
        self.migrations: Dict[str, Migration] = {}
        self.migration_table = "arcanum_migrations"
        
    def initialize(self):
        """Initialize the migration system."""
        if not self.db.adapter.table_exists(self.migration_table):
            self._create_migration_table()
            
    def _create_migration_table(self):
        """Create the migrations tracking table."""
        create_sql = f"""
        CREATE TABLE {self.migration_table} (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL UNIQUE,
            version TEXT NOT NULL,
            description TEXT,
            applied_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            execution_time REAL
        )
        """
        
        try:
            self.db.adapter.execute(create_sql)
            logger.info(f"Created migration table: {self.migration_table}")
        except Exception as e:
            logger.error(f"Failed to create migration table: {e}")
            raise
            
    def register_migration(self, migration: Migration):
        """Register a migration."""
        if migration.name in self.migrations:
            raise ValueError(f"Migration '{migration.name}' already registered")
            
        self.migrations[migration.name] = migration
        logger.info(f"Registered migration: {migration.name} (v{migration.version})")
        
    def create_migration(self, name: str, version: str, description: str = "") -> Migration:
        """Create a new migration."""
        migration = Migration(
            name=name,
            version=version,
            description=description
        )
        self.register_migration(migration)
        return migration
        
    def get_applied_migrations(self) -> List[Dict[str, Any]]:
        """Get list of applied migrations."""
        if not self.db.adapter.table_exists(self.migration_table):
            return []
            
        try:
            query = f"SELECT * FROM {self.migration_table} ORDER BY applied_at"
            return self.db.adapter.fetch_all(query)
        except Exception as e:
            logger.error(f"Failed to get applied migrations: {e}")
            return []
            
    def get_pending_migrations(self) -> List[Migration]:
        """Get list of pending migrations."""
        applied = {m['name'] for m in self.get_applied_migrations()}
        pending = []
        
        for name, migration in self.migrations.items():
            if name not in applied:
                # Check dependencies
                if self._dependencies_satisfied(migration, applied):
                    pending.append(migration)
                    
        # Sort by version
        pending.sort(key=lambda m: m.version)
        return pending
        
    def _dependencies_satisfied(self, migration: Migration, applied: set) -> bool:
        """Check if migration dependencies are satisfied."""
        for dep in migration.dependencies:
            if dep not in applied:
                return False
        return True
        
    def migrate_up(self, target_version: str = None) -> bool:
        """Run pending migrations up to target version."""
        if not self.db.is_connected():
            raise RuntimeError("Database not connected")
            
        pending = self.get_pending_migrations()
        
        if target_version:
            # Filter to target version
            pending = [m for m in pending if m.version <= target_version]
            
        if not pending:
            logger.info("No pending migrations")
            return True
            
        logger.info(f"Running {len(pending)} migrations")
        
        success = True
        for migration in pending:
            if not self._apply_migration(migration):
                success = False
                break
                
        return success
        
    def migrate_down(self, target_version: str) -> bool:
        """Rollback migrations to target version."""
        if not self.db.is_connected():
            raise RuntimeError("Database not connected")
            
        applied = self.get_applied_migrations()
        
        # Find migrations to rollback (in reverse order)
        to_rollback = []
        for migration_record in reversed(applied):
            if migration_record['version'] > target_version:
                migration_name = migration_record['name']
                if migration_name in self.migrations:
                    to_rollback.append(self.migrations[migration_name])
                else:
                    logger.warning(f"Migration '{migration_name}' not found in registry")
                    
        if not to_rollback:
            logger.info("No migrations to rollback")
            return True
            
        logger.info(f"Rolling back {len(to_rollback)} migrations")
        
        success = True
        for migration in to_rollback:
            if not self._rollback_migration(migration):
                success = False
                break
                
        return success
        
    def _apply_migration(self, migration: Migration) -> bool:
        """Apply a single migration."""
        logger.info(f"Applying migration: {migration.name} (v{migration.version})")

        # Validate migration before applying
        try:
            migration.validate()
        except ValueError as e:
            logger.error(f"Migration validation failed: {e}")
            return False

        start_time = time.time()

        try:
            with self.db.transaction():
                # Execute SQL statements
                for sql in migration.up_sql:
                    if sql.strip():
                        logger.debug(f"Executing: {sql}")
                        self.db.adapter.execute(sql)
                        
                # Execute function if provided
                if migration.up_function:
                    logger.debug("Executing up function")
                    migration.up_function(self.db)
                    
                # Record migration
                execution_time = time.time() - start_time
                self._record_migration(migration, execution_time)
                
            logger.info(f"Migration '{migration.name}' applied successfully")
            return True
            
        except Exception as e:
            logger.error(f"Migration '{migration.name}' failed: {e}")
            return False
            
    def _rollback_migration(self, migration: Migration) -> bool:
        """Rollback a single migration."""
        logger.info(f"Rolling back migration: {migration.name} (v{migration.version})")
        
        try:
            with self.db.transaction():
                # Execute down SQL statements
                for sql in migration.down_sql:
                    if sql.strip():
                        logger.debug(f"Executing: {sql}")
                        self.db.adapter.execute(sql)
                        
                # Execute down function if provided
                if migration.down_function:
                    logger.debug("Executing down function")
                    migration.down_function(self.db)
                    
                # Remove migration record
                self._remove_migration_record(migration)
                
            logger.info(f"Migration '{migration.name}' rolled back successfully")
            return True
            
        except Exception as e:
            logger.error(f"Migration rollback '{migration.name}' failed: {e}")
            return False
            
    def _record_migration(self, migration: Migration, execution_time: float):
        """Record a migration as applied."""
        insert_sql = f"""
        INSERT INTO {self.migration_table} 
        (name, version, description, execution_time)
        VALUES (?, ?, ?, ?)
        """
        
        self.db.adapter.execute(insert_sql, (
            migration.name,
            migration.version,
            migration.description,
            execution_time
        ))
        
    def _remove_migration_record(self, migration: Migration):
        """Remove a migration record."""
        delete_sql = f"DELETE FROM {self.migration_table} WHERE name = ?"
        self.db.adapter.execute(delete_sql, (migration.name,))
        
    def get_migration_status(self) -> Dict[str, Any]:
        """Get migration status information."""
        applied = self.get_applied_migrations()
        pending = self.get_pending_migrations()
        
        return {
            "total_migrations": len(self.migrations),
            "applied_count": len(applied),
            "pending_count": len(pending),
            "applied_migrations": applied,
            "pending_migrations": [
                {
                    "name": m.name,
                    "version": m.version,
                    "description": m.description
                }
                for m in pending
            ],
            "current_version": applied[-1]['version'] if applied else None
        }
        
    def reset_migrations(self) -> bool:
        """Reset all migrations (rollback everything)."""
        logger.warning("Resetting all migrations - this will rollback all changes!")
        
        applied = self.get_applied_migrations()
        
        success = True
        for migration_record in reversed(applied):
            migration_name = migration_record['name']
            if migration_name in self.migrations:
                migration = self.migrations[migration_name]
                if not self._rollback_migration(migration):
                    success = False
                    break
            else:
                # Migration not in registry, just remove record
                logger.warning(f"Removing orphaned migration record: {migration_name}")
                delete_sql = f"DELETE FROM {self.migration_table} WHERE name = ?"
                self.db.adapter.execute(delete_sql, (migration_name,))
                
        return success
        
    def create_standard_migrations(self):
        """Create standard migrations for Arcanum applications."""
        # Migration 1: Create users table
        users_migration = self.create_migration(
            "001_create_users",
            "1.0.0",
            "Create users table"
        )
        
        users_migration.add_up_sql("""
        CREATE TABLE users (
            id INTEGER PRIMARY KEY,
            username TEXT NOT NULL UNIQUE,
            email TEXT NOT NULL UNIQUE,
            password_hash TEXT NOT NULL,
            first_name TEXT,
            last_name TEXT,
            is_active BOOLEAN DEFAULT 1,
            last_login DATETIME,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        users_migration.add_up_sql("""
        CREATE UNIQUE INDEX idx_users_username ON users(username)
        """)
        
        users_migration.add_up_sql("""
        CREATE UNIQUE INDEX idx_users_email ON users(email)
        """)
        
        users_migration.add_down_sql("DROP TABLE IF EXISTS users")
        
        # Migration 2: Create form submissions table
        forms_migration = self.create_migration(
            "002_create_form_submissions",
            "1.0.0",
            "Create form submissions table"
        )
        
        forms_migration.add_up_sql("""
        CREATE TABLE form_submissions (
            id INTEGER PRIMARY KEY,
            form_name TEXT NOT NULL,
            form_data TEXT NOT NULL,
            user_id INTEGER,
            ip_address TEXT,
            user_agent TEXT,
            status TEXT DEFAULT 'pending',
            submitted_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            processed_at DATETIME,
            processing_notes TEXT,
            FOREIGN KEY (user_id) REFERENCES users(id)
        )
        """)
        
        forms_migration.add_up_sql("""
        CREATE INDEX idx_form_submissions_form_name ON form_submissions(form_name)
        """)
        
        forms_migration.add_up_sql("""
        CREATE INDEX idx_form_submissions_user_id ON form_submissions(user_id)
        """)
        
        forms_migration.add_down_sql("DROP TABLE IF EXISTS form_submissions")
        
        logger.info("Standard migrations created")

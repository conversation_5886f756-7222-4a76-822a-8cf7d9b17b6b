"""
Database manager for Arcanum applications.
"""

import time
import logging
from typing import Dict, List, Any, Optional, Union, Type
from pathlib import Path
from contextlib import contextmanager

from .adapters import Database<PERSON>dapter, SQLiteAdapter, DuckDBAdapter
from .models import TableSchema, DatabaseModel
from .query import QueryBuilder, QueryResult
from .migrations import MigrationManager

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Central database manager for Arcanum applications."""
    
    def __init__(self, adapter: DatabaseAdapter):
        self.adapter = adapter
        self.migration_manager = MigrationManager(self)
        self.schemas: Dict[str, TableSchema] = {}
        self.models: Dict[str, Type[DatabaseModel]] = {}
        self._connected = False
        
    @classmethod
    def create_sqlite(cls, database_path: Union[str, Path]) -> 'DatabaseManager':
        """Create a database manager with SQLite adapter."""
        adapter = SQLiteAdapter(database_path)
        return cls(adapter)
        
    @classmethod
    def create_duckdb(cls, database_path: Union[str, Path] = ":memory:") -> 'DatabaseManager':
        """Create a database manager with DuckDB adapter."""
        adapter = DuckDBAdapter(database_path)
        return cls(adapter)
        
    def connect(self) -> bool:
        """Connect to the database."""
        if self._connected:
            return True
            
        success = self.adapter.connect()
        if success:
            self._connected = True
            logger.info("Database manager connected")
            
            # Initialize migration table
            self.migration_manager.initialize()
            
        return success
        
    def disconnect(self):
        """Disconnect from the database."""
        if self._connected:
            self.adapter.disconnect()
            self._connected = False
            logger.info("Database manager disconnected")
            
    def is_connected(self) -> bool:
        """Check if connected to database."""
        return self._connected
        
    def register_schema(self, schema: TableSchema):
        """Register a table schema."""
        self.schemas[schema.name] = schema
        logger.info(f"Registered schema: {schema.name}")
        
    def register_model(self, model_class: Type[DatabaseModel], table_name: str = None):
        """Register a model class."""
        if not table_name:
            table_name = getattr(model_class, '__tablename__', model_class.__name__.lower())
            
        self.models[table_name] = model_class
        logger.info(f"Registered model: {model_class.__name__} -> {table_name}")
        
    def create_tables(self, drop_existing: bool = False) -> bool:
        """Create all registered tables."""
        if not self._connected:
            raise RuntimeError("Not connected to database")
            
        success = True
        for schema_name, schema in self.schemas.items():
            try:
                if drop_existing and self.adapter.table_exists(schema.name):
                    logger.info(f"Dropping existing table: {schema.name}")
                    self.adapter.drop_table(schema.name)
                    
                if not self.adapter.table_exists(schema.name):
                    logger.info(f"Creating table: {schema.name}")
                    if not self.adapter.create_table(schema):
                        success = False
                        logger.error(f"Failed to create table: {schema.name}")
                else:
                    logger.info(f"Table already exists: {schema.name}")
                    
            except Exception as e:
                logger.error(f"Error creating table {schema.name}: {e}")
                success = False
                
        return success
        
    def drop_tables(self) -> bool:
        """Drop all registered tables."""
        if not self._connected:
            raise RuntimeError("Not connected to database")
            
        success = True
        for schema_name, schema in self.schemas.items():
            try:
                if self.adapter.table_exists(schema.name):
                    logger.info(f"Dropping table: {schema.name}")
                    if not self.adapter.drop_table(schema.name):
                        success = False
                        logger.error(f"Failed to drop table: {schema.name}")
                        
            except Exception as e:
                logger.error(f"Error dropping table {schema.name}: {e}")
                success = False
                
        return success
        
    def query(self, table_name: str = None) -> QueryBuilder:
        """Create a new query builder."""
        return QueryBuilder(table_name)
        
    def execute_query(self, query_builder: QueryBuilder) -> QueryResult:
        """Execute a query builder and return results."""
        if not self._connected:
            raise RuntimeError("Not connected to database")
            
        start_time = time.time()
        result = QueryResult(success=False)
        
        try:
            query, params = query_builder.build()
            result.query = query
            result.params = params
            
            logger.debug(f"Executing query: {query}")
            logger.debug(f"Parameters: {params}")
            
            if query.strip().upper().startswith("SELECT"):
                # SELECT query
                data = self.adapter.fetch_all(query, params)
                result.data = data
                result.row_count = len(data)
                result.success = True
                
            else:
                # INSERT, UPDATE, DELETE
                cursor = self.adapter.execute(query, params)
                
                if hasattr(cursor, 'rowcount'):
                    result.affected_rows = cursor.rowcount
                    
                if hasattr(cursor, 'lastrowid') and cursor.lastrowid:
                    result.last_insert_id = cursor.lastrowid
                    
                result.success = True
                
        except Exception as e:
            result.error = str(e)
            logger.error(f"Query execution failed: {e}")
            
        finally:
            result.execution_time = time.time() - start_time
            
        return result
        
    def execute_raw(self, query: str, params: tuple = None) -> QueryResult:
        """Execute a raw SQL query."""
        if not self._connected:
            raise RuntimeError("Not connected to database")
            
        start_time = time.time()
        result = QueryResult(success=False, query=query, params=params or ())
        
        try:
            logger.debug(f"Executing raw query: {query}")
            logger.debug(f"Parameters: {params}")
            
            if query.strip().upper().startswith("SELECT"):
                data = self.adapter.fetch_all(query, params)
                result.data = data
                result.row_count = len(data)
            else:
                cursor = self.adapter.execute(query, params)
                if hasattr(cursor, 'rowcount'):
                    result.affected_rows = cursor.rowcount
                if hasattr(cursor, 'lastrowid') and cursor.lastrowid:
                    result.last_insert_id = cursor.lastrowid
                    
            result.success = True
            
        except Exception as e:
            result.error = str(e)
            logger.error(f"Raw query execution failed: {e}")
            
        finally:
            result.execution_time = time.time() - start_time
            
        return result
        
    def insert(self, table_name: str, data: Dict[str, Any]) -> QueryResult:
        """Insert a record."""
        query_builder = self.query(table_name).insert(data)
        return self.execute_query(query_builder)
        
    def update(self, table_name: str, data: Dict[str, Any],
              where_field: str, where_value: Any) -> QueryResult:
        """Update records."""
        query_builder = self.query(table_name).update(data).where(where_field, "=", where_value)
        return self.execute_query(query_builder)
        
    def delete(self, table_name: str, where_field: str, where_value: Any) -> QueryResult:
        """Delete records."""
        query_builder = self.query(table_name).delete().where(where_field, "=", where_value)
        return self.execute_query(query_builder)
        
    def find(self, table_name: str, where_field: str, where_value: Any) -> QueryResult:
        """Find records."""
        query_builder = self.query(table_name).select().where(where_field, "=", where_value)
        return self.execute_query(query_builder)
        
    def find_one(self, table_name: str, where_field: str, where_value: Any) -> Optional[Dict[str, Any]]:
        """Find one record."""
        result = self.find(table_name, where_field, where_value)
        return result.first() if result.success else None
        
    def find_all(self, table_name: str) -> QueryResult:
        """Find all records in a table."""
        query_builder = self.query(table_name).select()
        return self.execute_query(query_builder)
        
    def count(self, table_name: str, where_field: str = None, where_value: Any = None) -> int:
        """Count records in a table."""
        query_builder = self.query(table_name).select("COUNT(*) as count")
        
        if where_field and where_value is not None:
            query_builder.where(where_field, where_value)
            
        result = self.execute_query(query_builder)
        if result.success and result.data:
            return result.data[0].get('count', 0)
        return 0
        
    def table_exists(self, table_name: str) -> bool:
        """Check if a table exists."""
        if not self._connected:
            return False
        return self.adapter.table_exists(table_name)
        
    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """Get table information."""
        if not self._connected:
            return {}
        return self.adapter.get_table_info(table_name)
        
    def get_tables(self) -> List[str]:
        """Get list of all tables."""
        if not self._connected:
            return []
            
        try:
            if isinstance(self.adapter, SQLiteAdapter):
                result = self.execute_raw(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
                )
            else:
                result = self.execute_raw(
                    "SELECT table_name as name FROM information_schema.tables"
                )
                
            if result.success:
                return [row['name'] for row in result.data]
                
        except Exception as e:
            logger.error(f"Failed to get table list: {e}")
            
        return []
        
    @contextmanager
    def transaction(self):
        """Context manager for database transactions."""
        if not self._connected:
            raise RuntimeError("Not connected to database")
            
        try:
            if hasattr(self.adapter, 'begin_transaction'):
                self.adapter.begin_transaction()
            yield self
            if hasattr(self.adapter, 'commit_transaction'):
                self.adapter.commit_transaction()
        except Exception as e:
            if hasattr(self.adapter, 'rollback_transaction'):
                self.adapter.rollback_transaction()
            logger.error(f"Transaction failed: {e}")
            raise
            
    def backup(self, backup_path: Union[str, Path]) -> bool:
        """Create a backup of the database."""
        if not self._connected:
            raise RuntimeError("Not connected to database")
            
        try:
            if isinstance(self.adapter, SQLiteAdapter):
                # SQLite backup
                import shutil
                shutil.copy2(self.adapter.database_path, backup_path)
                logger.info(f"Database backed up to: {backup_path}")
                return True
            else:
                logger.warning("Backup not implemented for this database type")
                return False
                
        except Exception as e:
            logger.error(f"Backup failed: {e}")
            return False
            
    def restore(self, backup_path: Union[str, Path]) -> bool:
        """Restore database from backup."""
        try:
            if isinstance(self.adapter, SQLiteAdapter):
                # Disconnect first
                was_connected = self._connected
                if was_connected:
                    self.disconnect()
                    
                # Restore file
                import shutil
                shutil.copy2(backup_path, self.adapter.database_path)
                
                # Reconnect if needed
                if was_connected:
                    self.connect()
                    
                logger.info(f"Database restored from: {backup_path}")
                return True
            else:
                logger.warning("Restore not implemented for this database type")
                return False
                
        except Exception as e:
            logger.error(f"Restore failed: {e}")
            return False
            
    def vacuum(self) -> bool:
        """Vacuum the database to reclaim space."""
        if not self._connected:
            raise RuntimeError("Not connected to database")
            
        try:
            result = self.execute_raw("VACUUM")
            if result.success:
                logger.info("Database vacuumed successfully")
                return True
            else:
                logger.error(f"Vacuum failed: {result.error}")
                return False
                
        except Exception as e:
            logger.error(f"Vacuum failed: {e}")
            return False

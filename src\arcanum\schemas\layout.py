"""
Pydantic schemas for layout definitions and validation.
"""

from typing import Dict, List, Optional, Any, Union, Literal
from pydantic import BaseModel, Field, validator
from .widgets import BaseWidget, LayoutType


class LayoutStyle(BaseModel):
    """Layout styling configuration."""
    spacing: Optional[Union[str, int]] = None
    padding: Optional[Union[str, int]] = None
    margin: Optional[Union[str, int]] = None
    background_color: Optional[str] = None
    border: Optional[str] = None
    border_radius: Optional[str] = None
    width: Optional[Union[str, int]] = None
    height: Optional[Union[str, int]] = None
    min_width: Optional[Union[str, int]] = None
    min_height: Optional[Union[str, int]] = None
    max_width: Optional[Union[str, int]] = None
    max_height: Optional[Union[str, int]] = None
    overflow: Optional[Literal["visible", "hidden", "scroll", "auto"]] = None
    custom_css: Optional[str] = None


class ConditionalDisplay(BaseModel):
    """Conditional display rules for layouts and widgets."""
    condition: str  # Expression to evaluate
    show_if: Optional[bool] = True  # Show if condition is true/false
    depends_on: Optional[List[str]] = Field(default_factory=list)  # Widget IDs this depends on


class BaseLayout(BaseModel):
    """Base layout configuration."""
    type: LayoutType
    id: Optional[str] = None
    title: Optional[str] = None
    description: Optional[str] = None
    
    # Styling
    style: Optional[Union[str, LayoutStyle]] = None
    css_class: Optional[str] = None
    
    # Conditional display
    conditional: Optional[ConditionalDisplay] = None
    
    # Content
    content: List[Union[Dict[str, Any], "BaseLayout"]] = Field(default_factory=list)
    
    class Config:
        extra = "allow"


class RowLayout(BaseLayout):
    """Row-based layout (horizontal)."""
    type: Literal[LayoutType.ROW] = LayoutType.ROW
    columns: Optional[List[Dict[str, Any]]] = Field(default_factory=list)
    align_items: Optional[Literal["start", "center", "end", "stretch"]] = "start"
    justify_content: Optional[Literal["start", "center", "end", "space-between", "space-around"]] = "start"
    wrap: bool = False


class ColumnLayout(BaseLayout):
    """Column-based layout (vertical)."""
    type: Literal[LayoutType.COLUMN] = LayoutType.COLUMN
    align_items: Optional[Literal["start", "center", "end", "stretch"]] = "stretch"
    justify_content: Optional[Literal["start", "center", "end", "space-between", "space-around"]] = "start"


class GridLayout(BaseLayout):
    """Grid-based layout."""
    type: Literal[LayoutType.GRID] = LayoutType.GRID
    columns: int = 12
    rows: Optional[int] = None
    gap: Optional[Union[str, int]] = None
    column_gap: Optional[Union[str, int]] = None
    row_gap: Optional[Union[str, int]] = None
    
    # Grid template areas for named positioning
    areas: Optional[List[str]] = Field(default_factory=list)


class FlexLayout(BaseLayout):
    """Flexible layout."""
    type: Literal[LayoutType.FLEX] = LayoutType.FLEX
    direction: Optional[Literal["row", "column", "row-reverse", "column-reverse"]] = "row"
    wrap: Optional[Literal["nowrap", "wrap", "wrap-reverse"]] = "nowrap"
    justify_content: Optional[Literal["start", "center", "end", "space-between", "space-around", "space-evenly"]] = "start"
    align_items: Optional[Literal["start", "center", "end", "stretch", "baseline"]] = "stretch"
    align_content: Optional[Literal["start", "center", "end", "space-between", "space-around", "stretch"]] = "stretch"


class TabItem(BaseModel):
    """Individual tab configuration."""
    id: str
    title: str
    icon: Optional[str] = None
    disabled: bool = False
    content: List[Union[Dict[str, Any], BaseLayout]] = Field(default_factory=list)


class TabLayout(BaseLayout):
    """Tab-based layout."""
    type: Literal[LayoutType.TAB] = LayoutType.TAB
    tabs: List[TabItem] = Field(default_factory=list)
    active_tab: Optional[str] = None
    tab_position: Optional[Literal["top", "bottom", "left", "right"]] = "top"
    closable: bool = False


class AccordionItem(BaseModel):
    """Individual accordion section."""
    id: str
    title: str
    icon: Optional[str] = None
    expanded: bool = False
    disabled: bool = False
    content: List[Union[Dict[str, Any], BaseLayout]] = Field(default_factory=list)


class AccordionLayout(BaseLayout):
    """Accordion-based layout."""
    type: Literal[LayoutType.ACCORDION] = LayoutType.ACCORDION
    items: List[AccordionItem] = Field(default_factory=list)
    multiple_open: bool = False
    collapsible: bool = True


class CanvasLayout(BaseLayout):
    """Canvas-based layout with absolute positioning."""
    type: Literal[LayoutType.CANVAS] = LayoutType.CANVAS
    snap_to_grid: bool = True  # Enable grid snapping for widgets
    grid_size: int = 10  # Grid size in pixels for snapping
    canvas_width: int = 800  # Canvas width in pixels
    canvas_height: int = 600  # Canvas height in pixels
    auto_resize: bool = True  # Auto-resize canvas to fit content


class PageLayout(BaseModel):
    """Complete page layout definition."""
    id: str
    title: Optional[str] = None
    description: Optional[str] = None
    version: Optional[str] = "1.0.0"
    
    # Page metadata
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)
    
    # Main layout
    layout: Union[RowLayout, ColumnLayout, GridLayout, FlexLayout, TabLayout, AccordionLayout, CanvasLayout]
    
    # Page-level styling
    style: Optional[Union[str, LayoutStyle]] = None
    theme: Optional[str] = None
    
    # Data binding and logic
    data_sources: Optional[Dict[str, Any]] = Field(default_factory=dict)
    logic_graph: Optional[str] = None  # Reference to logic graph file
    
    # Validation and events
    validation_rules: Optional[List[Dict[str, Any]]] = Field(default_factory=list)
    events: Optional[Dict[str, Any]] = Field(default_factory=dict)
    
    @validator("id")
    def validate_id(cls, v):
        """Validate page ID format."""
        if not v or not v.replace("_", "").replace("-", "").isalnum():
            raise ValueError("Page ID must be alphanumeric with underscores or hyphens")
        return v


# Layout type mapping for dynamic creation
LAYOUT_TYPE_MAP = {
    LayoutType.ROW: RowLayout,
    LayoutType.COLUMN: ColumnLayout,
    LayoutType.GRID: GridLayout,
    LayoutType.FLEX: FlexLayout,
    LayoutType.TAB: TabLayout,
    LayoutType.ACCORDION: AccordionLayout,
    LayoutType.CANVAS: CanvasLayout,
}


# Update forward references
BaseLayout.model_rebuild()
RowLayout.model_rebuild()
ColumnLayout.model_rebuild()
GridLayout.model_rebuild()
FlexLayout.model_rebuild()
TabLayout.model_rebuild()
AccordionLayout.model_rebuild()

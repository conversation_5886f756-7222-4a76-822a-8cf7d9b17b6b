"""
Widget registry for dynamic widget discovery and management.
"""

import yaml
from pathlib import Path
from typing import Dict, List, Optional, Type, Any
from pydantic import ValidationError

from ..schemas.widgets import (
    BaseWidget, WidgetDefinition, WidgetType, WIDGET_TYPE_MAP
)


class WidgetRegistry:
    """
    Manages widget discovery, registration, and instantiation.
    """
    
    def __init__(self):
        """Initialize the widget registry."""
        self._widgets: Dict[str, WidgetDefinition] = {}
        self._widget_classes: Dict[WidgetType, Type[BaseWidget]] = WIDGET_TYPE_MAP.copy()
        self._categories: Dict[str, List[str]] = {}
        
        # Register built-in widgets
        self._register_builtin_widgets()
    
    def _register_builtin_widgets(self) -> None:
        """Register built-in widget definitions."""
        builtin_widgets = [
            WidgetDefinition(
                name="text",
                type=WidgetType.TEXT,
                description="Single-line text input",
                category="input",
                schema={
                    "type": "object",
                    "properties": {
                        "placeholder": {"type": "string"},
                        "max_length": {"type": "integer"},
                        "pattern": {"type": "string"}
                    }
                }
            ),
            WidgetDefinition(
                name="textarea",
                type=WidgetType.TEXTAREA,
                description="Multi-line text input",
                category="input",
                schema={
                    "type": "object",
                    "properties": {
                        "rows": {"type": "integer", "default": 4},
                        "max_length": {"type": "integer"}
                    }
                }
            ),
            WidgetDefinition(
                name="number",
                type=WidgetType.NUMBER,
                description="Numeric input",
                category="input",
                schema={
                    "type": "object",
                    "properties": {
                        "min_value": {"type": "number"},
                        "max_value": {"type": "number"},
                        "step": {"type": "number", "default": 1}
                    }
                }
            ),
            WidgetDefinition(
                name="checkbox",
                type=WidgetType.CHECKBOX,
                description="Boolean checkbox input",
                category="input",
                schema={
                    "type": "object",
                    "properties": {
                        "checked": {"type": "boolean", "default": False}
                    }
                }
            ),
            WidgetDefinition(
                name="dropdown",
                type=WidgetType.DROPDOWN,
                description="Dropdown selection",
                category="input",
                schema={
                    "type": "object",
                    "properties": {
                        "options": {"type": "array"},
                        "multiple": {"type": "boolean", "default": False}
                    },
                    "required": ["options"]
                }
            ),
            WidgetDefinition(
                name="button",
                type=WidgetType.BUTTON,
                description="Clickable button",
                category="action",
                schema={
                    "type": "object",
                    "properties": {
                        "button_type": {"type": "string", "enum": ["button", "submit", "reset"]},
                        "variant": {"type": "string", "enum": ["primary", "secondary", "success", "warning", "danger"]}
                    }
                }
            ),
            WidgetDefinition(
                name="label",
                type=WidgetType.LABEL,
                description="Text display",
                category="display",
                schema={
                    "type": "object",
                    "properties": {
                        "text": {"type": "string"},
                        "html": {"type": "boolean", "default": False}
                    },
                    "required": ["text"]
                }
            ),
            WidgetDefinition(
                name="image",
                type=WidgetType.IMAGE,
                description="Image display",
                category="display",
                schema={
                    "type": "object",
                    "properties": {
                        "src": {"type": "string"},
                        "alt": {"type": "string"},
                        "width": {"type": ["string", "integer"]},
                        "height": {"type": ["string", "integer"]}
                    },
                    "required": ["src"]
                }
            ),
            WidgetDefinition(
                name="slider",
                type=WidgetType.SLIDER,
                description="Range slider input",
                category="input",
                schema={
                    "type": "object",
                    "properties": {
                        "min_value": {"type": "number", "default": 0},
                        "max_value": {"type": "number", "default": 100},
                        "step": {"type": "number", "default": 1},
                        "show_value": {"type": "boolean", "default": True}
                    }
                }
            )
        ]
        
        for widget_def in builtin_widgets:
            self.register_widget(widget_def)
    
    def register_widget(self, widget_def: WidgetDefinition) -> None:
        """
        Register a widget definition.
        
        Args:
            widget_def: Widget definition to register.
        """
        self._widgets[widget_def.name] = widget_def
        
        # Update categories
        category = widget_def.category or "general"
        if category not in self._categories:
            self._categories[category] = []
        if widget_def.name not in self._categories[category]:
            self._categories[category].append(widget_def.name)
    
    def register_widget_class(self, widget_type: WidgetType, widget_class: Type[BaseWidget]) -> None:
        """
        Register a widget class for a specific type.
        
        Args:
            widget_type: Widget type enum.
            widget_class: Pydantic model class for the widget.
        """
        self._widget_classes[widget_type] = widget_class
    
    def discover_widgets(self, widgets_path: Path) -> int:
        """
        Discover and load widgets from a directory.
        
        Args:
            widgets_path: Path to widgets directory.
            
        Returns:
            Number of widgets discovered.
        """
        if not widgets_path.exists():
            return 0
        
        discovered = 0
        
        for widget_file in widgets_path.glob("*.yaml"):
            try:
                with open(widget_file, 'r', encoding='utf-8') as f:
                    widget_data = yaml.safe_load(f)
                
                if widget_data:
                    widget_def = WidgetDefinition(**widget_data)
                    self.register_widget(widget_def)
                    discovered += 1
                    
            except (yaml.YAMLError, ValidationError) as e:
                # Log error but continue discovery
                print(f"Warning: Failed to load widget from {widget_file}: {e}")
        
        return discovered
    
    def get_widget(self, name: str) -> Optional[WidgetDefinition]:
        """
        Get widget definition by name.
        
        Args:
            name: Widget name.
            
        Returns:
            Widget definition or None if not found.
        """
        return self._widgets.get(name)
    
    def get_widgets_by_category(self, category: str) -> List[WidgetDefinition]:
        """
        Get all widgets in a category.
        
        Args:
            category: Category name.
            
        Returns:
            List of widget definitions.
        """
        widget_names = self._categories.get(category, [])
        return [self._widgets[name] for name in widget_names if name in self._widgets]
    
    def get_all_widgets(self) -> Dict[str, WidgetDefinition]:
        """Get all registered widgets."""
        return self._widgets.copy()
    
    def get_categories(self) -> List[str]:
        """Get all widget categories."""
        return list(self._categories.keys())

    def get_widgets_by_category(self) -> Dict[str, List[str]]:
        """Get widgets organized by category."""
        result = {}
        for category_name, widget_names in self._categories.items():
            result[category_name] = list(widget_names)
        return result
    
    def create_widget(self, widget_data: Dict[str, Any]) -> BaseWidget:
        """
        Create a widget instance from data.
        
        Args:
            widget_data: Widget configuration data.
            
        Returns:
            Widget instance.
            
        Raises:
            ValueError: If widget type is unknown or data is invalid.
        """
        widget_type_str = widget_data.get("widget")
        if not widget_type_str:
            raise ValueError("Widget type not specified")
        
        try:
            widget_type = WidgetType(widget_type_str)
        except ValueError:
            raise ValueError(f"Unknown widget type: {widget_type_str}")
        
        widget_class = self._widget_classes.get(widget_type)
        if not widget_class:
            raise ValueError(f"No widget class registered for type: {widget_type}")
        
        try:
            return widget_class(**widget_data)
        except ValidationError as e:
            raise ValueError(f"Widget validation failed: {e}")
    
    def validate_widget_data(self, widget_data: Dict[str, Any]) -> bool:
        """
        Validate widget data against its schema.
        
        Args:
            widget_data: Widget data to validate.
            
        Returns:
            True if valid, False otherwise.
        """
        try:
            self.create_widget(widget_data)
            return True
        except ValueError:
            return False
    
    def export_widget_definitions(self) -> Dict[str, Any]:
        """
        Export all widget definitions for documentation or API.
        
        Returns:
            Dictionary of widget definitions.
        """
        return {
            name: widget_def.dict()
            for name, widget_def in self._widgets.items()
        }

"""
Test suite for professional GUI Builder features.
Tests the enhanced functionality including command system, keyboard shortcuts, 
visual feedback, and error handling.
"""

import pytest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication, QWidget
from PyQt5.QtCore import Qt
from PyQt5.QtTest import QTest

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from arcanum.dev.gui_builder.builder import ArcanumBuilder
from arcanum.dev.gui_builder.editor_commands import (
    EditorCommandManager, ClipboardManager, AddWidgetCommand, 
    DeleteWidgetCommand, PropertyChangeCommand
)
from arcanum.dev.gui_builder.keyboard_shortcuts import KeyboardShortcutManager
from arcanum.dev.gui_builder.visual_feedback import FeedbackManager
from arcanum.dev.gui_builder.error_handling import Error<PERSON><PERSON><PERSON>


@pytest.fixture(scope="session")
def qapp():
    """Create QApplication instance for testing."""
    if not QApplication.instance():
        app = QApplication([])
    else:
        app = QApplication.instance()
    yield app
    # Don't quit the app as it might be used by other tests


@pytest.fixture
def mock_builder(qapp):
    """Create a mock ArcanumBuilder for testing."""
    with patch('arcanum.dev.gui_builder.builder.ArcanumBuilder.__init__', return_value=None):
        builder = ArcanumBuilder()
        builder.design_canvas = Mock()
        builder.property_editor = Mock()
        builder.yaml_editor = Mock()
        builder.page_navigator = Mock()
        builder.widget_palette = Mock()
        builder.statusBar = Mock(return_value=Mock())
        return builder


class TestEditorCommandManager:
    """Test the command pattern implementation."""
    
    def test_command_manager_initialization(self, mock_builder):
        """Test command manager initializes correctly."""
        manager = EditorCommandManager(mock_builder)
        assert manager.editor == mock_builder
        assert manager.undo_stack is not None
        assert hasattr(manager, 'can_undo_changed')
        assert hasattr(manager, 'can_redo_changed')
    
    def test_add_widget_command(self, mock_builder):
        """Test add widget command execution."""
        manager = EditorCommandManager(mock_builder)
        widget_data = {'widget': 'button', 'id': 'test_button', 'label': 'Test'}
        position = (100, 100)
        
        command = AddWidgetCommand(widget_data, position)
        command.set_editor(mock_builder)
        
        # Mock the design canvas method
        mock_builder.design_canvas.add_widget_from_data = Mock()
        
        command.redo()
        mock_builder.design_canvas.add_widget_from_data.assert_called_once_with(widget_data, position)
    
    def test_delete_widget_command(self, mock_builder):
        """Test delete widget command execution."""
        manager = EditorCommandManager(mock_builder)
        widget_id = 'test_button'
        
        command = DeleteWidgetCommand(widget_id)
        command.set_editor(mock_builder)
        
        # Mock the design canvas method
        mock_builder.design_canvas.remove_widget_by_id = Mock()
        
        command.redo()
        mock_builder.design_canvas.remove_widget_by_id.assert_called_once_with(widget_id)
    
    def test_property_change_command(self, mock_builder):
        """Test property change command execution."""
        manager = EditorCommandManager(mock_builder)
        widget_id = 'test_button'
        property_path = 'label'
        old_value = 'Old Label'
        new_value = 'New Label'
        
        command = PropertyChangeCommand(widget_id, property_path, old_value, new_value)
        command.set_editor(mock_builder)
        
        # Mock the design canvas method
        mock_builder.design_canvas.set_widget_property = Mock()
        
        command.redo()
        mock_builder.design_canvas.set_widget_property.assert_called_once_with(
            widget_id, property_path, new_value
        )
        
        command.undo()
        mock_builder.design_canvas.set_widget_property.assert_called_with(
            widget_id, property_path, old_value
        )


class TestClipboardManager:
    """Test clipboard functionality."""
    
    def test_clipboard_manager_initialization(self, mock_builder):
        """Test clipboard manager initializes correctly."""
        manager = ClipboardManager(mock_builder)
        assert manager.editor == mock_builder
        assert manager.clipboard_data == []
    
    def test_copy_widgets(self, mock_builder):
        """Test copying widgets to clipboard."""
        manager = ClipboardManager(mock_builder)
        
        # Mock selected widgets
        mock_widget = Mock()
        mock_widget.item_data = {'widget': 'button', 'id': 'test_button', 'label': 'Test'}
        mock_builder.design_canvas.get_selected_widgets = Mock(return_value=[mock_widget])
        
        manager.copy_widgets()
        
        assert len(manager.clipboard_data) == 1
        assert manager.clipboard_data[0]['widget'] == 'button'
    
    def test_paste_widgets(self, mock_builder):
        """Test pasting widgets from clipboard."""
        manager = ClipboardManager(mock_builder)
        
        # Set up clipboard data
        manager.clipboard_data = [
            {'widget': 'button', 'id': 'test_button', 'label': 'Test', 'position': {'x': 100, 'y': 100}}
        ]
        
        # Mock the command manager
        mock_builder.command_manager = Mock()
        mock_builder.command_manager.execute_command = Mock()
        
        manager.paste_widgets()
        
        # Should execute an add widget command
        mock_builder.command_manager.execute_command.assert_called_once()


class TestKeyboardShortcutManager:
    """Test keyboard shortcuts system."""
    
    def test_shortcut_manager_initialization(self, qapp):
        """Test shortcut manager initializes correctly."""
        widget = QWidget()
        manager = KeyboardShortcutManager(widget)
        
        assert manager.parent == widget
        assert len(manager.shortcuts) > 0
        assert 'undo' in manager.shortcuts
        assert 'redo' in manager.shortcuts
        assert 'copy' in manager.shortcuts
        assert 'paste' in manager.shortcuts
    
    def test_add_custom_shortcut(self, qapp):
        """Test adding custom shortcuts."""
        widget = QWidget()
        manager = KeyboardShortcutManager(widget)
        
        initial_count = len(manager.shortcuts)
        manager.add_shortcut("Ctrl+T", "test_action", "Test Action")
        
        assert len(manager.shortcuts) == initial_count + 1
        assert 'test_action' in manager.shortcuts
        
        info = manager.get_shortcut_info('test_action')
        assert info['key_sequence'] == "Ctrl+T"
        assert info['description'] == "Test Action"
    
    def test_connect_shortcut(self, qapp):
        """Test connecting shortcuts to callbacks."""
        widget = QWidget()
        manager = KeyboardShortcutManager(widget)
        
        callback = Mock()
        manager.connect_shortcut('undo', callback)
        
        # Verify the shortcut exists and is connected
        assert 'undo' in manager.shortcuts
    
    def test_shortcuts_by_category(self, qapp):
        """Test organizing shortcuts by category."""
        widget = QWidget()
        manager = KeyboardShortcutManager(widget)
        
        categories = manager.get_shortcuts_by_category()
        
        assert 'File' in categories
        assert 'Edit' in categories
        assert 'View' in categories
        assert 'Widget' in categories
        
        # Check that file category contains expected shortcuts
        file_shortcuts = categories['File']
        assert any('new_project' in action for action in file_shortcuts)
        assert any('save_project' in action for action in file_shortcuts)


class TestVisualFeedbackManager:
    """Test visual feedback system."""
    
    def test_feedback_manager_initialization(self, qapp):
        """Test feedback manager initializes correctly."""
        widget = QWidget()
        manager = FeedbackManager(widget)
        
        assert manager.parent == widget
        assert manager.status_indicator is not None
        assert manager.selection_highlight is not None
        assert manager.loading_overlay is not None
        assert manager.snap_guides is not None
        assert manager.active_toasts == []
    
    def test_status_indicator(self, qapp):
        """Test status indicator functionality."""
        widget = QWidget()
        manager = FeedbackManager(widget)
        
        manager.set_status("working")
        assert manager.status_indicator.status == "working"
        
        manager.set_status("success")
        assert manager.status_indicator.status == "success"
        
        manager.set_status("error")
        assert manager.status_indicator.status == "error"
    
    def test_progress_toast(self, qapp):
        """Test progress toast notifications."""
        widget = QWidget()
        widget.resize(800, 600)  # Give the widget a size
        manager = FeedbackManager(widget)
        
        toast = manager.show_progress_toast("Testing...")
        
        assert toast is not None
        assert len(manager.active_toasts) == 1
        assert toast.message == "Testing..."
    
    def test_loading_overlay(self, qapp):
        """Test loading overlay functionality."""
        widget = QWidget()
        widget.resize(800, 600)
        manager = FeedbackManager(widget)
        
        manager.show_loading("Loading test...")
        assert manager.loading_overlay.isVisible()
        
        manager.hide_loading()
        assert not manager.loading_overlay.isVisible()


class TestErrorHandler:
    """Test error handling system."""
    
    def test_error_handler_initialization(self, qapp):
        """Test error handler initializes correctly."""
        widget = QWidget()
        handler = ErrorHandler(widget)
        
        assert handler.parent == widget
        assert handler.logger is not None
        assert hasattr(handler, 'error_occurred')
        assert hasattr(handler, 'warning_occurred')
    
    def test_handle_exception(self, qapp):
        """Test exception handling."""
        widget = QWidget()
        handler = ErrorHandler(widget)
        
        # Mock the logger
        handler.logger = Mock()
        
        try:
            raise ValueError("Test error")
        except Exception as e:
            handler.handle_exception(type(e), e, e.__traceback__, "test_context")
            
        handler.logger.error.assert_called_once()
    
    def test_safe_execute_decorator(self, qapp):
        """Test safe execution decorator."""
        from arcanum.dev.gui_builder.error_handling import safe_execute
        
        @safe_execute
        def test_function():
            raise ValueError("Test error")
        
        # Should not raise exception
        result = test_function()
        assert result is None


class TestIntegration:
    """Integration tests for the complete system."""
    
    @patch('arcanum.dev.gui_builder.builder.ArcanumProject')
    @patch('arcanum.dev.gui_builder.builder.WidgetRegistry')
    def test_builder_initialization(self, mock_registry, mock_project, qapp):
        """Test that builder initializes with all professional features."""
        with patch('arcanum.dev.gui_builder.builder.ArcanumBuilder.setup_ui'):
            with patch('arcanum.dev.gui_builder.builder.ArcanumBuilder.setup_connections'):
                builder = ArcanumBuilder()
                
                # Check that all professional systems are initialized
                assert hasattr(builder, 'error_handler')
                assert hasattr(builder, 'command_manager')
                assert hasattr(builder, 'clipboard_manager')
                assert hasattr(builder, 'shortcut_manager')
                assert hasattr(builder, 'feedback_manager')
    
    def test_command_integration(self, mock_builder):
        """Test that commands integrate properly with the builder."""
        # Set up the builder with command manager
        mock_builder.command_manager = EditorCommandManager(mock_builder)
        mock_builder.clipboard_manager = ClipboardManager(mock_builder)
        
        # Test that copy/paste workflow works
        mock_widget = Mock()
        mock_widget.item_data = {'widget': 'button', 'id': 'test_button'}
        mock_builder.design_canvas.get_selected_widgets = Mock(return_value=[mock_widget])
        
        # Copy widgets
        mock_builder.clipboard_manager.copy_widgets()
        assert len(mock_builder.clipboard_manager.clipboard_data) == 1
        
        # Paste widgets should execute command
        mock_builder.command_manager.execute_command = Mock()
        mock_builder.clipboard_manager.paste_widgets()
        mock_builder.command_manager.execute_command.assert_called_once()


if __name__ == '__main__':
    pytest.main([__file__, '-v'])

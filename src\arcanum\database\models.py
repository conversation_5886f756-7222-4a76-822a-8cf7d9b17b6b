"""
Database models and schema definitions.
"""

from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
import uuid
from datetime import datetime


class ColumnType(Enum):
    """Database column types."""
    TEXT = "TEXT"
    INTEGER = "INTEGER"
    REAL = "REAL"
    BLOB = "BLOB"
    BOOLEAN = "BOOLEAN"
    DATETIME = "DATETIME"
    JSON = "JSON"


class ConstraintType(Enum):
    """Database constraint types."""
    PRIMARY_KEY = "PRIMARY KEY"
    FOREIGN_KEY = "FOREIGN KEY"
    UNIQUE = "UNIQUE"
    NOT_NULL = "NOT NULL"
    CHECK = "CHECK"
    DEFAULT = "DEFAULT"
    AUTO_INCREMENT = "AUTOINCREMENT"


@dataclass
class ColumnConstraint:
    """Represents a column constraint."""
    type: ConstraintType
    value: Any = None
    reference_table: Optional[str] = None
    reference_column: Optional[str] = None


@dataclass
class IndexDefinition:
    """Defines a database index."""
    name: str
    columns: List[str]
    unique: bool = False
    description: str = ""

    def to_sql(self, table_name: str, dialect: str = "sqlite") -> str:
        """Generate CREATE INDEX SQL."""
        unique_keyword = "UNIQUE " if self.unique else ""
        columns_str = ", ".join(self.columns)
        return f"CREATE {unique_keyword}INDEX {self.name} ON {table_name} ({columns_str})"


@dataclass
class ColumnDefinition:
    """Defines a database column."""
    name: str
    type: ColumnType
    constraints: List[ColumnConstraint] = field(default_factory=list)
    description: str = ""
    
    def __post_init__(self):
        if not self.constraints:
            self.constraints = []
            
    def add_constraint(self, constraint_type: ConstraintType, value: Any = None,
                      reference_table: str = None, reference_column: str = None):
        """Add a constraint to the column."""
        constraint = ColumnConstraint(
            type=constraint_type,
            value=value,
            reference_table=reference_table,
            reference_column=reference_column
        )
        self.constraints.append(constraint)
        
    def is_primary_key(self) -> bool:
        """Check if this column is a primary key."""
        return any(c.type == ConstraintType.PRIMARY_KEY for c in self.constraints)
        
    def is_nullable(self) -> bool:
        """Check if this column allows NULL values."""
        return not any(c.type == ConstraintType.NOT_NULL for c in self.constraints)
        
    def get_default_value(self) -> Any:
        """Get the default value for this column."""
        for constraint in self.constraints:
            if constraint.type == ConstraintType.DEFAULT:
                return constraint.value
        return None
        
    def to_sql(self, dialect: str = "sqlite") -> str:
        """Generate SQL column definition."""
        sql_parts = [self.name, self.type.value]
        
        for constraint in self.constraints:
            if constraint.type == ConstraintType.PRIMARY_KEY:
                sql_parts.append("PRIMARY KEY")
            elif constraint.type == ConstraintType.NOT_NULL:
                sql_parts.append("NOT NULL")
            elif constraint.type == ConstraintType.UNIQUE:
                sql_parts.append("UNIQUE")
            elif constraint.type == ConstraintType.DEFAULT:
                if isinstance(constraint.value, str):
                    sql_parts.append(f"DEFAULT '{constraint.value}'")
                else:
                    sql_parts.append(f"DEFAULT {constraint.value}")
            elif constraint.type == ConstraintType.FOREIGN_KEY:
                sql_parts.append(
                    f"REFERENCES {constraint.reference_table}({constraint.reference_column})"
                )
            elif constraint.type == ConstraintType.CHECK:
                sql_parts.append(f"CHECK ({constraint.value})")
                
        return " ".join(sql_parts)


@dataclass
class TableIndex:
    """Represents a database index."""
    name: str
    columns: List[str]
    unique: bool = False
    partial: Optional[str] = None  # WHERE clause for partial index
    
    def to_sql(self, table_name: str, dialect: str = "sqlite") -> str:
        """Generate SQL index definition."""
        unique_keyword = "UNIQUE " if self.unique else ""
        columns_str = ", ".join(self.columns)
        sql = f"CREATE {unique_keyword}INDEX {self.name} ON {table_name} ({columns_str})"
        
        if self.partial:
            sql += f" WHERE {self.partial}"
            
        return sql


@dataclass
class TableSchema:
    """Defines a database table schema."""
    name: str
    columns: List[ColumnDefinition] = field(default_factory=list)
    indexes: List[TableIndex] = field(default_factory=list)
    description: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        if not self.columns:
            self.columns = []
        if not self.indexes:
            self.indexes = []
            
    def add_column(self, name: str, column_type: ColumnType, 
                  constraints: List[ColumnConstraint] = None,
                  description: str = "") -> ColumnDefinition:
        """Add a column to the table."""
        column = ColumnDefinition(
            name=name,
            type=column_type,
            constraints=constraints or [],
            description=description
        )
        self.columns.append(column)
        return column
        
    def add_index(self, name: str, columns: List[str], 
                 unique: bool = False, partial: str = None) -> TableIndex:
        """Add an index to the table."""
        index = TableIndex(
            name=name,
            columns=columns,
            unique=unique,
            partial=partial
        )
        self.indexes.append(index)
        return index
        
    def get_column(self, name: str) -> Optional[ColumnDefinition]:
        """Get a column by name."""
        return next((col for col in self.columns if col.name == name), None)
        
    def get_primary_key_columns(self) -> List[ColumnDefinition]:
        """Get all primary key columns."""
        return [col for col in self.columns if col.is_primary_key()]
        
    def to_create_sql(self, dialect: str = "sqlite") -> str:
        """Generate CREATE TABLE SQL."""
        if not self.columns:
            raise ValueError("Table must have at least one column")
            
        column_definitions = [col.to_sql(dialect) for col in self.columns]
        columns_sql = ",\n    ".join(column_definitions)
        
        sql = f"CREATE TABLE {self.name} (\n    {columns_sql}\n)"
        return sql
        
    def to_drop_sql(self) -> str:
        """Generate DROP TABLE SQL."""
        return f"DROP TABLE IF EXISTS {self.name}"
        
    def validate(self) -> List[str]:
        """Validate the table schema."""
        errors = []
        
        if not self.name:
            errors.append("Table name is required")
            
        if not self.columns:
            errors.append("Table must have at least one column")
            
        # Check for duplicate column names
        column_names = [col.name for col in self.columns]
        if len(column_names) != len(set(column_names)):
            errors.append("Duplicate column names found")
            
        # Check primary key constraints
        pk_columns = self.get_primary_key_columns()
        if len(pk_columns) == 0:
            errors.append("Table should have at least one primary key column")
            
        return errors


class DatabaseModel:
    """Base class for database models with ORM-like functionality."""
    
    def __init__(self, table_schema: TableSchema):
        self.schema = table_schema
        self.data: Dict[str, Any] = {}
        self._original_data: Dict[str, Any] = {}
        self._is_new = True
        
    def __getattr__(self, name: str) -> Any:
        """Get column value."""
        if name in self.data:
            return self.data[name]
        raise AttributeError(f"'{self.__class__.__name__}' has no attribute '{name}'")
        
    def __setattr__(self, name: str, value: Any):
        """Set column value."""
        if name.startswith('_') or name in ['schema', 'data']:
            super().__setattr__(name, value)
        else:
            # Check if column exists
            column = self.schema.get_column(name)
            if column:
                self.data[name] = value
            else:
                super().__setattr__(name, value)
                
    def set_data(self, data: Dict[str, Any], is_new: bool = True):
        """Set model data from dictionary."""
        self.data = data.copy()
        self._original_data = data.copy()
        self._is_new = is_new
        
    def get_changes(self) -> Dict[str, Any]:
        """Get changed fields since last save."""
        changes = {}
        for key, value in self.data.items():
            if key not in self._original_data or self._original_data[key] != value:
                changes[key] = value
        return changes
        
    def is_dirty(self) -> bool:
        """Check if model has unsaved changes."""
        return len(self.get_changes()) > 0
        
    def is_new(self) -> bool:
        """Check if this is a new record."""
        return self._is_new
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary."""
        return self.data.copy()
        
    def validate(self) -> List[str]:
        """Validate model data against schema."""
        errors = []
        
        for column in self.schema.columns:
            value = self.data.get(column.name)
            
            # Check NOT NULL constraints
            if not column.is_nullable() and value is None:
                errors.append(f"Column '{column.name}' cannot be NULL")
                
            # Check data type compatibility
            if value is not None:
                if column.type == ColumnType.INTEGER and not isinstance(value, int):
                    try:
                        int(value)
                    except (ValueError, TypeError):
                        errors.append(f"Column '{column.name}' must be an integer")
                        
                elif column.type == ColumnType.REAL and not isinstance(value, (int, float)):
                    try:
                        float(value)
                    except (ValueError, TypeError):
                        errors.append(f"Column '{column.name}' must be a number")
                        
                elif column.type == ColumnType.BOOLEAN and not isinstance(value, bool):
                    if value not in [0, 1, "true", "false", "True", "False"]:
                        errors.append(f"Column '{column.name}' must be a boolean")
                        
        return errors
        
    @classmethod
    def create_schema(cls, table_name: str) -> TableSchema:
        """Create a basic schema for the model. Override in subclasses."""
        schema = TableSchema(table_name)
        
        # Add default ID column
        id_column = schema.add_column("id", ColumnType.INTEGER)
        id_column.add_constraint(ConstraintType.PRIMARY_KEY)
        
        # Add timestamps
        created_column = schema.add_column("created_at", ColumnType.DATETIME)
        created_column.add_constraint(ConstraintType.DEFAULT, datetime.now().isoformat())
        
        updated_column = schema.add_column("updated_at", ColumnType.DATETIME)
        updated_column.add_constraint(ConstraintType.DEFAULT, datetime.now().isoformat())
        
        return schema


# Pre-built model classes
class UserModel(DatabaseModel):
    """User model with standard fields."""

    def __init__(self, **kwargs):
        super().__init__()
        self.id = kwargs.get('id')
        self.username = kwargs.get('username')
        self.email = kwargs.get('email')
        self.password_hash = kwargs.get('password_hash')
        self.first_name = kwargs.get('first_name')
        self.last_name = kwargs.get('last_name')
        self.is_active = kwargs.get('is_active', True)
        self.last_login = kwargs.get('last_login')
        self.created_at = kwargs.get('created_at')
        self.updated_at = kwargs.get('updated_at')

    @classmethod
    def get_schema(cls) -> TableSchema:
        """Get the table schema for users."""
        schema = TableSchema("users")

        schema.add_column("id", ColumnType.INTEGER, [
            ColumnConstraint(ConstraintType.PRIMARY_KEY),
            ColumnConstraint(ConstraintType.AUTO_INCREMENT)
        ])

        schema.add_column("username", ColumnType.TEXT, [
            ColumnConstraint(ConstraintType.NOT_NULL),
            ColumnConstraint(ConstraintType.UNIQUE)
        ])

        schema.add_column("email", ColumnType.TEXT, [
            ColumnConstraint(ConstraintType.NOT_NULL),
            ColumnConstraint(ConstraintType.UNIQUE)
        ])

        schema.add_column("password_hash", ColumnType.TEXT, [
            ColumnConstraint(ConstraintType.NOT_NULL)
        ])

        schema.add_column("first_name", ColumnType.TEXT)
        schema.add_column("last_name", ColumnType.TEXT)

        schema.add_column("is_active", ColumnType.BOOLEAN, [
            ColumnConstraint(ConstraintType.DEFAULT, "1")
        ])

        schema.add_column("last_login", ColumnType.DATETIME)

        schema.add_column("created_at", ColumnType.DATETIME, [
            ColumnConstraint(ConstraintType.NOT_NULL),
            ColumnConstraint(ConstraintType.DEFAULT, "CURRENT_TIMESTAMP")
        ])

        schema.add_column("updated_at", ColumnType.DATETIME, [
            ColumnConstraint(ConstraintType.NOT_NULL),
            ColumnConstraint(ConstraintType.DEFAULT, "CURRENT_TIMESTAMP")
        ])

        # Add indexes
        schema.add_index("idx_users_username", ["username"], unique=True)
        schema.add_index("idx_users_email", ["email"], unique=True)

        return schema


class FormSubmissionModel(DatabaseModel):
    """Form submission model."""

    def __init__(self, **kwargs):
        super().__init__()
        self.id = kwargs.get('id')
        self.form_name = kwargs.get('form_name')
        self.form_data = kwargs.get('form_data')
        self.user_id = kwargs.get('user_id')
        self.ip_address = kwargs.get('ip_address')
        self.user_agent = kwargs.get('user_agent')
        self.status = kwargs.get('status', 'pending')
        self.submitted_at = kwargs.get('submitted_at')
        self.processed_at = kwargs.get('processed_at')
        self.processing_notes = kwargs.get('processing_notes')

    @classmethod
    def get_schema(cls) -> TableSchema:
        """Get the table schema for form submissions."""
        schema = TableSchema("form_submissions")

        schema.add_column("id", ColumnType.INTEGER, [
            ColumnConstraint(ConstraintType.PRIMARY_KEY),
            ColumnConstraint(ConstraintType.AUTO_INCREMENT)
        ])

        schema.add_column("form_name", ColumnType.TEXT, [
            ColumnConstraint(ConstraintType.NOT_NULL)
        ])

        schema.add_column("form_data", ColumnType.TEXT, [
            ColumnConstraint(ConstraintType.NOT_NULL)
        ])

        schema.add_column("user_id", ColumnType.INTEGER, [
            ColumnConstraint(ConstraintType.FOREIGN_KEY, "users(id)")
        ])
        schema.add_column("ip_address", ColumnType.TEXT)
        schema.add_column("user_agent", ColumnType.TEXT)

        schema.add_column("status", ColumnType.TEXT, [
            ColumnConstraint(ConstraintType.DEFAULT, "'pending'")
        ])

        schema.add_column("submitted_at", ColumnType.DATETIME, [
            ColumnConstraint(ConstraintType.NOT_NULL),
            ColumnConstraint(ConstraintType.DEFAULT, "CURRENT_TIMESTAMP")
        ])

        schema.add_column("processed_at", ColumnType.DATETIME)
        schema.add_column("processing_notes", ColumnType.TEXT)

        # Add indexes
        schema.add_index("idx_form_submissions_form_name", ["form_name"])
        schema.add_index("idx_form_submissions_user_id", ["user_id"])

        return schema


# Common model schemas for Arcanum applications
def create_user_schema() -> TableSchema:
    """Create a standard user table schema."""
    schema = TableSchema("users", description="User accounts")

    # Primary key
    id_col = schema.add_column("id", ColumnType.INTEGER, description="User ID")
    id_col.add_constraint(ConstraintType.PRIMARY_KEY)

    # User fields
    username_col = schema.add_column("username", ColumnType.TEXT, description="Username")
    username_col.add_constraint(ConstraintType.NOT_NULL)
    username_col.add_constraint(ConstraintType.UNIQUE)

    email_col = schema.add_column("email", ColumnType.TEXT, description="Email address")
    email_col.add_constraint(ConstraintType.NOT_NULL)
    email_col.add_constraint(ConstraintType.UNIQUE)

    password_col = schema.add_column("password_hash", ColumnType.TEXT, description="Password hash")
    password_col.add_constraint(ConstraintType.NOT_NULL)

    # Optional fields
    schema.add_column("first_name", ColumnType.TEXT, description="First name")
    schema.add_column("last_name", ColumnType.TEXT, description="Last name")
    schema.add_column("is_active", ColumnType.BOOLEAN, description="Account active status")
    schema.add_column("last_login", ColumnType.DATETIME, description="Last login time")

    # Timestamps
    created_col = schema.add_column("created_at", ColumnType.DATETIME, description="Creation time")
    created_col.add_constraint(ConstraintType.NOT_NULL)
    created_col.add_constraint(ConstraintType.DEFAULT, "CURRENT_TIMESTAMP")

    updated_col = schema.add_column("updated_at", ColumnType.DATETIME, description="Last update time")
    updated_col.add_constraint(ConstraintType.NOT_NULL)
    updated_col.add_constraint(ConstraintType.DEFAULT, "CURRENT_TIMESTAMP")

    # Indexes
    schema.add_index("idx_users_username", ["username"], unique=True)
    schema.add_index("idx_users_email", ["email"], unique=True)
    schema.add_index("idx_users_active", ["is_active"])

    return schema


def create_form_submission_schema() -> TableSchema:
    """Create a schema for form submissions."""
    schema = TableSchema("form_submissions", description="Form submission data")

    # Primary key
    id_col = schema.add_column("id", ColumnType.INTEGER, description="Submission ID")
    id_col.add_constraint(ConstraintType.PRIMARY_KEY)

    # Form identification
    form_name_col = schema.add_column("form_name", ColumnType.TEXT, description="Form identifier")
    form_name_col.add_constraint(ConstraintType.NOT_NULL)

    # Submission data
    data_col = schema.add_column("form_data", ColumnType.JSON, description="Form field data")
    data_col.add_constraint(ConstraintType.NOT_NULL)

    # Metadata
    schema.add_column("user_id", ColumnType.INTEGER, description="Submitting user ID")
    schema.add_column("ip_address", ColumnType.TEXT, description="Client IP address")
    schema.add_column("user_agent", ColumnType.TEXT, description="Client user agent")
    schema.add_column("status", ColumnType.TEXT, description="Submission status")

    # Timestamps
    submitted_col = schema.add_column("submitted_at", ColumnType.DATETIME, description="Submission time")
    submitted_col.add_constraint(ConstraintType.NOT_NULL)
    submitted_col.add_constraint(ConstraintType.DEFAULT, "CURRENT_TIMESTAMP")

    processed_col = schema.add_column("processed_at", ColumnType.DATETIME, description="Processing time")

    # Indexes
    schema.add_index("idx_form_submissions_form_name", ["form_name"])
    schema.add_index("idx_form_submissions_user_id", ["user_id"])
    schema.add_index("idx_form_submissions_submitted_at", ["submitted_at"])
    schema.add_index("idx_form_submissions_status", ["status"])

    return schema

# Getting Started Page
id: getting_started
title: "Getting Started with Arcanum"
description: "Introduction and tutorial for new users"

layout:
  type: column
  style:
    spacing: 20
    padding: 30
    max_width: 800
    margin: "0 auto"
  
  content:
    - widget: button
      id: back_btn
      label: "← Back to Welcome"
      variant: "secondary"
      action:
        type: link_to
        target: "pages/welcome_screen.yaml"
      style: "align-self: flex-start; margin-bottom: 20px;"
      
    - widget: label
      id: page_title
      text: "Getting Started with Arcanum"
      style: "font-size: 24px; font-weight: bold; color: #1e293b; margin-bottom: 20px;"
      
    - widget: label
      id: intro_text
      text: "Arcanum is a powerful application builder that lets you create desktop and web applications using simple YAML configuration files. Here's how to get started:"
      style: "color: #475569; line-height: 1.6; margin-bottom: 30px;"
    
    - type: accordion
      style:
        border: "1px solid #e2e8f0"
        border_radius: "8px"
      items:
        - id: step1
          title: "1. Understanding the Project Structure"
          expanded: true
          content:
            - widget: label
              text: "Your Arcanum project is organized into several key directories:"
              style: "margin-bottom: 15px; color: #374151;"
            - widget: label
              text: "• pages/ - Contains your layout definitions in YAML format"
              style: "margin-bottom: 5px; color: #6b7280;"
            - widget: label
              text: "• ui/widgets/ - Custom widget definitions"
              style: "margin-bottom: 5px; color: #6b7280;"
            - widget: label
              text: "• ui/themes/ - Theme and styling configurations"
              style: "margin-bottom: 5px; color: #6b7280;"
            - widget: label
              text: "• Arcanum.yaml - Main project configuration file"
              style: "margin-bottom: 15px; color: #6b7280;"
        
        - id: step2
          title: "2. Creating Your First Layout"
          content:
            - widget: label
              text: "Layouts are defined using YAML files in the pages/ directory. Each layout can contain:"
              style: "margin-bottom: 15px; color: #374151;"
            - widget: label
              text: "• Widgets (text inputs, buttons, labels, etc.)"
              style: "margin-bottom: 5px; color: #6b7280;"
            - widget: label
              text: "• Layout containers (rows, columns, grids, tabs)"
              style: "margin-bottom: 5px; color: #6b7280;"
            - widget: label
              text: "• Styling and theming options"
              style: "margin-bottom: 5px; color: #6b7280;"
            - widget: label
              text: "• Interactive logic and event handling"
              style: "margin-bottom: 15px; color: #6b7280;"
        
        - id: step3
          title: "3. Using the Widget System"
          content:
            - widget: label
              text: "Arcanum comes with built-in widgets for common use cases:"
              style: "margin-bottom: 15px; color: #374151;"
            - type: grid
              style:
                columns: 2
                gap: 10
              content:
                - widget: label
                  text: "• text - Single-line text input"
                  style: "color: #6b7280;"
                - widget: label
                  text: "• textarea - Multi-line text input"
                  style: "color: #6b7280;"
                - widget: label
                  text: "• button - Clickable buttons"
                  style: "color: #6b7280;"
                - widget: label
                  text: "• dropdown - Selection lists"
                  style: "color: #6b7280;"
                - widget: label
                  text: "• checkbox - Boolean inputs"
                  style: "color: #6b7280;"
                - widget: label
                  text: "• slider - Range inputs"
                  style: "color: #6b7280;"
        
        - id: step4
          title: "4. Adding Logic and Interactivity"
          content:
            - widget: label
              text: "Make your layouts interactive by adding actions and logic:"
              style: "margin-bottom: 15px; color: #374151;"
            - widget: label
              text: "• Button actions can navigate between pages"
              style: "margin-bottom: 5px; color: #6b7280;"
            - widget: label
              text: "• Form submissions can save data to the local database"
              style: "margin-bottom: 5px; color: #6b7280;"
            - widget: label
              text: "• Conditional display based on user input"
              style: "margin-bottom: 5px; color: #6b7280;"
            - widget: label
              text: "• Visual logic graphs for complex workflows"
              style: "margin-bottom: 15px; color: #6b7280;"
        
        - id: step5
          title: "5. Testing and Exporting"
          content:
            - widget: label
              text: "Once your application is ready, you can:"
              style: "margin-bottom: 15px; color: #374151;"
            - widget: label
              text: "• Use 'arcanum validate' to check for errors"
              style: "margin-bottom: 5px; color: #6b7280;"
            - widget: label
              text: "• Run 'arcanum run' to test your application"
              style: "margin-bottom: 5px; color: #6b7280;"
            - widget: label
              text: "• Export to web, desktop, or mobile formats"
              style: "margin-bottom: 5px; color: #6b7280;"
            - widget: label
              text: "• Share your application with others"
              style: "margin-bottom: 15px; color: #6b7280;"
    
    - type: row
      style:
        justify_content: center
        margin_top: 30
        spacing: 15
      content:
        - widget: button
          id: examples_btn
          label: "View Examples"
          variant: "primary"
          action:
            type: link_to
            target: "pages/examples.yaml"
        
        - widget: button
          id: docs_btn
          label: "Read Documentation"
          variant: "secondary"
          action:
            type: custom
            target: "open_docs"

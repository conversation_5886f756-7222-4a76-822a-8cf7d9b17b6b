"""
Visual Logic Graph Editor - PyQt5-based node editor for logic graphs.
"""

import math
from typing import Dict, List, Optional, Tuple, Any
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGraphicsView, QGraphicsScene,
    QGraphicsItem, QGraphicsRectItem, QGraphicsTextItem, QGraphicsEllipseItem,
    QGraphicsLineItem, QMenu, QAction, QInputDialog, QMessageBox,
    QToolBar, QPushButton, QLabel, QComboBox, QSplitter
)
from PyQt5.QtCore import Qt, QRectF, QPointF, pyqtSignal, QTimer
from PyQt5.QtGui import QPen, QBrush, QColor, QPainter, QFont, QPainterPath

from .graph import LogicGraph, LogicNode, LogicConnection, NodeType
from .nodes import EventNode, ActionNode, ConditionNode, VariableNode, FunctionNode, TriggerNode, DataNode
from .serializer import LogicGraphSerializer


class NodeGraphicsItem(QGraphicsRectItem):
    """Graphics item representing a logic node."""
    
    def __init__(self, node: LogicNode, parent=None):
        super().__init__(parent)
        self.node = node
        self.input_ports: List[PortGraphicsItem] = []
        self.output_ports: List[PortGraphicsItem] = []
        self.title_item = QGraphicsTextItem(self)
        self.setup_appearance()
        self.setup_ports()
        
    def setup_appearance(self):
        """Set up the visual appearance of the node."""
        # Set size and position
        self.setRect(0, 0, self.node.width, self.node.height)
        self.setPos(self.node.x, self.node.y)
        
        # Set colors based on node type
        colors = {
            NodeType.EVENT: QColor(100, 200, 100),
            NodeType.ACTION: QColor(100, 150, 200),
            NodeType.CONDITION: QColor(200, 200, 100),
            NodeType.VARIABLE: QColor(200, 150, 100),
            NodeType.FUNCTION: QColor(150, 100, 200),
            NodeType.TRIGGER: QColor(200, 100, 150),
            NodeType.DATA: QColor(150, 200, 150),
        }
        
        color = colors.get(self.node.node_type, QColor(150, 150, 150))
        self.setBrush(QBrush(color))
        self.setPen(QPen(QColor(50, 50, 50), 2))
        
        # Set up title
        self.title_item.setPlainText(self.node.name)
        self.title_item.setPos(5, 5)
        font = QFont()
        font.setBold(True)
        font.setPointSize(10)
        self.title_item.setFont(font)
        
        # Make item movable and selectable
        self.setFlag(QGraphicsItem.ItemIsMovable, True)
        self.setFlag(QGraphicsItem.ItemIsSelectable, True)
        self.setFlag(QGraphicsItem.ItemSendsGeometryChanges, True)
        
    def setup_ports(self):
        """Set up input and output ports."""
        port_size = 12
        port_spacing = 20
        
        # Input ports on the left
        for i, port in enumerate(self.node.input_ports):
            port_item = PortGraphicsItem(port, "input", self)
            port_item.setPos(-port_size/2, 30 + i * port_spacing)
            self.input_ports.append(port_item)
            
        # Output ports on the right
        for i, port in enumerate(self.node.output_ports):
            port_item = PortGraphicsItem(port, "output", self)
            port_item.setPos(self.node.width - port_size/2, 30 + i * port_spacing)
            self.output_ports.append(port_item)
            
    def itemChange(self, change, value):
        """Handle item changes (like position updates)."""
        if change == QGraphicsItem.ItemPositionChange:
            # Update node position
            self.node.x = value.x()
            self.node.y = value.y()
            
        return super().itemChange(change, value)
        
    def contextMenuEvent(self, event):
        """Show context menu for the node."""
        menu = QMenu()
        
        delete_action = QAction("Delete Node", menu)
        delete_action.triggered.connect(self.delete_node)
        menu.addAction(delete_action)
        
        edit_action = QAction("Edit Properties", menu)
        edit_action.triggered.connect(self.edit_properties)
        menu.addAction(edit_action)
        
        menu.exec_(event.screenPos())
        
    def delete_node(self):
        """Delete this node."""
        if self.scene():
            self.scene().removeItem(self)
            
    def edit_properties(self):
        """Edit node properties."""
        # TODO: Implement property editing dialog
        QMessageBox.information(None, "Edit Properties", 
                               f"Property editing for {self.node.name} not yet implemented")


class PortGraphicsItem(QGraphicsEllipseItem):
    """Graphics item representing a node port."""
    
    def __init__(self, port, port_direction: str, parent=None):
        super().__init__(parent)
        self.port = port
        self.port_direction = port_direction  # "input" or "output"
        self.connections: List['ConnectionGraphicsItem'] = []
        self.setup_appearance()
        
    def setup_appearance(self):
        """Set up the visual appearance of the port."""
        size = 12
        self.setRect(-size/2, -size/2, size, size)
        
        # Color based on data type
        colors = {
            "execution": QColor(255, 255, 255),
            "string": QColor(255, 200, 200),
            "number": QColor(200, 255, 200),
            "boolean": QColor(200, 200, 255),
            "object": QColor(255, 255, 200),
        }
        
        color = colors.get(self.port.data_type, QColor(200, 200, 200))
        self.setBrush(QBrush(color))
        self.setPen(QPen(QColor(50, 50, 50), 1))
        
        # Add tooltip
        self.setToolTip(f"{self.port.name} ({self.port.data_type})")
        
    def mousePressEvent(self, event):
        """Handle mouse press for connection creation."""
        if event.button() == Qt.LeftButton:
            # Start connection creation
            if hasattr(self.scene(), 'start_connection'):
                self.scene().start_connection(self)
        super().mousePressEvent(event)


class ConnectionGraphicsItem(QGraphicsLineItem):
    """Graphics item representing a connection between ports."""
    
    def __init__(self, connection: LogicConnection, 
                 source_port: PortGraphicsItem, 
                 target_port: PortGraphicsItem, parent=None):
        super().__init__(parent)
        self.connection = connection
        self.source_port = source_port
        self.target_port = target_port
        self.setup_appearance()
        self.update_line()
        
    def setup_appearance(self):
        """Set up the visual appearance of the connection."""
        # Color based on connection type
        colors = {
            "execution": QColor(255, 255, 255),
            "data": QColor(100, 150, 255),
            "event": QColor(255, 150, 100),
        }
        
        color = colors.get(self.connection.connection_type.value, QColor(150, 150, 150))
        pen = QPen(color, 3)
        self.setPen(pen)
        
    def update_line(self):
        """Update the line position based on port positions."""
        if self.source_port and self.target_port:
            source_pos = self.source_port.scenePos()
            target_pos = self.target_port.scenePos()
            self.setLine(source_pos.x(), source_pos.y(), 
                        target_pos.x(), target_pos.y())


class LogicGraphView(QGraphicsView):
    """Graphics view for displaying and editing logic graphs."""
    
    node_selected = pyqtSignal(object)  # Emitted when a node is selected
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.scene = QGraphicsScene(self)
        self.setScene(self.scene)
        
        # Set up view properties
        self.setDragMode(QGraphicsView.RubberBandDrag)
        self.setRenderHint(QPainter.Antialiasing)
        self.setViewportUpdateMode(QGraphicsView.FullViewportUpdate)
        
        # Graph data
        self.graph: Optional[LogicGraph] = None
        self.node_items: Dict[str, NodeGraphicsItem] = {}
        self.connection_items: Dict[str, ConnectionGraphicsItem] = {}
        
        # Connection creation state
        self.creating_connection = False
        self.connection_start_port: Optional[PortGraphicsItem] = None
        self.temp_connection_line: Optional[QGraphicsLineItem] = None
        
    def set_graph(self, graph: LogicGraph):
        """Set the graph to display."""
        self.graph = graph
        self.refresh_view()
        
    def refresh_view(self):
        """Refresh the view with current graph data."""
        if not self.graph:
            return
            
        # Clear existing items
        self.scene.clear()
        self.node_items.clear()
        self.connection_items.clear()
        
        # Add nodes
        for node_id, node in self.graph.nodes.items():
            node_item = NodeGraphicsItem(node)
            self.scene.addItem(node_item)
            self.node_items[node_id] = node_item
            
        # Add connections
        for conn_id, connection in self.graph.connections.items():
            source_node_item = self.node_items.get(connection.source_node_id)
            target_node_item = self.node_items.get(connection.target_node_id)
            
            if source_node_item and target_node_item:
                # Find the specific ports
                source_port = None
                target_port = None
                
                for port in source_node_item.output_ports:
                    if port.port.id == connection.source_port_id:
                        source_port = port
                        break
                        
                for port in target_node_item.input_ports:
                    if port.port.id == connection.target_port_id:
                        target_port = port
                        break
                        
                if source_port and target_port:
                    conn_item = ConnectionGraphicsItem(connection, source_port, target_port)
                    self.scene.addItem(conn_item)
                    self.connection_items[conn_id] = conn_item
                    
    def start_connection(self, port: PortGraphicsItem):
        """Start creating a connection from a port."""
        if port.port_direction == "output":
            self.creating_connection = True
            self.connection_start_port = port
            
            # Create temporary line
            self.temp_connection_line = QGraphicsLineItem()
            self.temp_connection_line.setPen(QPen(QColor(255, 255, 255), 2))
            self.scene.addItem(self.temp_connection_line)
            
    def mouseMoveEvent(self, event):
        """Handle mouse move for connection creation."""
        if self.creating_connection and self.temp_connection_line:
            start_pos = self.connection_start_port.scenePos()
            end_pos = self.mapToScene(event.pos())
            self.temp_connection_line.setLine(start_pos.x(), start_pos.y(),
                                            end_pos.x(), end_pos.y())
        super().mouseMoveEvent(event)
        
    def mouseReleaseEvent(self, event):
        """Handle mouse release for connection creation."""
        if self.creating_connection:
            # Check if we're over an input port
            item = self.itemAt(event.pos())
            if isinstance(item, PortGraphicsItem) and item.port_direction == "input":
                self.create_connection(self.connection_start_port, item)
                
            # Clean up
            if self.temp_connection_line:
                self.scene.removeItem(self.temp_connection_line)
                self.temp_connection_line = None
                
            self.creating_connection = False
            self.connection_start_port = None
            
        super().mouseReleaseEvent(event)
        
    def create_connection(self, source_port: PortGraphicsItem, target_port: PortGraphicsItem):
        """Create a connection between two ports."""
        if not self.graph:
            return
            
        # Create connection object
        connection = LogicConnection(
            source_node_id=source_port.parentItem().node.id,
            source_port_id=source_port.port.id,
            target_node_id=target_port.parentItem().node.id,
            target_port_id=target_port.port.id
        )
        
        try:
            # Add to graph
            self.graph.add_connection(connection)
            
            # Create visual representation
            conn_item = ConnectionGraphicsItem(connection, source_port, target_port)
            self.scene.addItem(conn_item)
            self.connection_items[connection.id] = conn_item
            
        except ValueError as e:
            QMessageBox.warning(self, "Connection Error", str(e))
            
    def wheelEvent(self, event):
        """Handle zoom with mouse wheel."""
        zoom_factor = 1.15
        if event.angleDelta().y() < 0:
            zoom_factor = 1.0 / zoom_factor
            
        self.scale(zoom_factor, zoom_factor)


class LogicGraphEditor(QWidget):
    """Complete logic graph editor widget."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.graph: Optional[LogicGraph] = None
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)
        
        # Toolbar
        toolbar = QToolBar()
        layout.addWidget(toolbar)
        
        # Add node buttons
        node_types = [
            ("Event", EventNode),
            ("Action", ActionNode),
            ("Condition", ConditionNode),
            ("Variable", VariableNode),
            ("Function", FunctionNode),
            ("Trigger", TriggerNode),
            ("Data", DataNode),
        ]
        
        for name, node_class in node_types:
            btn = QPushButton(f"Add {name}")
            btn.clicked.connect(lambda checked, cls=node_class: self.add_node(cls))
            toolbar.addWidget(btn)
            
        toolbar.addSeparator()
        
        # Graph operations
        save_btn = QPushButton("Save")
        save_btn.clicked.connect(self.save_graph)
        toolbar.addWidget(save_btn)
        
        load_btn = QPushButton("Load")
        load_btn.clicked.connect(self.load_graph)
        toolbar.addWidget(load_btn)
        
        # Main content area
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # Graph view
        self.graph_view = LogicGraphView()
        splitter.addWidget(self.graph_view)
        
        # Properties panel (placeholder)
        properties_widget = QWidget()
        properties_layout = QVBoxLayout(properties_widget)
        properties_layout.addWidget(QLabel("Properties"))
        properties_layout.addWidget(QLabel("Select a node to edit properties"))
        splitter.addWidget(properties_widget)
        
        # Set splitter proportions
        splitter.setSizes([800, 200])
        
        # Create default graph
        self.new_graph()
        
    def new_graph(self):
        """Create a new empty graph."""
        self.graph = LogicGraph("New Logic Graph", "A new logic graph")
        self.graph_view.set_graph(self.graph)
        
    def add_node(self, node_class):
        """Add a new node to the graph."""
        if not self.graph:
            return
            
        # Create node
        node = node_class()
        node.x = 100
        node.y = 100
        
        # Add to graph
        self.graph.add_node(node)
        
        # Refresh view
        self.graph_view.refresh_view()
        
    def save_graph(self):
        """Save the current graph."""
        if not self.graph:
            return
            
        # For now, just show the YAML representation
        yaml_str = LogicGraphSerializer.to_yaml(self.graph)
        QMessageBox.information(self, "Graph YAML", yaml_str)
        
    def load_graph(self):
        """Load a graph from YAML."""
        # TODO: Implement file dialog and loading
        QMessageBox.information(self, "Load Graph", "Graph loading not yet implemented")

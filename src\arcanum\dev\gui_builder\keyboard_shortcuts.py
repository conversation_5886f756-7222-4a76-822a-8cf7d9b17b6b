"""
Professional keyboard shortcuts system for Arcanum GUI Builder.
"""

from typing import Dict, Callable, Optional
from PyQt5.QtWidgets import QWidget, QShortcut
from PyQt5.QtGui import Q<PERSON>eySequence
from PyQt5.QtCore import Qt


class KeyboardShortcutManager:
    """Manages keyboard shortcuts for the GUI Builder."""
    
    def __init__(self, parent_widget: QWidget):
        self.parent = parent_widget
        self.shortcuts: Dict[str, QShortcut] = {}
        self.setup_default_shortcuts()
        
    def setup_default_shortcuts(self):
        """Set up default keyboard shortcuts."""
        # File operations
        self.add_shortcut("Ctrl+N", "new_project", "New Project")
        self.add_shortcut("Ctrl+O", "open_project", "Open Project")
        self.add_shortcut("Ctrl+S", "save_project", "Save Project")
        self.add_shortcut("Ctrl+Shift+S", "save_as", "Save As")
        self.add_shortcut("<PERSON>tr<PERSON>+Q", "quit", "Quit")
        
        # Edit operations
        self.add_shortcut("Ctrl+Z", "undo", "Undo")
        self.add_shortcut("Ctrl+Y", "redo", "Redo")
        self.add_shortcut("Ctrl+Shift+Z", "redo", "Redo (Alternative)")
        self.add_shortcut("Ctrl+C", "copy", "Copy")
        self.add_shortcut("Ctrl+V", "paste", "Paste")
        self.add_shortcut("Ctrl+X", "cut", "Cut")
        self.add_shortcut("Ctrl+A", "select_all", "Select All")
        self.add_shortcut("Delete", "delete", "Delete")
        self.add_shortcut("Ctrl+D", "duplicate", "Duplicate")
        
        # View operations
        self.add_shortcut("F5", "preview", "Preview")
        self.add_shortcut("Ctrl+R", "refresh", "Refresh")
        self.add_shortcut("Ctrl+0", "zoom_reset", "Reset Zoom")
        self.add_shortcut("Ctrl++", "zoom_in", "Zoom In")
        self.add_shortcut("Ctrl+-", "zoom_out", "Zoom Out")
        self.add_shortcut("Ctrl+1", "zoom_fit", "Fit to Window")
        
        # Navigation
        self.add_shortcut("Ctrl+Tab", "next_tab", "Next Tab")
        self.add_shortcut("Ctrl+Shift+Tab", "prev_tab", "Previous Tab")
        self.add_shortcut("Ctrl+1", "switch_to_design", "Switch to Design")
        self.add_shortcut("Ctrl+2", "switch_to_code", "Switch to Code")
        self.add_shortcut("Ctrl+3", "switch_to_preview", "Switch to Preview")
        
        # Widget operations
        self.add_shortcut("Ctrl+G", "group_widgets", "Group Widgets")
        self.add_shortcut("Ctrl+Shift+G", "ungroup_widgets", "Ungroup Widgets")
        self.add_shortcut("Ctrl+L", "lock_widget", "Lock Widget")
        self.add_shortcut("Ctrl+Shift+L", "unlock_widget", "Unlock Widget")
        
        # Alignment shortcuts
        self.add_shortcut("Ctrl+Shift+Left", "align_left", "Align Left")
        self.add_shortcut("Ctrl+Shift+Right", "align_right", "Align Right")
        self.add_shortcut("Ctrl+Shift+Up", "align_top", "Align Top")
        self.add_shortcut("Ctrl+Shift+Down", "align_bottom", "Align Bottom")
        self.add_shortcut("Ctrl+Shift+H", "align_center_h", "Align Center Horizontal")
        self.add_shortcut("Ctrl+Shift+V", "align_center_v", "Align Center Vertical")
        
        # Layer operations
        self.add_shortcut("Ctrl+Shift+]", "bring_forward", "Bring Forward")
        self.add_shortcut("Ctrl+Shift+[", "send_backward", "Send Backward")
        self.add_shortcut("Ctrl+]", "bring_to_front", "Bring to Front")
        self.add_shortcut("Ctrl+[", "send_to_back", "Send to Back")
        
        # Grid and snapping
        self.add_shortcut("Ctrl+;", "toggle_grid", "Toggle Grid")
        self.add_shortcut("Ctrl+Shift+;", "toggle_snap", "Toggle Snap to Grid")
        
        # Quick widget creation
        self.add_shortcut("B", "add_button", "Add Button")
        self.add_shortcut("T", "add_text", "Add Text")
        self.add_shortcut("I", "add_input", "Add Input")
        self.add_shortcut("L", "add_label", "Add Label")
        self.add_shortcut("M", "add_image", "Add Image")
        
        # Panel toggles
        self.add_shortcut("F9", "toggle_palette", "Toggle Widget Palette")
        self.add_shortcut("F10", "toggle_properties", "Toggle Properties Panel")
        self.add_shortcut("F11", "toggle_yaml", "Toggle YAML Editor")
        self.add_shortcut("F12", "toggle_navigator", "Toggle Page Navigator")
        
        # Search and find
        self.add_shortcut("Ctrl+F", "find", "Find")
        self.add_shortcut("Ctrl+H", "find_replace", "Find and Replace")
        self.add_shortcut("F3", "find_next", "Find Next")
        self.add_shortcut("Shift+F3", "find_previous", "Find Previous")
        
        # Help
        self.add_shortcut("F1", "help", "Help")
        self.add_shortcut("Ctrl+?", "shortcuts_help", "Keyboard Shortcuts Help")
        
    def add_shortcut(self, key_sequence: str, action_name: str, description: str = ""):
        """Add a keyboard shortcut."""
        if action_name in self.shortcuts:
            # Remove existing shortcut
            self.shortcuts[action_name].deleteLater()
            
        shortcut = QShortcut(QKeySequence(key_sequence), self.parent)
        shortcut.setContext(Qt.ApplicationShortcut)
        
        # Store shortcut info
        shortcut.action_name = action_name
        shortcut.description = description
        shortcut.key_sequence = key_sequence
        
        self.shortcuts[action_name] = shortcut
        
    def connect_shortcut(self, action_name: str, callback: Callable):
        """Connect a shortcut to a callback function."""
        if action_name in self.shortcuts:
            self.shortcuts[action_name].activated.connect(callback)
            
    def remove_shortcut(self, action_name: str):
        """Remove a keyboard shortcut."""
        if action_name in self.shortcuts:
            self.shortcuts[action_name].deleteLater()
            del self.shortcuts[action_name]
            
    def get_shortcut_info(self, action_name: str) -> Optional[Dict[str, str]]:
        """Get information about a shortcut."""
        if action_name in self.shortcuts:
            shortcut = self.shortcuts[action_name]
            return {
                'action': action_name,
                'key_sequence': shortcut.key_sequence,
                'description': shortcut.description
            }
        return None
        
    def get_all_shortcuts(self) -> Dict[str, Dict[str, str]]:
        """Get information about all shortcuts."""
        shortcuts_info = {}
        for action_name, shortcut in self.shortcuts.items():
            shortcuts_info[action_name] = {
                'key_sequence': shortcut.key_sequence,
                'description': shortcut.description
            }
        return shortcuts_info
        
    def disable_shortcut(self, action_name: str):
        """Temporarily disable a shortcut."""
        if action_name in self.shortcuts:
            self.shortcuts[action_name].setEnabled(False)
            
    def enable_shortcut(self, action_name: str):
        """Re-enable a disabled shortcut."""
        if action_name in self.shortcuts:
            self.shortcuts[action_name].setEnabled(True)
            
    def disable_all_shortcuts(self):
        """Disable all shortcuts (useful for modal dialogs)."""
        for shortcut in self.shortcuts.values():
            shortcut.setEnabled(False)
            
    def enable_all_shortcuts(self):
        """Re-enable all shortcuts."""
        for shortcut in self.shortcuts.values():
            shortcut.setEnabled(True)
            
    def get_shortcuts_by_category(self) -> Dict[str, Dict[str, Dict[str, str]]]:
        """Get shortcuts organized by category."""
        categories = {
            'File': ['new_project', 'open_project', 'save_project', 'save_as', 'quit'],
            'Edit': ['undo', 'redo', 'copy', 'paste', 'cut', 'select_all', 'delete', 'duplicate'],
            'View': ['preview', 'refresh', 'zoom_reset', 'zoom_in', 'zoom_out', 'zoom_fit'],
            'Navigation': ['next_tab', 'prev_tab', 'switch_to_design', 'switch_to_code', 'switch_to_preview'],
            'Widget': ['group_widgets', 'ungroup_widgets', 'lock_widget', 'unlock_widget'],
            'Alignment': ['align_left', 'align_right', 'align_top', 'align_bottom', 'align_center_h', 'align_center_v'],
            'Layer': ['bring_forward', 'send_backward', 'bring_to_front', 'send_to_back'],
            'Grid': ['toggle_grid', 'toggle_snap'],
            'Quick Add': ['add_button', 'add_text', 'add_input', 'add_label', 'add_image'],
            'Panels': ['toggle_palette', 'toggle_properties', 'toggle_yaml', 'toggle_navigator'],
            'Search': ['find', 'find_replace', 'find_next', 'find_previous'],
            'Help': ['help', 'shortcuts_help']
        }
        
        organized_shortcuts = {}
        for category, actions in categories.items():
            organized_shortcuts[category] = {}
            for action in actions:
                if action in self.shortcuts:
                    shortcut = self.shortcuts[action]
                    organized_shortcuts[category][action] = {
                        'key_sequence': shortcut.key_sequence,
                        'description': shortcut.description
                    }
                    
        return organized_shortcuts
        
    def create_shortcuts_help_text(self) -> str:
        """Create a formatted help text for all shortcuts."""
        help_text = "Keyboard Shortcuts\n" + "=" * 50 + "\n\n"
        
        categories = self.get_shortcuts_by_category()
        for category, shortcuts in categories.items():
            if shortcuts:  # Only show categories that have shortcuts
                help_text += f"{category}\n" + "-" * len(category) + "\n"
                for action, info in shortcuts.items():
                    help_text += f"{info['key_sequence']:<20} {info['description']}\n"
                help_text += "\n"
                
        return help_text
        
    def export_shortcuts_config(self) -> Dict[str, str]:
        """Export shortcuts configuration for saving/loading."""
        config = {}
        for action_name, shortcut in self.shortcuts.items():
            config[action_name] = shortcut.key_sequence
        return config
        
    def import_shortcuts_config(self, config: Dict[str, str]):
        """Import shortcuts configuration."""
        for action_name, key_sequence in config.items():
            if action_name in self.shortcuts:
                # Update existing shortcut
                self.shortcuts[action_name].setKey(QKeySequence(key_sequence))
                self.shortcuts[action_name].key_sequence = key_sequence

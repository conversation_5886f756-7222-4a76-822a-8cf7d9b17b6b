"""
Core configuration management for Arcanum projects.
"""

import yaml
from pathlib import Path
from typing import Optional, Dict, Any
from pydantic import ValidationError

from ..schemas.config import ArcanumConfigSchema


class ArcanumConfig:
    """
    Manages loading, validation, and access to Arcanum project configuration.
    """
    
    def __init__(self, config_path: Optional[Path] = None):
        """
        Initialize configuration manager.
        
        Args:
            config_path: Path to Arcanum.yaml file. If None, looks for it in current directory.
        """
        self.config_path = config_path or Path("Arcanum.yaml")
        self._config: Optional[ArcanumConfigSchema] = None
        self._raw_config: Optional[Dict[str, Any]] = None
        
    def load(self) -> ArcanumConfigSchema:
        """
        Load and validate configuration from YAML file.
        
        Returns:
            Validated configuration object.
            
        Raises:
            FileNotFoundError: If config file doesn't exist.
            ValidationError: If config validation fails.
            yaml.YAMLError: If YAML parsing fails.
        """
        if not self.config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self._raw_config = yaml.safe_load(f)
                
            if self._raw_config is None:
                self._raw_config = {}
                
            self._config = ArcanumConfigSchema(**self._raw_config)
            return self._config
            
        except yaml.YAMLError as e:
            raise yaml.YAMLError(f"Failed to parse YAML configuration: {e}")
        except ValidationError as e:
            raise ValidationError(f"Configuration validation failed: {e}")
    
    def save(self, config: Optional[ArcanumConfigSchema] = None) -> None:
        """
        Save configuration to YAML file.
        
        Args:
            config: Configuration to save. If None, saves current loaded config.
            
        Raises:
            ValueError: If no configuration is loaded or provided.
        """
        config_to_save = config or self._config
        if config_to_save is None:
            raise ValueError("No configuration to save")
            
        # Convert to dict and save as YAML
        config_dict = config_to_save.dict()
        
        # Ensure parent directory exists
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(self.config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, indent=2, sort_keys=False)
            
        self._config = config_to_save
        self._raw_config = config_dict
    
    def reload(self) -> ArcanumConfigSchema:
        """
        Reload configuration from file.
        
        Returns:
            Reloaded configuration object.
        """
        return self.load()
    
    @property
    def config(self) -> Optional[ArcanumConfigSchema]:
        """Get the loaded configuration."""
        return self._config
    
    @property
    def raw_config(self) -> Optional[Dict[str, Any]]:
        """Get the raw configuration dictionary."""
        return self._raw_config
    
    def is_loaded(self) -> bool:
        """Check if configuration is loaded."""
        return self._config is not None
    
    def validate_paths(self, project_root: Path) -> Dict[str, bool]:
        """
        Validate that all configured paths exist relative to project root.
        
        Args:
            project_root: Root directory of the project.
            
        Returns:
            Dictionary mapping path names to existence status.
        """
        if not self._config:
            raise ValueError("Configuration not loaded")
            
        paths_to_check = {
            "registry_path": self._config.registry_path,
            "ui_path": self._config.ui_path,
            "schemas_path": self._config.schemas_path,
            "plugins_path": self._config.plugins_path,
            "user_data_path": self._config.user_data_path,
        }
        
        results = {}
        for name, path in paths_to_check.items():
            full_path = project_root / path
            results[name] = full_path.exists()
            
        return results
    
    def get_theme(self, theme_name: Optional[str] = None) -> Optional[Dict[str, str]]:
        """
        Get theme configuration by name.
        
        Args:
            theme_name: Name of theme to get. If None, uses current theme.
            
        Returns:
            Theme configuration dictionary or None if not found.
        """
        if not self._config:
            return None
            
        theme_name = theme_name or self._config.theme
        theme_config = self._config.themes.get(theme_name)
        
        if theme_config:
            return theme_config.dict()
        return None
    
    def update_config(self, **kwargs) -> None:
        """
        Update configuration values.
        
        Args:
            **kwargs: Configuration values to update.
        """
        if not self._config:
            raise ValueError("Configuration not loaded")
            
        # Create new config with updated values
        config_dict = self._config.dict()
        config_dict.update(kwargs)
        
        # Validate and update
        self._config = ArcanumConfigSchema(**config_dict)
        self._raw_config = config_dict

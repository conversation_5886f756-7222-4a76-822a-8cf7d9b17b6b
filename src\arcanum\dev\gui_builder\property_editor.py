"""
Property Editor for the Arcanum GUI Builder.
"""

from typing import Dict, Any, Optional, List
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QTextEdit, QCheckBox, QComboBox, QSpinBox, QDoubleSpinBox,
    QGroupBox, QScrollArea, QFormLayout, QPushButton, QColorDialog
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor


class PropertyWidget(QWidget):
    """
    Base class for property editing widgets.
    """
    
    value_changed = pyqtSignal(str, object)  # property_name, value
    
    def __init__(self, property_name: str, property_value: Any, parent=None):
        super().__init__(parent)
        self.property_name = property_name
        self.property_value = property_value
        
    def get_value(self) -> Any:
        """Get the current value."""
        return self.property_value
        
    def set_value(self, value: Any):
        """Set the value."""
        self.property_value = value


class TextPropertyWidget(PropertyWidget):
    """Property widget for text values."""

    def __init__(self, property_name: str, property_value: str, parent=None, read_only: bool = False):
        super().__init__(property_name, property_value, parent)

        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        self.line_edit = QLineEdit(str(property_value or ""))
        self.line_edit.setReadOnly(read_only)
        if read_only:
            self.line_edit.setStyleSheet("QLineEdit { background-color: #f0f0f0; color: #666; }")
            self.line_edit.setToolTip("This is a unique identifier and cannot be changed")
        self.line_edit.textChanged.connect(self.on_value_changed)
        layout.addWidget(self.line_edit)
        
    def on_value_changed(self, text: str):
        """Handle value changes."""
        self.property_value = text
        self.value_changed.emit(self.property_name, text)
        
    def set_value(self, value: Any):
        """Set the value."""
        super().set_value(value)
        self.line_edit.setText(str(value or ""))


class NumberPropertyWidget(PropertyWidget):
    """Property widget for numeric values."""
    
    def __init__(self, property_name: str, property_value: float, is_integer: bool = False, parent=None):
        super().__init__(property_name, property_value, parent)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        if is_integer:
            self.spin_box = QSpinBox()
            self.spin_box.setRange(-999999, 999999)
            self.spin_box.setValue(int(property_value or 0))
            self.spin_box.valueChanged.connect(self.on_value_changed)
        else:
            self.spin_box = QDoubleSpinBox()
            self.spin_box.setRange(-999999.0, 999999.0)
            self.spin_box.setValue(float(property_value or 0.0))
            self.spin_box.valueChanged.connect(self.on_value_changed)
            
        layout.addWidget(self.spin_box)
        
    def on_value_changed(self, value):
        """Handle value changes."""
        self.property_value = value
        self.value_changed.emit(self.property_name, value)
        
    def set_value(self, value: Any):
        """Set the value."""
        super().set_value(value)
        self.spin_box.setValue(value or 0)


class BooleanPropertyWidget(PropertyWidget):
    """Property widget for boolean values."""
    
    def __init__(self, property_name: str, property_value: bool, parent=None):
        super().__init__(property_name, property_value, parent)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        self.checkbox = QCheckBox()
        self.checkbox.setChecked(bool(property_value))
        self.checkbox.toggled.connect(self.on_value_changed)
        layout.addWidget(self.checkbox)
        
    def on_value_changed(self, checked: bool):
        """Handle value changes."""
        self.property_value = checked
        self.value_changed.emit(self.property_name, checked)
        
    def set_value(self, value: Any):
        """Set the value."""
        super().set_value(value)
        self.checkbox.setChecked(bool(value))


class ChoicePropertyWidget(PropertyWidget):
    """Property widget for choice values."""
    
    def __init__(self, property_name: str, property_value: str, choices: list, parent=None):
        super().__init__(property_name, property_value, parent)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        self.combo_box = QComboBox()
        self.combo_box.addItems([str(choice) for choice in choices])
        
        # Set current value
        if property_value in choices:
            self.combo_box.setCurrentText(str(property_value))
            
        self.combo_box.currentTextChanged.connect(self.on_value_changed)
        layout.addWidget(self.combo_box)
        
    def on_value_changed(self, text: str):
        """Handle value changes."""
        self.property_value = text
        self.value_changed.emit(self.property_name, text)
        
    def set_value(self, value: Any):
        """Set the value."""
        super().set_value(value)
        self.combo_box.setCurrentText(str(value))


class ColorPropertyWidget(PropertyWidget):
    """Property widget for color values."""
    
    def __init__(self, property_name: str, property_value: str, parent=None):
        super().__init__(property_name, property_value, parent)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        self.color_button = QPushButton()
        self.color_button.setMaximumWidth(50)
        self.color_button.clicked.connect(self.choose_color)
        
        self.color_label = QLabel(str(property_value or "#000000"))
        
        layout.addWidget(self.color_button)
        layout.addWidget(self.color_label)
        
        self.update_color_button()
        
    def update_color_button(self):
        """Update the color button appearance."""
        color = QColor(self.property_value or "#000000")
        self.color_button.setStyleSheet(f"background-color: {color.name()};")
        
    def choose_color(self):
        """Open color chooser dialog."""
        color = QColorDialog.getColor(QColor(self.property_value or "#000000"), self)
        if color.isValid():
            self.property_value = color.name()
            self.color_label.setText(color.name())
            self.update_color_button()
            self.value_changed.emit(self.property_name, color.name())
            
    def set_value(self, value: Any):
        """Set the value."""
        super().set_value(value)
        self.color_label.setText(str(value or "#000000"))
        self.update_color_button()


class PropertyEditor(QWidget):
    """
    Property editor for selected widgets and layouts.
    """

    # Signals
    property_changed = pyqtSignal(str, object)  # property_name, value
    widget_deleted = pyqtSignal()  # Signal when widget is deleted
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_item: Optional[Dict[str, Any]] = None
        self.property_widgets: Dict[str, PropertyWidget] = {}
        
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the property editor UI."""
        layout = QVBoxLayout(self)
        
        # Title
        title_label = QLabel("Properties")
        title_font = QFont()
        title_font.setBold(True)
        title_font.setPointSize(12)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Scroll area for properties
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        # Container for property groups
        self.properties_widget = QWidget()
        self.properties_layout = QVBoxLayout(self.properties_widget)
        
        scroll_area.setWidget(self.properties_widget)
        layout.addWidget(scroll_area)
        
        # No selection message
        self.no_selection_label = QLabel("No item selected")
        self.no_selection_label.setAlignment(Qt.AlignCenter)
        self.no_selection_label.setStyleSheet("color: #6c757d; font-style: italic;")
        layout.addWidget(self.no_selection_label)
        
        # Initially show no selection
        self.show_no_selection()
        
    def show_no_selection(self):
        """Show the no selection state."""
        self.properties_widget.hide()
        self.no_selection_label.show()
        
    def show_properties(self):
        """Show the properties editor."""
        self.no_selection_label.hide()
        self.properties_widget.show()
        
    def set_selected_item(self, item_data: Dict[str, Any]):
        """Set the currently selected item."""
        # Force update even if it's the same item to ensure responsiveness
        self.current_item = item_data.copy() if item_data else None
        self.refresh_properties()
        
    def refresh_properties(self):
        """Refresh the properties display."""
        # Clear existing property widgets
        self.clear_properties()
        
        if not self.current_item:
            self.show_no_selection()
            return
            
        self.show_properties()
        
        # Create property groups
        self.create_validation_warnings()
        self.create_basic_properties()
        self.create_position_properties()
        self.create_style_properties()
        self.create_behavior_properties()
        self.create_actions_section()

        # Add stretch to push properties to top
        self.properties_layout.addStretch()

    def create_validation_warnings(self):
        """Create validation warnings section."""
        from ...core.widget_utils import validate_widget_requirements

        warnings = validate_widget_requirements(self.current_item)
        if not warnings:
            return

        # Create warnings group
        warnings_group = QGroupBox("⚠️ Validation Warnings")
        warnings_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                color: #d63031;
                border: 2px solid #fab1a0;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #ffeaa7;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        warnings_layout = QVBoxLayout(warnings_group)

        for warning in warnings:
            warning_label = QLabel(f"• {warning}")
            warning_label.setWordWrap(True)
            warning_label.setStyleSheet("color: #2d3436; font-weight: normal; margin: 2px;")
            warnings_layout.addWidget(warning_label)

        self.properties_layout.addWidget(warnings_group)

    def clear_properties(self):
        """Clear all property widgets."""
        while self.properties_layout.count():
            child = self.properties_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        self.property_widgets.clear()

    def reset_properties(self):
        """Reset the property editor to no selection state."""
        self.current_item = None
        self.clear_properties()
        self.show_no_selection()
        
    def create_basic_properties(self):
        """Create basic property editors."""
        group = QGroupBox("Basic Properties")
        form_layout = QFormLayout(group)

        # Get widget type to determine available properties
        widget_type = self.current_item.get("widget", "")

        # Common properties for all widgets
        basic_props = {
            "id": ("text", "ID"),
            "label": ("text", "Label"),
            "required": ("boolean", "Required"),
            "disabled": ("boolean", "Disabled"),
            "visible": ("boolean", "Visible"),
        }

        # Widget-specific properties
        if widget_type == "text":
            basic_props.update({
                "placeholder": ("text", "Placeholder"),
                "default_value": ("text", "Default Value"),
                "max_length": ("number", "Max Length"),
                "min_length": ("number", "Min Length"),
                "pattern": ("text", "Pattern (Regex)"),
            })
        elif widget_type == "textarea":
            basic_props.update({
                "placeholder": ("text", "Placeholder"),
                "default_value": ("text", "Default Value"),
                "rows": ("number", "Rows"),
                "max_length": ("number", "Max Length"),
            })
        elif widget_type == "button":
            basic_props.update({
                "label": ("text", "Button Text"),
                "button_type": ("choice", "Button Type"),
                "variant": ("choice", "Variant"),
            })
        elif widget_type == "label":
            basic_props.update({
                "text": ("text", "Text Content"),
                "html": ("boolean", "Allow HTML"),
            })
        elif widget_type == "checkbox":
            basic_props.update({
                "checked": ("boolean", "Checked"),
            })
        elif widget_type == "dropdown":
            basic_props.update({
                "options": ("text", "Options (JSON)"),
                "multiple": ("boolean", "Multiple Selection"),
                "searchable": ("boolean", "Searchable"),
            })
        elif widget_type == "number":
            basic_props.update({
                "placeholder": ("text", "Placeholder"),
                "default_value": ("number", "Default Value"),
                "min_value": ("number", "Minimum Value"),
                "max_value": ("number", "Maximum Value"),
                "step": ("number", "Step"),
                "decimal_places": ("number", "Decimal Places"),
            })
        elif widget_type == "image":
            basic_props.update({
                "src": ("text", "Image Source"),
                "alt": ("text", "Alt Text"),
                "width": ("text", "Width"),
                "height": ("text", "Height"),
            })

        # Add properties to form
        for prop_name, (prop_type, display_name) in basic_props.items():
            value = self.current_item.get(prop_name, self._get_default_value(prop_name, prop_type))

            # Special handling for choice properties
            choices = []
            if prop_type == "choice":
                if prop_name == "button_type":
                    choices = ["button", "submit", "reset"]
                elif prop_name == "variant":
                    choices = ["primary", "secondary", "success", "warning", "danger"]

            # Special handling for ID field
            if prop_name == "id":
                widget = self.create_property_widget(prop_name, prop_type, value, choices=choices)
                if widget:
                    # Style the ID field to indicate it's required
                    if not value:
                        widget.setStyleSheet("QLineEdit { border: 2px solid #e17055; background-color: #ffeaa7; }")
                    widget.value_changed.connect(self.on_property_changed)
                    id_label = QLabel(display_name + " *")
                    id_label.setStyleSheet("font-weight: bold; color: #2d3436;")
                    form_layout.addRow(id_label, widget)
                    self.property_widgets[prop_name] = widget
            else:
                widget = self.create_property_widget(prop_name, prop_type, value, choices=choices)
                if widget:
                    form_layout.addRow(display_name + ":", widget)
                    self.property_widgets[prop_name] = widget

        self.properties_layout.addWidget(group)

    def create_position_properties(self):
        """Create position and sizing property editors."""
        group = QGroupBox("Position & Size")
        form_layout = QFormLayout(group)

        # Get position data from widget
        position_data = self.current_item.get("position", {})

        # Position properties
        position_props = {
            "x": ("number", "X Position (px)"),
            "y": ("number", "Y Position (px)"),
            "width": ("number", "Width (px)"),
            "height": ("number", "Height (px)"),
            "z_index": ("number", "Z-Index"),
            "snap_to_grid": ("boolean", "Snap to Grid"),
            "locked": ("boolean", "Locked"),
            "resizable": ("boolean", "Resizable"),
        }

        # Add properties to form
        for prop_name, (prop_type, display_name) in position_props.items():
            value = position_data.get(prop_name, self._get_position_default(prop_name))

            widget = self.create_property_widget(f"position.{prop_name}", prop_type, value, is_integer=True)
            if widget:
                form_layout.addRow(display_name + ":", widget)
                self.property_widgets[f"position.{prop_name}"] = widget

        self.properties_layout.addWidget(group)

    def _get_position_default(self, prop_name: str) -> Any:
        """Get default value for position properties."""
        defaults = {
            "x": 50,
            "y": 50,
            "width": 200,
            "height": 35,
            "z_index": 10,
            "snap_to_grid": True,
            "locked": False,
            "resizable": True,
        }
        return defaults.get(prop_name, 0)

    def create_style_properties(self):
        """Create style property editors."""
        group = QGroupBox("Style & Appearance")
        form_layout = QFormLayout(group)

        # Style properties
        style_props = {
            "css_class": ("text", "CSS Class"),
            "style": ("text", "Inline Style"),
        }

        # Add common styling options
        style_props.update({
            "background_color": ("color", "Background Color"),
            "color": ("color", "Text Color"),
            "font_size": ("text", "Font Size (e.g., 16px)"),
            "font_weight": ("choice", "Font Weight"),
            "text_align": ("choice", "Text Alignment"),
            "padding": ("text", "Padding (e.g., 10px)"),
            "margin": ("text", "Margin (e.g., 5px)"),
            "border": ("text", "Border (e.g., 1px solid #ccc)"),
            "border_radius": ("text", "Border Radius (e.g., 4px)"),
            "width": ("text", "Width (e.g., 100px, 50%)"),
            "height": ("text", "Height (e.g., 40px)"),
        })

        for prop_name, (prop_type, display_name) in style_props.items():
            value = self.current_item.get(prop_name, "")

            # Special handling for choice properties
            choices = []
            if prop_type == "choice":
                if prop_name == "font_weight":
                    choices = ["normal", "bold", "lighter", "bolder", "100", "200", "300", "400", "500", "600", "700", "800", "900"]
                elif prop_name == "text_align":
                    choices = ["left", "center", "right", "justify"]

            widget = self.create_property_widget(prop_name, prop_type, value, choices=choices)
            if widget:
                form_layout.addRow(display_name + ":", widget)
                self.property_widgets[prop_name] = widget

        self.properties_layout.addWidget(group)
        
    def create_behavior_properties(self):
        """Create behavior property editors."""
        group = QGroupBox("Actions & Behavior")
        form_layout = QFormLayout(group)

        # Common behavior properties
        behavior_props = {
            "tooltip": ("text", "Tooltip Text"),
            "help_text": ("text", "Help Text"),
        }

        # Widget-specific behavior properties
        widget_type = self.current_item.get("widget")

        if widget_type == "button":
            behavior_props.update({
                "action_type": ("choice", "Action Type"),
                "action_target": ("text", "Action Target"),
                "action_message": ("text", "Message Text"),
            })
        elif widget_type in ["text", "textarea", "number"]:
            behavior_props.update({
                "validation_required": ("boolean", "Required Field"),
                "validation_message": ("text", "Validation Message"),
            })
        elif widget_type == "dropdown":
            behavior_props.update({
                "data_source": ("text", "Data Source"),
                "binds_to": ("text", "Binds To Field"),
            })

        # Add properties to form
        for prop_name, (prop_type, display_name) in behavior_props.items():
            value = self.current_item.get(prop_name, "")

            # Special handling for choice properties
            choices = []
            if prop_type == "choice" and prop_name == "action_type":
                choices = ["link_to", "submit", "reset", "custom", "show_message"]

            widget = self.create_property_widget(prop_name, prop_type, value, choices=choices)
            if widget:
                form_layout.addRow(display_name + ":", widget)
                self.property_widgets[prop_name] = widget

        self.properties_layout.addWidget(group)

    def create_actions_section(self):
        """Create actions section with delete button."""
        group = QGroupBox("Actions")
        layout = QVBoxLayout(group)

        # Delete button
        delete_button = QPushButton("🗑️ Delete Widget")
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
        """)
        delete_button.clicked.connect(self.delete_widget)
        layout.addWidget(delete_button)

        # Reset size button
        reset_button = QPushButton("↻ Reset Size")
        reset_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        reset_button.clicked.connect(self.reset_widget_size)
        layout.addWidget(reset_button)

        self.properties_layout.addWidget(group)

    def delete_widget(self):
        """Delete the current widget."""
        self.widget_deleted.emit()

    def reset_widget_size(self):
        """Reset widget to default size."""
        if not self.current_item:
            return

        # Get widget type and set default size
        widget_type = self.current_item.get('widget', 'text')
        from ...core.widget_utils import get_default_width, get_default_height

        default_width = get_default_width(widget_type)
        default_height = get_default_height(widget_type)

        # Update position data
        if 'position' not in self.current_item:
            self.current_item['position'] = {}

        self.current_item['position']['width'] = default_width
        self.current_item['position']['height'] = default_height

        # Update property widgets
        if 'position.width' in self.property_widgets:
            self.property_widgets['position.width'].set_value(default_width)
        if 'position.height' in self.property_widgets:
            self.property_widgets['position.height'].set_value(default_height)

        # Emit property change
        self.property_changed.emit('position.width', default_width)
        self.property_changed.emit('position.height', default_height)

    def _get_default_value(self, prop_name: str, prop_type: str) -> Any:
        """Get default value for a property."""
        defaults = {
            "visible": True,
            "required": False,
            "disabled": False,
            "checked": False,
            "multiple": False,
            "searchable": False,
            "html": False,
            "rows": 4,
            "step": 1,
            "decimal_places": 0,
            "button_type": "button",
            "variant": "primary",
            "font_weight": "normal",
            "text_align": "left",
        }

        if prop_type == "boolean":
            return defaults.get(prop_name, False)
        elif prop_type == "number":
            return defaults.get(prop_name, 0)
        else:
            return defaults.get(prop_name, "")
        
    def create_property_widget(self, prop_name: str, prop_type: str, value: Any, **kwargs) -> Optional[PropertyWidget]:
        """Create a property widget based on type."""
        widget = None

        if prop_type == "text":
            read_only = kwargs.get("read_only", False)
            widget = TextPropertyWidget(prop_name, value, read_only=read_only)
        elif prop_type == "number":
            is_integer = kwargs.get("is_integer", False)
            widget = NumberPropertyWidget(prop_name, value, is_integer)
        elif prop_type == "boolean":
            widget = BooleanPropertyWidget(prop_name, value)
        elif prop_type == "choice":
            choices = kwargs.get("choices", [])
            widget = ChoicePropertyWidget(prop_name, value, choices)
        elif prop_type == "color":
            widget = ColorPropertyWidget(prop_name, value)

        if widget:
            widget.value_changed.connect(self.on_property_changed)

        return widget
        
    def on_property_changed(self, property_name: str, value: Any):
        """Handle property value changes."""
        if self.current_item:
            # Handle nested properties (e.g., position.x)
            if '.' in property_name:
                parts = property_name.split('.')
                if len(parts) == 2:
                    parent_key, child_key = parts
                    if parent_key not in self.current_item:
                        self.current_item[parent_key] = {}
                    self.current_item[parent_key][child_key] = value
                else:
                    # Handle deeper nesting if needed
                    current = self.current_item
                    for part in parts[:-1]:
                        if part not in current:
                            current[part] = {}
                        current = current[part]
                    current[parts[-1]] = value
            else:
                self.current_item[property_name] = value

            self.property_changed.emit(property_name, value)

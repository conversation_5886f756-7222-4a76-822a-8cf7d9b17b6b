AI Development Instructions for Arcanum

1. Project Initialization & Setup
Initialize the Arcanum project with the following folder structure and baseline config:
my_app/
├── Arcanum.yaml
├── pages/
├── ui/
│   ├── widgets/
│   ├── themes/
│   └── layouts/
├── schemas/
├── renderers/
├── fiddle/
├── user_data/
├── plugins/
├── docs/

Arcanum.yaml serves as the root config describing app metadata, theme, start layout, logic graph toggle, embedded DB flag, and export targets.

2. Core Config Loader
Develop a parser for Arcanum.yaml to load core settings.
Ensure the app reads and validates the config at startup.
Support toggling features like logic graph and embedded database from this config.

3. YAML Schema Validation
Use Pydantic or equivalent to create schemas for:
Layout definitions (rows, columns, grids, tabs)
Widget definitions (base widgets with props)
Logic graph nodes and edges
Plugin metadata
Ensure all YAML files validate against these schemas on load and save.

4. Widget Registry & Loader
Implement a dynamic widget registry that:
Discovers widgets in /ui/widgets/
Loads their schema and rendering info
Allows runtime registration of new widgets via the Widget Wizard or CLI
Widgets should declare:
Input bindings (e.g., binds_to: username)
Supported props and validation rules
Render hints (type of input control)

5. Layout Engine
Build the layout engine supporting nested layouts:
Row, Column, Flexbox-inspired YAML declarations
Ability to embed images, buttons, text, and nested pages
Conditional display of widgets based on logic
Support linking between pages (e.g., button action triggers page load)

6. Drag-and-Drop GUI Builder
Create a live builder GUI with:
Drag widgets from a palette into the layout canvas
Show live YAML output synced side-by-side
Allow switching between row/column/grid modes
Preview across multiple themes (normal, dark, fantasy, professional)
Save changes back to YAML files instantly
Implement undo/redo support and autosave.

7. Logic Graph System
Develop a node-based logic graph editor:
Nodes represent events (on_submit, on_change) and conditions
Edges define flow and state transitions
Link logic nodes to UI widgets and layouts
Support exporting graph definitions to YAML and runtime execution.

8. Embedded Database
Integrate SQLite or DuckDB for local data storage
Provide API hooks to bind widget fields to DB queries
Enable saving form submissions and querying data within layouts
Support exporting data in JSON, CSV, or YAML

9. Plugin System
Design plugin manifest format (plugin.yaml) with metadata: name, version, author, dependencies
Allow plugins to add widgets, layouts, logic nodes, and renderers
Implement CLI commands to install, update, and remove plugins
Support GitHub-based plugin registry indexing and search

10. Validation & Stats Panel
Create a dashboard showing:
Widget usage count
Duplicate or missing IDs
Schema validation errors and warnings
Allow developers to fix issues directly from this panel

11. Export & Publicize Mode
Build export pipelines to output:
Standalone web apps (HTML, JS, CSS with Tailwind)
Desktop executables via PyInstaller or Electron
Strip internal Arcanum metadata and dev tooling for production builds
Ensure exports work offline and require no additional dependencies

12. Support for Multiple Rendering Backends
Abstract the rendering API so that layouts can render in:
PyQt (desktop)
Web (React/Svelte + Tailwind)
Headless mode (CLI, YAML/json output)
Plan for future WebAssembly renderer for browser-native apps

13. Coding Languages Supported
Python 3.10+ for core engine, CLI, and plugin scripting
Rust for future core runtime (optional at MVP)
JavaScript/TypeScript for web rendering
YAML/JSON for configuration and logic definitions

14. Testing & CI
Implement unit and integration tests covering:
YAML validation
Widget rendering & event handling
Logic graph execution
Plugin loading and isolation
Setup CI to lint, test, and build artifacts

15. Documentation Generation
Auto-generate:
Widget and layout API docs
Tag and field index (human & machine-readable)
Include tutorial and onboarding content visible in the app

Summary
This AI should prioritize a modular, schema-driven architecture with live GUI + YAML sync and strong validation. Emphasize offline-first design, plugin extensibility, and multi-backend rendering. The system should allow both no-code users and developers to build, preview, and export complete apps seamlessly.

Data flow
A. Arcanum Internal Data Flow (App Lifecycle)
This describes how Arcanum runs an app from config to export:

csharp
Copy
Edit
[Arcanum.yaml]
     ⬇
[Project Loader] ──▶ Loads settings, start layout, themes
     ⬇
[Widget Registry] ──▶ Discovers all widgets in /ui/widgets/
     ⬇
[Layout Loader] ──▶ Loads page layouts from /pages/ recursively
     ⬇
[Schema Validator] ──▶ Validates all YAML fields and structures
     ⬇
[Logic Graph Engine] ──▶ Parses any logic_graph.yaml into executable rules
     ⬇
[Renderer Dispatcher] ──▶ Renders based on target:
   ├── PyQt5
   ├── Web (HTML5 + Tailwind)
   └── CLI / JSON
     ⬇
[Local DB Engine] ⬄ [user_data/ or DuckDB]
     ⬇
[Export Pipeline] ──▶
   ├── `publicize/`
   ├── Standalone EXE (via PyInstaller)
   ├── Web Build (HTML/JS)
   └── API spec or schema bundle
👩‍💻 B. Developer Flow Using Arcanum
This represents the journey of a developer or no-code user building their own application using Arcanum:

less
Copy
Edit
[arcanum init] ──▶ Sets up base folders + Arcanum.yaml
     ⬇
[Visual GUI Builder] OR [YAML IDE Editing]
     ⬇
[Drag + drop layouts] + [Widget Wizard]
     ⬇
[Define Logic] via:
   ├── Logic Graph GUI
   └── Inline YAML `on_submit`, `on_change`, etc.
     ⬇
[Preview in Fiddle Mode] ⬄ [Mock data / Simulated events]
     ⬇
[Test with Sample Users / DB Data]
     ⬇
[arcanum validate] ──▶ Fix warnings, check widget use
     ⬇
[arcanum export] or [Publicize GUI Button]
     ⬇
[App Output]
   ├── Web App
   ├── Executable
   └── JSON/CSV Form Data
🧰 Both Flows Share Core Modules:
Module	Purpose
arcanum.yaml	Main app config & toggle flags
Widget Registry	Central place for reusable UI inputs
YAML Schema Engine	Enforces structure & logic compatibility
Local Data Manager	Saves forms, user inputs, and logic results
GUI Editor	Drag-and-drop layouts + instant YAML sync
Exporter	Prepares apps for deployment or data sharing



!! Make sure to use our folder structure and maintain core functionality. Consistently clean up code and follow best practices throughout development. which includes removing old files and folders, updating outdated comments, and ensuring consistent naming conventions. !!

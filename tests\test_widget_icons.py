"""
Test suite for the professional widget icon system.
Tests the WidgetIconProvider and WidgetTemplateProvider functionality.
"""

import pytest
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from arcanum.dev.gui_builder.widget_icons import WidgetIconProvider, WidgetTemplateProvider


class TestWidgetIconProvider:
    """Test the widget icon provider functionality."""
    
    def test_get_widget_icon_known_widgets(self):
        """Test getting icons for known widget types."""
        # Test basic widget types
        assert WidgetIconProvider.get_widget_icon('input') == '📝'
        assert WidgetIconProvider.get_widget_icon('button') == '🔲'
        assert WidgetIconProvider.get_widget_icon('label') == '🏷️'
        assert WidgetIconProvider.get_widget_icon('checkbox') == '☑️'
        assert WidgetIconProvider.get_widget_icon('select') == '📋'
        assert WidgetIconProvider.get_widget_icon('textarea') == '📄'
        assert WidgetIconProvider.get_widget_icon('image') == '🖼️'
        assert WidgetIconProvider.get_widget_icon('container') == '📦'
    
    def test_get_widget_icon_unknown_widget(self):
        """Test getting icon for unknown widget type."""
        icon = WidgetIconProvider.get_widget_icon('unknown_widget')
        assert icon == '❓'  # Should return default icon
    
    def test_get_widget_icon_empty_string(self):
        """Test getting icon for empty string."""
        icon = WidgetIconProvider.get_widget_icon('')
        assert icon == '❓'  # Should return default icon
    
    def test_get_widget_icon_none(self):
        """Test getting icon for None."""
        icon = WidgetIconProvider.get_widget_icon(None)
        assert icon == '❓'  # Should return default icon
    
    def test_get_category_color_known_categories(self):
        """Test getting colors for known categories."""
        assert WidgetIconProvider.get_category_color('input') == '#e3f2fd'
        assert WidgetIconProvider.get_category_color('action') == '#f3e5f5'
        assert WidgetIconProvider.get_category_color('display') == '#e8f5e8'
        assert WidgetIconProvider.get_category_color('layout') == '#fff3e0'
        assert WidgetIconProvider.get_category_color('data') == '#fce4ec'
    
    def test_get_category_color_unknown_category(self):
        """Test getting color for unknown category."""
        color = WidgetIconProvider.get_category_color('unknown_category')
        assert color == '#f5f5f5'  # Should return default color
    
    def test_get_category_color_empty_string(self):
        """Test getting color for empty string."""
        color = WidgetIconProvider.get_category_color('')
        assert color == '#f5f5f5'  # Should return default color
    
    def test_get_category_color_none(self):
        """Test getting color for None."""
        color = WidgetIconProvider.get_category_color(None)
        assert color == '#f5f5f5'  # Should return default color
    
    def test_widget_icons_completeness(self):
        """Test that core widget types have icons."""
        # Test only the widgets that are actually defined in WIDGET_ICONS
        core_widgets = [
            'input', 'button', 'label', 'checkbox', 'select', 'textarea',
            'image', 'container', 'slider', 'progress', 'spinner', 'toggle',
            'radio', 'date', 'time', 'color', 'file', 'number', 'email',
            'password', 'url', 'search', 'tel', 'range', 'month', 'week',
            'datetime', 'hidden', 'submit', 'reset', 'text', 'dropdown',
            'multiselect', 'autocomplete', 'tags', 'rating', 'signature',
            'qr_code', 'barcode', 'map', 'chart', 'table', 'list', 'tree',
            'tabs', 'accordion', 'modal', 'tooltip', 'popover', 'alert',
            'badge', 'avatar', 'card', 'panel', 'sidebar', 'navbar',
            'footer', 'header', 'section', 'article', 'aside', 'main',
            'div', 'span', 'grid', 'flex', 'row', 'column'
        ]

        # Get the actual defined widgets from the provider
        from arcanum.dev.gui_builder.widget_icons import WidgetIconProvider
        defined_widgets = list(WidgetIconProvider.WIDGET_ICONS.keys())

        for widget_type in defined_widgets:
            icon = WidgetIconProvider.get_widget_icon(widget_type)
            assert icon != '❓', f"Widget type '{widget_type}' should have a specific icon"
            assert len(icon) > 0, f"Widget type '{widget_type}' should have a non-empty icon"
    
    def test_category_colors_completeness(self):
        """Test that all expected categories have colors."""
        expected_categories = ['input', 'action', 'display', 'layout', 'data']
        
        for category in expected_categories:
            color = WidgetIconProvider.get_category_color(category)
            assert color != '#f5f5f5', f"Category '{category}' should have a specific color"
            assert color.startswith('#'), f"Category '{category}' color should be a hex color"
            assert len(color) == 7, f"Category '{category}' color should be a valid hex color"


class TestWidgetTemplateProvider:
    """Test the widget template provider functionality."""
    
    def test_get_widget_template_button(self):
        """Test getting template for button widget."""
        template = WidgetTemplateProvider.get_widget_template('button')
        
        assert template['widget'] == 'button'
        assert 'label' in template
        assert 'action' in template
        assert 'style' in template
        assert 'position' in template
        assert template['position']['width'] > 0
        assert template['position']['height'] > 0
    
    def test_get_widget_template_input(self):
        """Test getting template for input widget."""
        template = WidgetTemplateProvider.get_widget_template('input')
        
        assert template['widget'] == 'input'
        assert 'label' in template
        assert 'placeholder' in template
        assert 'required' in template
        assert 'style' in template
        assert 'position' in template
    
    def test_get_widget_template_label(self):
        """Test getting template for label widget."""
        template = WidgetTemplateProvider.get_widget_template('label')
        
        assert template['widget'] == 'label'
        assert 'text' in template
        assert 'style' in template
        assert 'position' in template
    
    def test_get_widget_template_checkbox(self):
        """Test getting template for checkbox widget."""
        template = WidgetTemplateProvider.get_widget_template('checkbox')
        
        assert template['widget'] == 'checkbox'
        assert 'label' in template
        assert 'checked' in template
        assert 'style' in template
        assert 'position' in template
    
    def test_get_widget_template_select(self):
        """Test getting template for select widget."""
        template = WidgetTemplateProvider.get_widget_template('select')
        
        assert template['widget'] == 'select'
        assert 'label' in template
        assert 'options' in template
        assert isinstance(template['options'], list)
        assert len(template['options']) > 0
        assert 'style' in template
        assert 'position' in template
    
    def test_get_widget_template_textarea(self):
        """Test getting template for textarea widget."""
        template = WidgetTemplateProvider.get_widget_template('textarea')
        
        assert template['widget'] == 'textarea'
        assert 'label' in template
        assert 'placeholder' in template
        assert 'rows' in template
        assert 'style' in template
        assert 'position' in template
    
    def test_get_widget_template_image(self):
        """Test getting template for image widget."""
        template = WidgetTemplateProvider.get_widget_template('image')
        
        assert template['widget'] == 'image'
        assert 'src' in template
        assert 'alt' in template
        assert 'style' in template
        assert 'position' in template
    
    def test_get_widget_template_container(self):
        """Test getting template for container widget."""
        template = WidgetTemplateProvider.get_widget_template('container')
        
        assert template['widget'] == 'container'
        assert 'children' in template
        assert isinstance(template['children'], list)
        assert 'style' in template
        assert 'position' in template
    
    def test_get_widget_template_unknown_widget(self):
        """Test getting template for unknown widget type."""
        template = WidgetTemplateProvider.get_widget_template('unknown_widget')

        # Should return a basic template
        assert template['widget'] == 'unknown_widget'
        assert 'position' in template
        assert template['position']['width'] == 150
        assert template['position']['height'] == 35
    
    def test_get_widget_template_empty_string(self):
        """Test getting template for empty string."""
        template = WidgetTemplateProvider.get_widget_template('')
        
        # Should return a basic template
        assert template['widget'] == ''
        assert 'position' in template
    
    def test_get_widget_template_none(self):
        """Test getting template for None."""
        template = WidgetTemplateProvider.get_widget_template(None)

        # Should return a basic template
        assert template['widget'] == 'unknown'
        assert 'position' in template
    
    def test_template_position_consistency(self):
        """Test that all templates have consistent position structure."""
        widget_types = ['button', 'input', 'label', 'checkbox', 'select', 'textarea', 'image', 'container']
        
        for widget_type in widget_types:
            template = WidgetTemplateProvider.get_widget_template(widget_type)
            
            assert 'position' in template, f"Template for '{widget_type}' missing position"
            position = template['position']
            
            assert 'width' in position, f"Template for '{widget_type}' missing position.width"
            assert 'height' in position, f"Template for '{widget_type}' missing position.height"
            assert isinstance(position['width'], int), f"Template for '{widget_type}' width should be int"
            assert isinstance(position['height'], int), f"Template for '{widget_type}' height should be int"
            assert position['width'] > 0, f"Template for '{widget_type}' width should be positive"
            assert position['height'] > 0, f"Template for '{widget_type}' height should be positive"
    
    def test_template_style_consistency(self):
        """Test that all templates have style information."""
        widget_types = ['button', 'input', 'label', 'checkbox', 'select', 'textarea', 'image', 'container']
        
        for widget_type in widget_types:
            template = WidgetTemplateProvider.get_widget_template(widget_type)
            
            assert 'style' in template, f"Template for '{widget_type}' missing style"
            assert isinstance(template['style'], str), f"Template for '{widget_type}' style should be string"
            assert len(template['style']) > 0, f"Template for '{widget_type}' style should not be empty"


class TestIntegration:
    """Integration tests for icon and template providers."""
    
    def test_icon_template_consistency(self):
        """Test that widgets with templates also have icons."""
        widget_types = ['button', 'input', 'label', 'checkbox', 'select', 'textarea', 'image', 'container']
        
        for widget_type in widget_types:
            # Should have both icon and template
            icon = WidgetIconProvider.get_widget_icon(widget_type)
            template = WidgetTemplateProvider.get_widget_template(widget_type)
            
            assert icon != '❓', f"Widget '{widget_type}' should have a specific icon"
            assert template['widget'] == widget_type, f"Template for '{widget_type}' should match widget type"
    
    def test_all_icons_are_unicode(self):
        """Test that all icons are valid Unicode characters."""
        # Test a sample of widget types
        widget_types = ['button', 'input', 'label', 'checkbox', 'select', 'textarea', 'image', 'container']
        
        for widget_type in widget_types:
            icon = WidgetIconProvider.get_widget_icon(widget_type)
            
            # Should be a string
            assert isinstance(icon, str), f"Icon for '{widget_type}' should be a string"
            
            # Should be a single character (emoji)
            assert len(icon) >= 1, f"Icon for '{widget_type}' should not be empty"
            
            # Should be encodable as UTF-8
            try:
                icon.encode('utf-8')
            except UnicodeEncodeError:
                pytest.fail(f"Icon for '{widget_type}' is not valid UTF-8: {icon}")


if __name__ == '__main__':
    pytest.main([__file__, '-v'])

"""
Query builder and result handling for database operations.
"""

from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json
from datetime import datetime


class JoinType(Enum):
    """SQL join types."""
    INNER = "INNER JOIN"
    LEFT = "LEFT JOIN"
    RIGHT = "RIGHT JOIN"
    FULL = "FULL OUTER JOIN"


class OrderDirection(Enum):
    """SQL order directions."""
    ASC = "ASC"
    DESC = "DESC"


@dataclass
class QueryResult:
    """Represents the result of a database query."""
    success: bool
    data: List[Dict[str, Any]] = field(default_factory=list)
    row_count: int = 0
    affected_rows: int = 0
    last_insert_id: Optional[int] = None
    execution_time: float = 0.0
    error: Optional[str] = None
    query: str = ""
    params: Tuple = field(default_factory=tuple)
    
    def first(self) -> Optional[Dict[str, Any]]:
        """Get the first row of results."""
        return self.data[0] if self.data else None
        
    def last(self) -> Optional[Dict[str, Any]]:
        """Get the last row of results."""
        return self.data[-1] if self.data else None
        
    def to_dict_list(self) -> List[Dict[str, Any]]:
        """Get results as list of dictionaries."""
        return self.data
        
    def to_json(self) -> str:
        """Convert results to JSON string."""
        return json.dumps(self.data, default=str, indent=2)


class QueryBuilder:
    """SQL query builder with fluent interface."""
    
    def __init__(self, table_name: str = None):
        self.reset()
        if table_name:
            self._table = table_name
            
    def reset(self):
        """Reset the query builder to initial state."""
        self._query_type = None
        self._table = None
        self._select_fields = []
        self._where_conditions = []
        self._join_clauses = []
        self._group_by_fields = []
        self._having_conditions = []
        self._order_by_fields = []
        self._limit_value = None
        self._offset_value = None
        self._insert_data = {}
        self._update_data = {}
        self._params = []
        return self
        
    def table(self, table_name: str):
        """Set the table name."""
        self._table = table_name
        return self
        
    def select(self, *fields):
        """Add SELECT fields."""
        self._query_type = "SELECT"
        if fields:
            self._select_fields.extend(fields)
        else:
            self._select_fields = ["*"]
        return self
        
    def insert(self, data: Dict[str, Any]):
        """Set up INSERT query."""
        self._query_type = "INSERT"
        self._insert_data = data
        return self
        
    def update(self, data: Dict[str, Any]):
        """Set up UPDATE query."""
        self._query_type = "UPDATE"
        self._update_data = data
        return self
        
    def delete(self):
        """Set up DELETE query."""
        self._query_type = "DELETE"
        return self
        
    def where(self, field: str, operator: str = "=", value: Any = None):
        """Add WHERE condition."""
        if value is None and operator == "=":
            # Handle case where operator is omitted: where("field", "value")
            value = operator
            operator = "="
            
        self._where_conditions.append({
            "field": field,
            "operator": operator,
            "value": value,
            "connector": "AND"
        })
        return self
        
    def where_or(self, field: str, operator: str = "=", value: Any = None):
        """Add WHERE condition with OR connector."""
        if value is None and operator == "=":
            value = operator
            operator = "="
            
        self._where_conditions.append({
            "field": field,
            "operator": operator,
            "value": value,
            "connector": "OR"
        })
        return self
        
    def where_in(self, field: str, values: List[Any]):
        """Add WHERE IN condition."""
        self._where_conditions.append({
            "field": field,
            "operator": "IN",
            "value": values,
            "connector": "AND"
        })
        return self
        
    def where_between(self, field: str, start: Any, end: Any):
        """Add WHERE BETWEEN condition."""
        self._where_conditions.append({
            "field": field,
            "operator": "BETWEEN",
            "value": (start, end),
            "connector": "AND"
        })
        return self
        
    def where_like(self, field: str, pattern: str):
        """Add WHERE LIKE condition."""
        self._where_conditions.append({
            "field": field,
            "operator": "LIKE",
            "value": pattern,
            "connector": "AND"
        })
        return self
        
    def where_null(self, field: str):
        """Add WHERE IS NULL condition."""
        self._where_conditions.append({
            "field": field,
            "operator": "IS NULL",
            "value": None,
            "connector": "AND"
        })
        return self
        
    def where_not_null(self, field: str):
        """Add WHERE IS NOT NULL condition."""
        self._where_conditions.append({
            "field": field,
            "operator": "IS NOT NULL",
            "value": None,
            "connector": "AND"
        })
        return self
        
    def join(self, table: str, on_condition: str, join_type: JoinType = JoinType.INNER):
        """Add JOIN clause."""
        self._join_clauses.append({
            "table": table,
            "condition": on_condition,
            "type": join_type
        })
        return self
        
    def left_join(self, table: str, on_condition: str):
        """Add LEFT JOIN clause."""
        return self.join(table, on_condition, JoinType.LEFT)
        
    def right_join(self, table: str, on_condition: str):
        """Add RIGHT JOIN clause."""
        return self.join(table, on_condition, JoinType.RIGHT)
        
    def group_by(self, *fields):
        """Add GROUP BY fields."""
        self._group_by_fields.extend(fields)
        return self
        
    def having(self, condition: str):
        """Add HAVING condition."""
        self._having_conditions.append(condition)
        return self
        
    def order_by(self, field: str, direction: OrderDirection = OrderDirection.ASC):
        """Add ORDER BY field."""
        self._order_by_fields.append({
            "field": field,
            "direction": direction
        })
        return self
        
    def order_by_desc(self, field: str):
        """Add ORDER BY field DESC."""
        return self.order_by(field, OrderDirection.DESC)
        
    def limit(self, count: int):
        """Set LIMIT."""
        self._limit_value = count
        return self
        
    def offset(self, count: int):
        """Set OFFSET."""
        self._offset_value = count
        return self
        
    def paginate(self, page: int, per_page: int = 20):
        """Set pagination (LIMIT and OFFSET)."""
        self._limit_value = per_page
        self._offset_value = (page - 1) * per_page
        return self
        
    def build(self) -> Tuple[str, Tuple]:
        """Build the SQL query and parameters."""
        if not self._query_type:
            raise ValueError("Query type not specified")
            
        if not self._table:
            raise ValueError("Table name not specified")
            
        self._params = []
        
        if self._query_type == "SELECT":
            return self._build_select()
        elif self._query_type == "INSERT":
            return self._build_insert()
        elif self._query_type == "UPDATE":
            return self._build_update()
        elif self._query_type == "DELETE":
            return self._build_delete()
        else:
            raise ValueError(f"Unsupported query type: {self._query_type}")
            
    def _build_select(self) -> Tuple[str, Tuple]:
        """Build SELECT query."""
        # SELECT clause
        fields = ", ".join(self._select_fields) if self._select_fields else "*"
        query = f"SELECT {fields} FROM {self._table}"
        
        # JOIN clauses
        for join in self._join_clauses:
            query += f" {join['type'].value} {join['table']} ON {join['condition']}"
            
        # WHERE clause
        where_sql = self._build_where_clause()
        if where_sql:
            query += f" WHERE {where_sql}"
            
        # GROUP BY clause
        if self._group_by_fields:
            query += f" GROUP BY {', '.join(self._group_by_fields)}"
            
        # HAVING clause
        if self._having_conditions:
            query += f" HAVING {' AND '.join(self._having_conditions)}"
            
        # ORDER BY clause
        if self._order_by_fields:
            order_parts = []
            for order in self._order_by_fields:
                order_parts.append(f"{order['field']} {order['direction'].value}")
            query += f" ORDER BY {', '.join(order_parts)}"
            
        # LIMIT and OFFSET
        if self._limit_value is not None:
            query += f" LIMIT {self._limit_value}"
            
        if self._offset_value is not None:
            query += f" OFFSET {self._offset_value}"
            
        return query, tuple(self._params)
        
    def _build_insert(self) -> Tuple[str, Tuple]:
        """Build INSERT query."""
        if not self._insert_data:
            raise ValueError("No data provided for INSERT")
            
        columns = list(self._insert_data.keys())
        placeholders = ["?" for _ in columns]
        values = list(self._insert_data.values())
        
        # Process values
        processed_values = []
        for value in values:
            if isinstance(value, (dict, list)):
                processed_values.append(json.dumps(value))
            elif isinstance(value, datetime):
                processed_values.append(value.isoformat())
            else:
                processed_values.append(value)
                
        query = f"INSERT INTO {self._table} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
        return query, tuple(processed_values)
        
    def _build_update(self) -> Tuple[str, Tuple]:
        """Build UPDATE query."""
        if not self._update_data:
            raise ValueError("No data provided for UPDATE")
            
        # SET clause
        set_clauses = []
        for key, value in self._update_data.items():
            set_clauses.append(f"{key} = ?")
            if isinstance(value, (dict, list)):
                self._params.append(json.dumps(value))
            elif isinstance(value, datetime):
                self._params.append(value.isoformat())
            else:
                self._params.append(value)
                
        query = f"UPDATE {self._table} SET {', '.join(set_clauses)}"
        
        # WHERE clause
        where_sql = self._build_where_clause()
        if where_sql:
            query += f" WHERE {where_sql}"
        else:
            raise ValueError("UPDATE query requires WHERE clause for safety")
            
        return query, tuple(self._params)
        
    def _build_delete(self) -> Tuple[str, Tuple]:
        """Build DELETE query."""
        query = f"DELETE FROM {self._table}"
        
        # WHERE clause
        where_sql = self._build_where_clause()
        if where_sql:
            query += f" WHERE {where_sql}"
        else:
            raise ValueError("DELETE query requires WHERE clause for safety")
            
        return query, tuple(self._params)
        
    def _build_where_clause(self) -> str:
        """Build WHERE clause."""
        if not self._where_conditions:
            return ""
            
        where_parts = []
        for i, condition in enumerate(self._where_conditions):
            connector = "" if i == 0 else f" {condition['connector']} "
            
            field = condition["field"]
            operator = condition["operator"]
            value = condition["value"]
            
            if operator == "IS NULL" or operator == "IS NOT NULL":
                where_parts.append(f"{connector}{field} {operator}")
            elif operator == "IN":
                if isinstance(value, (list, tuple)):
                    placeholders = ", ".join(["?" for _ in value])
                    where_parts.append(f"{connector}{field} IN ({placeholders})")
                    self._params.extend(value)
                else:
                    raise ValueError("IN operator requires list or tuple value")
            elif operator == "BETWEEN":
                if isinstance(value, (list, tuple)) and len(value) == 2:
                    where_parts.append(f"{connector}{field} BETWEEN ? AND ?")
                    self._params.extend(value)
                else:
                    raise ValueError("BETWEEN operator requires tuple of two values")
            else:
                where_parts.append(f"{connector}{field} {operator} ?")
                self._params.append(value)
                
        return "".join(where_parts)
        
    def to_sql(self) -> str:
        """Get the SQL query as string (for debugging)."""
        query, params = self.build()
        return f"Query: {query}\nParams: {params}"

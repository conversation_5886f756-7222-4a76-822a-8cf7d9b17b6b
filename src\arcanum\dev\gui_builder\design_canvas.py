"""
Design Canvas for the Arcanum GUI Builder - Visual layout editor.
"""

import yaml
from typing import Dict, List, Optional, Any
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QScrollArea,
    QLabel, QPushButton, QMenu, QAction, QMessageBox, QApplication,
    QLineEdit
)
from PyQt5.QtCore import Qt, pyqtSignal, QPoint, QRect, QSize
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor, QFont, QCursor

from ...core.layout import LayoutEngine
from ...core.widget_utils import (
    ensure_widget_position, snap_to_grid, find_free_position, get_widget_bounds
)


class GridCanvasWidget(QWidget):
    """Canvas widget with grid painting and alignment guides."""

    def __init__(self, design_canvas, parent=None):
        super().__init__(parent)
        self.design_canvas = design_canvas
        self.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 1px solid #dee2e6;
            }
        """)

    def paintEvent(self, event):
        """Paint the grid and alignment guides."""
        super().paintEvent(event)

        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Draw grid
        if self.design_canvas.show_grid:
            self.draw_grid(painter)

        # Draw alignment guides
        if self.design_canvas.show_alignment_guides:
            self.draw_alignment_guides(painter)

    def draw_grid(self, painter: QPainter):
        """Draw the grid."""
        grid_size = self.design_canvas.grid_size

        # Set grid pen
        pen = QPen(QColor(220, 220, 220))
        pen.setWidth(1)
        painter.setPen(pen)

        # Draw vertical lines
        for x in range(0, self.width(), grid_size):
            painter.drawLine(x, 0, x, self.height())

        # Draw horizontal lines
        for y in range(0, self.height(), grid_size):
            painter.drawLine(0, y, self.width(), y)

    def draw_alignment_guides(self, painter: QPainter):
        """Draw alignment guides."""
        # Set guide pen
        pen = QPen(QColor(0, 150, 255))
        pen.setWidth(1)
        pen.setStyle(Qt.DashLine)
        painter.setPen(pen)

        # Draw guides (placeholder - would be calculated based on selected items)
        for guide in self.design_canvas.alignment_guides:
            if guide['type'] == 'vertical':
                painter.drawLine(guide['position'], 0, guide['position'], self.height())
            elif guide['type'] == 'horizontal':
                painter.drawLine(0, guide['position'], self.width(), guide['position'])


class DesignItem(QWidget):
    """
    Represents a widget or layout item on the design canvas with resize handles.
    """

    # Signals
    selected = pyqtSignal(object)
    moved = pyqtSignal(object, QPoint)
    resized = pyqtSignal(object, QSize)
    context_menu_requested = pyqtSignal(object, QPoint)

    def __init__(self, item_type: str, item_data: Dict[str, Any], parent=None):
        super().__init__(parent)
        self.item_type = item_type  # 'widget' or 'layout'
        self.item_data = item_data
        self.is_selected = False
        self.is_dragging = False
        self.is_resizing = False
        self.resize_handle = None  # Which handle is being dragged
        self.drag_start_pos = QPoint()
        self.resize_start_size = QSize()
        self.resize_start_pos = QPoint()

        # Resize handle size
        self.handle_size = 8

        # Ensure widget has position data
        self.item_data = ensure_widget_position(self.item_data)

        self.setup_ui()
        
    def setup_ui(self):
        """Set up the design item UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(4, 4, 4, 4)

        if self.item_type == 'widget':
            # Create actual widget representation
            self._create_widget_representation(layout)
        else:
            # Create layout representation
            self._create_layout_representation(layout)

        # Set initial size and position from widget data
        self.apply_position_data()

        # Enable mouse tracking for resize handles
        self.setMouseTracking(True)

        self.update_style()

    def apply_position_data(self):
        """Apply position and size from widget data."""
        position = self.item_data.get('position', {})
        x = position.get('x', 50)
        y = position.get('y', 50)
        width = position.get('width', 200)
        height = position.get('height', 35)

        self.move(x, y)
        self.resize(width, height)

    def update_position_data(self):
        """Update widget data with current position and size."""
        if 'position' not in self.item_data:
            self.item_data['position'] = {}

        position = self.item_data['position']
        position['x'] = self.x()
        position['y'] = self.y()
        position['width'] = self.width()
        position['height'] = self.height()

    def _create_widget_representation(self, layout):
        """Create a visual representation of the widget."""
        widget_type = self.item_data.get('widget', 'unknown')

        if widget_type == 'label':
            # Create actual label widget
            text = self.item_data.get('text', self.item_data.get('label', 'Label'))
            self.widget = QLabel(text)
            self.widget.setWordWrap(True)
            self.widget.setAlignment(Qt.AlignLeft | Qt.AlignTop)

            # Apply styling if provided
            style = self.item_data.get('style', '')
            if style:
                self.widget.setStyleSheet(f"QLabel {{ {style} }}")
            else:
                self.widget.setStyleSheet("QLabel { padding: 4px; }")

        elif widget_type == 'button':
            # Create actual button widget
            button_text = self.item_data.get('label', self.item_data.get('text', 'Button'))
            self.widget = QPushButton(button_text)

            # Apply styling if provided
            style = self.item_data.get('style', '')
            if style:
                self.widget.setStyleSheet(f"QPushButton {{ {style} }}")
            else:
                self.widget.setStyleSheet("""
                    QPushButton {
                        background-color: #007bff;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #0056b3;
                    }
                """)

        elif widget_type == 'input' or widget_type == 'text_input':
            # Create interactive input field for live preview testing
            placeholder = self.item_data.get('placeholder', 'Enter text...')
            self.widget = QLineEdit()
            self.widget.setPlaceholderText(placeholder)

            # Apply custom styling if provided
            style = self.item_data.get('style', '')
            if style:
                self.widget.setStyleSheet(f"QLineEdit {{ padding: 8px; border: 1px solid #ccc; border-radius: 4px; {style} }}")
            else:
                self.widget.setStyleSheet("QLineEdit { padding: 8px; border: 1px solid #ccc; border-radius: 4px; }")

            # Make it interactive for testing (data not saved)
            self.widget.setEnabled(True)
            self.widget.setReadOnly(False)

        elif widget_type == 'image':
            # Create image placeholder
            image_text = f"🖼️ Image: {self.item_data.get('src', 'image.png')}"
            self.widget = QLabel(image_text)
            self.widget.setAlignment(Qt.AlignCenter)
            self.widget.setStyleSheet("""
                QLabel {
                    border: 2px dashed #007bff;
                    background-color: #f8f9fa;
                    padding: 20px;
                    border-radius: 4px;
                    color: #007bff;
                }
            """)

        elif widget_type == 'text':
            # Create text widget (similar to label but with different styling)
            text = self.item_data.get('content', self.item_data.get('text', 'Text content'))
            self.widget = QLabel(text)
            self.widget.setWordWrap(True)
            self.widget.setAlignment(Qt.AlignLeft | Qt.AlignTop)
            self.widget.setStyleSheet("QLabel { padding: 8px; background-color: #f8f9fa; border-radius: 4px; }")

        else:
            # Unknown widget type - show placeholder
            self.widget = QLabel(f"Widget: {widget_type}")
            self.widget.setAlignment(Qt.AlignCenter)
            self.widget.setStyleSheet("QLabel { color: #666; font-style: italic; }")

        layout.addWidget(self.widget)

        # Set minimum size based on widget type
        if widget_type == 'button':
            self.setMinimumSize(120, 40)
        elif widget_type == 'input' or widget_type == 'text_input':
            self.setMinimumSize(200, 35)
        else:
            self.setMinimumSize(100, 30)

    def _create_layout_representation(self, layout):
        """Create a visual representation of the layout."""
        layout_type = self.item_data.get('type', 'unknown')

        # Create a styled container to represent the layout
        self.widget = QLabel(f"📋 {layout_type.title()} Layout")
        self.widget.setAlignment(Qt.AlignCenter)
        self.widget.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 2px dashed #6c757d;
                border-radius: 6px;
                padding: 12px;
                color: #495057;
                font-weight: bold;
            }
        """)

        layout.addWidget(self.widget)
        self.setMinimumSize(150, 80)
        
    def update_style(self):
        """Update the visual style based on selection state."""
        if self.is_selected:
            border_color = "#007bff"
            background_color = "#e3f2fd"
        else:
            border_color = "#ccc"
            background_color = "#f8f9fa"
            
        self.setStyleSheet(f"""
            DesignItem {{
                border: 2px solid {border_color};
                border-radius: 4px;
                background-color: {background_color};
            }}
            QLabel {{
                border: none;
                background: transparent;
                font-size: 10px;
            }}
        """)
        
    def set_selected(self, selected: bool):
        """Set the selection state."""
        self.is_selected = selected
        self.update_style()
        
    def get_resize_handle(self, pos: QPoint) -> Optional[str]:
        """Get which resize handle is at the given position."""
        if not self.is_selected:
            return None

        rect = self.rect()
        handle_size = self.handle_size

        # Check corner handles
        if (pos.x() <= handle_size and pos.y() <= handle_size):
            return "top_left"
        elif (pos.x() >= rect.width() - handle_size and pos.y() <= handle_size):
            return "top_right"
        elif (pos.x() <= handle_size and pos.y() >= rect.height() - handle_size):
            return "bottom_left"
        elif (pos.x() >= rect.width() - handle_size and pos.y() >= rect.height() - handle_size):
            return "bottom_right"

        # Check edge handles
        elif (pos.y() <= handle_size):
            return "top"
        elif (pos.y() >= rect.height() - handle_size):
            return "bottom"
        elif (pos.x() <= handle_size):
            return "left"
        elif (pos.x() >= rect.width() - handle_size):
            return "right"

        return None

    def update_cursor(self, pos: QPoint):
        """Update cursor based on position."""
        handle = self.get_resize_handle(pos)

        if handle:
            cursor_map = {
                "top_left": Qt.SizeFDiagCursor,
                "top_right": Qt.SizeBDiagCursor,
                "bottom_left": Qt.SizeBDiagCursor,
                "bottom_right": Qt.SizeFDiagCursor,
                "top": Qt.SizeVerCursor,
                "bottom": Qt.SizeVerCursor,
                "left": Qt.SizeHorCursor,
                "right": Qt.SizeHorCursor,
            }
            self.setCursor(cursor_map.get(handle, Qt.ArrowCursor))
        else:
            self.setCursor(Qt.ArrowCursor)

    def mousePressEvent(self, event):
        """Handle mouse press events."""
        if event.button() == Qt.LeftButton:
            self.selected.emit(self)

            # Check if clicking on resize handle
            handle = self.get_resize_handle(event.pos())
            if handle and self.is_selected:
                self.is_resizing = True
                self.resize_handle = handle
                self.resize_start_size = self.size()
                self.resize_start_pos = self.pos()
                self.drag_start_pos = event.globalPos()
            else:
                self.is_dragging = False
                self.drag_start_pos = event.pos()

        elif event.button() == Qt.RightButton:
            # Emit context menu signal
            self.context_menu_requested.emit(self, event.globalPos())

        super().mousePressEvent(event)
        
    def mouseMoveEvent(self, event):
        """Handle mouse move events for dragging and resizing."""
        # Update cursor when not dragging/resizing
        if not (event.buttons() & Qt.LeftButton):
            self.update_cursor(event.pos())
            return

        if self.is_resizing:
            # Handle resizing
            delta = event.globalPos() - self.drag_start_pos
            self.handle_resize(delta)

        elif not self.is_dragging:
            # Check if we should start dragging
            if ((event.pos() - self.drag_start_pos).manhattanLength() >=
                QApplication.startDragDistance()):
                self.is_dragging = True

        if self.is_dragging and not self.is_resizing:
            # Move the item
            new_pos = self.mapToParent(event.pos() - self.drag_start_pos)

            # Apply grid snapping if parent is DesignCanvas
            parent_widget = self.parent()
            while parent_widget and not isinstance(parent_widget, DesignCanvas):
                parent_widget = parent_widget.parent()

            if parent_widget and hasattr(parent_widget, 'snap_to_grid'):
                new_pos = parent_widget.snap_to_grid(new_pos)

            self.move(new_pos)
            self.update_position_data()
            self.moved.emit(self, new_pos)

    def handle_resize(self, delta: QPoint):
        """Handle widget resizing based on drag delta."""
        if not self.resize_handle:
            return

        new_size = QSize(self.resize_start_size)
        new_pos = QPoint(self.resize_start_pos)

        # Apply resize based on handle
        if "right" in self.resize_handle:
            new_size.setWidth(max(50, self.resize_start_size.width() + delta.x()))
        elif "left" in self.resize_handle:
            new_width = max(50, self.resize_start_size.width() - delta.x())
            new_pos.setX(self.resize_start_pos.x() + (self.resize_start_size.width() - new_width))
            new_size.setWidth(new_width)

        if "bottom" in self.resize_handle:
            new_size.setHeight(max(25, self.resize_start_size.height() + delta.y()))
        elif "top" in self.resize_handle:
            new_height = max(25, self.resize_start_size.height() - delta.y())
            new_pos.setY(self.resize_start_pos.y() + (self.resize_start_size.height() - new_height))
            new_size.setHeight(new_height)

        # Apply grid snapping
        parent_widget = self.parent()
        while parent_widget and not isinstance(parent_widget, DesignCanvas):
            parent_widget = parent_widget.parent()

        if parent_widget and hasattr(parent_widget, 'snap_to_grid'):
            new_pos = parent_widget.snap_to_grid(new_pos)
            # Snap size to grid as well
            grid_size = getattr(parent_widget, 'grid_size', 10)
            new_size.setWidth(round(new_size.width() / grid_size) * grid_size)
            new_size.setHeight(round(new_size.height() / grid_size) * grid_size)

        self.move(new_pos)
        self.resize(new_size)
        self.update_position_data()
        self.resized.emit(self, new_size)
            
    def mouseReleaseEvent(self, event):
        """Handle mouse release events."""
        if event.button() == Qt.LeftButton:
            self.is_dragging = False
            self.is_resizing = False
            self.resize_handle = None

        super().mouseReleaseEvent(event)

    def mouseDoubleClickEvent(self, event):
        """Handle double-click to reset size."""
        if event.button() == Qt.LeftButton and self.is_selected:
            # Reset to default size
            widget_type = self.item_data.get('widget', 'text')
            from ...core.widget_utils import get_default_width, get_default_height
            default_width = get_default_width(widget_type)
            default_height = get_default_height(widget_type)

            self.resize(default_width, default_height)
            self.update_position_data()
            self.resized.emit(self, self.size())

        super().mouseDoubleClickEvent(event)

    def paintEvent(self, event):
        """Paint the widget with resize handles if selected."""
        super().paintEvent(event)

        if self.is_selected:
            painter = QPainter(self)
            painter.setRenderHint(QPainter.Antialiasing)

            # Draw resize handles
            handle_color = QColor("#007bff")
            painter.setBrush(QBrush(handle_color))
            painter.setPen(QPen(handle_color, 1))

            rect = self.rect()
            handle_size = self.handle_size

            # Corner handles
            handles = [
                QRect(0, 0, handle_size, handle_size),  # top-left
                QRect(rect.width() - handle_size, 0, handle_size, handle_size),  # top-right
                QRect(0, rect.height() - handle_size, handle_size, handle_size),  # bottom-left
                QRect(rect.width() - handle_size, rect.height() - handle_size, handle_size, handle_size),  # bottom-right
            ]

            # Edge handles
            handles.extend([
                QRect(rect.width() // 2 - handle_size // 2, 0, handle_size, handle_size),  # top
                QRect(rect.width() // 2 - handle_size // 2, rect.height() - handle_size, handle_size, handle_size),  # bottom
                QRect(0, rect.height() // 2 - handle_size // 2, handle_size, handle_size),  # left
                QRect(rect.width() - handle_size, rect.height() // 2 - handle_size // 2, handle_size, handle_size),  # right
            ])

            for handle in handles:
                painter.drawRect(handle)
        
    def contextMenuEvent(self, event):
        """Handle context menu events."""
        menu = QMenu(self)
        
        delete_action = QAction("Delete", self)
        delete_action.triggered.connect(self.delete_item)
        menu.addAction(delete_action)
        
        copy_action = QAction("Copy", self)
        copy_action.triggered.connect(self.copy_item)
        menu.addAction(copy_action)
        
        menu.exec_(event.globalPos())
        
    def delete_item(self):
        """Delete this item."""
        # TODO: Implement deletion
        pass
        
    def copy_item(self):
        """Copy this item."""
        # TODO: Implement copying
        pass

    def update_widget(self):
        """Update the widget display after data changes."""
        # Recreate the widget representation
        if hasattr(self, 'widget') and self.widget:
            self.widget.setParent(None)
            self.widget.deleteLater()

        # Clear layout
        layout = self.layout()
        if layout:
            while layout.count():
                child = layout.takeAt(0)
                if child.widget():
                    child.widget().setParent(None)

        # Recreate widget
        if self.item_type == 'widget':
            self._create_widget_representation(layout)
        else:
            self._create_layout_representation(layout)

        # Update styling
        self.update_style()


class DesignCanvas(QScrollArea):
    """
    Visual design canvas for building layouts with grid snapping and alignment guides.
    """

    # Signals
    selection_changed = pyqtSignal(object)
    layout_changed = pyqtSignal(dict)

    def __init__(self, layout_engine: LayoutEngine, parent=None):
        super().__init__(parent)
        self.layout_engine = layout_engine
        self.current_layout: Optional[Dict[str, Any]] = None
        self.design_items: List[DesignItem] = []
        self.selected_item: Optional[DesignItem] = None
        self.selected_items: List[DesignItem] = []  # Multi-select support

        # Grid and snapping settings
        self.grid_size = 10
        self.snap_enabled = True
        self.show_grid = True
        self.show_alignment_guides = True

        # Alignment guides
        self.alignment_guides = []

        # Interaction mode
        self.interaction_mode = 'select'  # 'select', 'pan', 'zoom'

        self.setup_ui()
        
    def setup_ui(self):
        """Set up the canvas UI."""
        # Create enhanced canvas widget with grid
        self.canvas_widget = GridCanvasWidget(self)
        self.canvas_widget.setMinimumSize(800, 600)

        # Enable dynamic sizing
        self.auto_resize_enabled = True

        # Enable drag and drop
        self.canvas_widget.setAcceptDrops(True)
        self.canvas_widget.dragEnterEvent = self.dragEnterEvent
        self.canvas_widget.dragMoveEvent = self.dragMoveEvent
        self.canvas_widget.dropEvent = self.dropEvent

        self.setWidget(self.canvas_widget)
        self.setWidgetResizable(True)

        # Create default layout
        self.create_default_layout()

    def snap_to_grid(self, point: QPoint) -> QPoint:
        """Snap a point to the grid."""
        if not self.snap_enabled:
            return point

        x = round(point.x() / self.grid_size) * self.grid_size
        y = round(point.y() / self.grid_size) * self.grid_size
        return QPoint(x, y)

    def toggle_grid(self):
        """Toggle grid visibility."""
        self.show_grid = not self.show_grid
        self.canvas_widget.update()

    def toggle_snap_to_grid(self):
        """Toggle grid snapping."""
        self.snap_enabled = not self.snap_enabled

    def set_grid_size(self, size: int):
        """Set the grid size."""
        self.grid_size = max(5, min(50, size))
        self.canvas_widget.update()
        
    def create_default_layout(self):
        """Create a default empty canvas layout."""
        self.current_layout = {
            "id": "main_page",
            "title": "New Page",
            "layout": {
                "type": "canvas",
                "snap_to_grid": True,
                "grid_size": 10,
                "canvas_width": 800,
                "canvas_height": 600,
                "auto_resize": True,
                "content": []
            }
        }
        self.refresh_canvas()
        
    def dragEnterEvent(self, event):
        """Handle drag enter events."""
        if event.mimeData().hasFormat("application/x-arcanum-widget"):
            event.acceptProposedAction()
        else:
            event.ignore()
            
    def dragMoveEvent(self, event):
        """Handle drag move events."""
        if event.mimeData().hasFormat("application/x-arcanum-widget"):
            event.acceptProposedAction()
        else:
            event.ignore()
            
    def dropEvent(self, event):
        """Handle drop events."""
        if event.mimeData().hasFormat("application/x-arcanum-widget"):
            widget_type = event.mimeData().data("application/x-arcanum-widget").data().decode()
            drop_pos = event.pos()
            
            self.add_widget_at_position(widget_type, drop_pos)
            event.acceptProposedAction()
        else:
            event.ignore()
            
    def add_widget(self, widget_type: str):
        """Add a widget to the canvas."""
        # Add to center of canvas
        canvas_center = QPoint(
            self.canvas_widget.width() // 2,
            self.canvas_widget.height() // 2
        )
        self.add_widget_at_position(widget_type, canvas_center)
        
    def add_widget_at_position(self, widget_type: str, position: QPoint):
        """Add a widget at a specific position."""
        try:
            # Create widget data with position (no auto-ID generation)
            widget_data = {
                "widget": widget_type,
                "label": f"New {widget_type.title()}"
            }

            # Ensure position data and find free position if needed
            existing_widgets = [item.item_data for item in self.design_items]
            free_x, free_y = find_free_position(existing_widgets,
                                              start_x=position.x(),
                                              start_y=position.y(),
                                              grid_size=self.grid_size)

            widget_data = ensure_widget_position(widget_data, free_x, free_y)
            
            # Add default properties based on widget type
            if widget_type == "text" or widget_type == "text_input":
                widget_data["placeholder"] = "Enter text..."
                widget_data["label"] = "Text Input"  # User-facing label
            elif widget_type == "button":
                widget_data["variant"] = "primary"
                widget_data["label"] = "Click Me"  # Button text
            elif widget_type == "label":
                widget_data["text"] = "Label Text"  # Content to display
                widget_data["label"] = "Text Label"  # User-facing label for editor
            elif widget_type == "image":
                widget_data["src"] = "placeholder.png"
                widget_data["alt"] = "Image"
                widget_data["label"] = "Image Widget"  # User-facing label
            elif widget_type == "checkbox":
                widget_data["label"] = "Check me"  # Checkbox label
            elif widget_type == "dropdown":
                widget_data["label"] = "Select option"  # Dropdown label
                widget_data["options"] = ["Option 1", "Option 2", "Option 3"]
                
            # Create design item
            design_item = DesignItem("widget", widget_data, self.canvas_widget)
            design_item.show()

            # Connect signals
            design_item.selected.connect(self.select_item)
            design_item.moved.connect(self.item_moved)
            design_item.resized.connect(self.item_resized)
            design_item.context_menu_requested.connect(self.show_context_menu)

            self.design_items.append(design_item)
            
            # Update layout data
            if self.current_layout:
                self.current_layout["layout"]["content"].append(widget_data)
                self.layout_changed.emit(self.current_layout)
                
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to add widget: {e}")
            
    def select_item(self, item: DesignItem):
        """Select a design item."""
        # Deselect previous item
        if self.selected_item:
            self.selected_item.set_selected(False)

        # Select new item
        self.selected_item = item
        item.set_selected(True)

        # Emit selection changed with a copy to ensure responsiveness
        self.selection_changed.emit(item.item_data.copy())
        
    def item_moved(self, item: DesignItem, position: QPoint):
        """Handle item movement."""
        # Update layout data with new position
        item.update_position_data()
        self.layout_changed.emit(self.current_layout)

        # Update canvas size when items are moved
        self.update_canvas_size()

    def item_resized(self, item: DesignItem, size: QSize):
        """Handle item resizing."""
        # Update layout data with new size
        item.update_position_data()
        self.layout_changed.emit(self.current_layout)

        # Update canvas size when items are resized
        self.update_canvas_size()

    def show_context_menu(self, item: DesignItem, global_pos: QPoint):
        """Show context menu for widget."""
        menu = QMenu(self)

        # Delete action
        delete_action = QAction("Delete Widget", self)
        delete_action.triggered.connect(lambda: self.delete_widget(item))
        menu.addAction(delete_action)

        # Duplicate action
        duplicate_action = QAction("Duplicate Widget", self)
        duplicate_action.triggered.connect(lambda: self.duplicate_widget(item))
        menu.addAction(duplicate_action)

        menu.addSeparator()

        # Bring to front/send to back
        front_action = QAction("Bring to Front", self)
        front_action.triggered.connect(lambda: self.bring_to_front(item))
        menu.addAction(front_action)

        back_action = QAction("Send to Back", self)
        back_action.triggered.connect(lambda: self.send_to_back(item))
        menu.addAction(back_action)

        menu.exec_(global_pos)

    def delete_widget(self, item: DesignItem):
        """Delete a widget from the canvas."""
        if item in self.design_items:
            # Remove from design items
            self.design_items.remove(item)

            # Remove from layout data
            if self.current_layout and "layout" in self.current_layout:
                content = self.current_layout["layout"].get("content", [])
                # Find and remove the widget data
                for i, widget_data in enumerate(content):
                    if widget_data.get("id") == item.item_data.get("id"):
                        content.pop(i)
                        break

            # Remove widget from UI
            item.deleteLater()

            # Clear selection if this was selected
            if self.selected_item == item:
                self.selected_item = None
                self.selection_changed.emit({})

            # Emit layout change
            self.layout_changed.emit(self.current_layout)

    def duplicate_widget(self, item: DesignItem):
        """Duplicate a widget on the canvas."""
        # Create a copy of the widget data
        import copy
        widget_data = copy.deepcopy(item.item_data)

        # Remove ID to let user set it manually
        if 'id' in widget_data:
            del widget_data['id']

        # Offset position slightly
        position = widget_data.get('position', {})
        position['x'] = position.get('x', 50) + 20
        position['y'] = position.get('y', 50) + 20

        # Create new design item
        design_item = DesignItem("widget", widget_data, self.canvas_widget)
        design_item.show()

        # Connect signals
        design_item.selected.connect(self.select_item)
        design_item.moved.connect(self.item_moved)
        design_item.resized.connect(self.item_resized)
        design_item.context_menu_requested.connect(self.show_context_menu)

        self.design_items.append(design_item)

        # Add to layout data
        if self.current_layout:
            self.current_layout["layout"]["content"].append(widget_data)
            self.layout_changed.emit(self.current_layout)

    def bring_to_front(self, item: DesignItem):
        """Bring widget to front (increase z-index)."""
        if 'position' not in item.item_data:
            item.item_data['position'] = {}

        # Find max z-index
        max_z = 10
        for design_item in self.design_items:
            z_index = design_item.item_data.get('position', {}).get('z_index', 10)
            max_z = max(max_z, z_index)

        # Set higher z-index
        item.item_data['position']['z_index'] = max_z + 1
        item.raise_()

        self.layout_changed.emit(self.current_layout)

    def send_to_back(self, item: DesignItem):
        """Send widget to back (decrease z-index)."""
        if 'position' not in item.item_data:
            item.item_data['position'] = {}

        # Find min z-index
        min_z = 10
        for design_item in self.design_items:
            z_index = design_item.item_data.get('position', {}).get('z_index', 10)
            min_z = min(min_z, z_index)

        # Set lower z-index
        item.item_data['position']['z_index'] = min_z - 1
        item.lower()

        self.layout_changed.emit(self.current_layout)

    def update_canvas_size(self):
        """Update canvas size to accommodate all widgets with padding."""
        if not self.auto_resize_enabled or not self.design_items:
            return

        # Find the bounds of all widgets
        min_x = min_y = float('inf')
        max_x = max_y = float('-inf')

        for item in self.design_items:
            if item.isVisible():
                pos = item.pos()
                size = item.size()

                min_x = min(min_x, pos.x())
                min_y = min(min_y, pos.y())
                max_x = max(max_x, pos.x() + size.width())
                max_y = max(max_y, pos.y() + size.height())

        if min_x != float('inf'):  # We have widgets
            # Add padding around content
            padding = 100
            content_width = max_x - min_x + (2 * padding)
            content_height = max_y - min_y + (2 * padding)

            # Ensure minimum size
            min_width, min_height = 800, 600
            new_width = max(content_width, min_width)
            new_height = max(content_height, min_height)

            # Update canvas size
            self.canvas_widget.resize(int(new_width), int(new_height))
            self.canvas_widget.setMinimumSize(int(new_width), int(new_height))
        
    def update_selected_item(self, property_name: str, value: Any):
        """Update a property of the selected item."""
        if self.selected_item:
            # Handle nested properties (e.g., position.x)
            if '.' in property_name:
                parts = property_name.split('.')
                if len(parts) == 2:
                    parent_key, child_key = parts
                    if parent_key not in self.selected_item.item_data:
                        self.selected_item.item_data[parent_key] = {}
                    self.selected_item.item_data[parent_key][child_key] = value

                    # Apply position changes immediately
                    if parent_key == 'position':
                        self.selected_item.apply_position_data()
                else:
                    # Handle deeper nesting if needed
                    current = self.selected_item.item_data
                    for part in parts[:-1]:
                        if part not in current:
                            current[part] = {}
                        current = current[part]
                    current[parts[-1]] = value
            else:
                self.selected_item.item_data[property_name] = value

            # Update the visual representation
            self.selected_item.setup_ui()

            # Update layout data
            self.layout_changed.emit(self.current_layout)
            
    def load_from_yaml(self, yaml_content: str):
        """Load layout from YAML content."""
        try:
            layout_data = yaml.safe_load(yaml_content)
            self.current_layout = layout_data
            self.refresh_canvas()
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to load YAML: {e}")
            
    def refresh_canvas(self):
        """Refresh the canvas with current layout data."""
        # Clear existing items
        for item in self.design_items:
            item.deleteLater()
        self.design_items.clear()
        self.selected_item = None

        # Recreate items from layout data
        if self.current_layout and "layout" in self.current_layout:
            self._create_items_from_layout(self.current_layout["layout"], QPoint(50, 50))

        # Update canvas size to fit content
        self.update_canvas_size()

    def _create_items_from_layout(self, layout_data: Dict[str, Any], position: QPoint):
        """Recursively create design items from layout data."""
        if not layout_data:
            return

        layout_type = layout_data.get("type", "column")
        content = layout_data.get("content", [])

        # For canvas layout, use absolute positioning
        if layout_type == "canvas":
            # Create items for each content element using their position data
            for item_data in content:
                if isinstance(item_data, dict) and "widget" in item_data:
                    # Ensure widget has position data
                    item_data = ensure_widget_position(item_data)

                    # Create widget item
                    widget_item = DesignItem("widget", item_data, self.canvas_widget)
                    widget_item.show()
                    widget_item.selected.connect(self.select_item)
                    widget_item.moved.connect(self.item_moved)
                    widget_item.resized.connect(self.item_resized)
                    widget_item.context_menu_requested.connect(self.show_context_menu)
                    self.design_items.append(widget_item)
        else:
            # For flow layouts, use sequential positioning
            child_position = QPoint(position.x(), position.y())

            # Create items for each content element
            for i, item_data in enumerate(content):
                if isinstance(item_data, dict):
                    if "widget" in item_data:
                        # This is a widget - create the actual widget representation
                        widget_item = DesignItem("widget", item_data, self.canvas_widget)
                        widget_item.move(child_position)
                        widget_item.show()
                        widget_item.selected.connect(self.select_item)
                        widget_item.moved.connect(self.item_moved)
                        widget_item.resized.connect(self.item_resized)
                        widget_item.context_menu_requested.connect(self.show_context_menu)
                        self.design_items.append(widget_item)

                    # Move position for next item based on layout type
                    if layout_type == "column":
                        # Stack vertically with spacing
                        spacing = layout_data.get("spacing", 15)
                        widget_height = widget_item.height() if widget_item.height() > 0 else 40
                        child_position.setY(child_position.y() + widget_height + spacing)
                    else:  # row
                        # Place horizontally with spacing
                        spacing = layout_data.get("spacing", 15)
                        widget_width = widget_item.width() if widget_item.width() > 0 else 120
                        child_position.setX(child_position.x() + widget_width + spacing)

                elif "type" in item_data:
                    # This is a nested layout - create a visual container
                    nested_layout_item = DesignItem("layout", item_data, self.canvas_widget)
                    nested_layout_item.move(child_position)
                    nested_layout_item.show()
                    nested_layout_item.selected.connect(self.select_item)
                    self.design_items.append(nested_layout_item)

                    # Recursively create items for nested layout content
                    nested_position = QPoint(child_position.x() + 20, child_position.y() + 90)
                    self._create_items_from_layout(item_data, nested_position)

                    # Move position for next item
                    if layout_type == "column":
                        child_position.setY(child_position.y() + 120)
                    else:  # row
                        child_position.setX(child_position.x() + 200)
        
    def get_layout_data(self) -> Dict[str, Any]:
        """Get the current layout data."""
        return self.current_layout or {}

    def get_selected_widgets(self) -> List[DesignItem]:
        """Get list of currently selected widgets."""
        if self.selected_item:
            return [self.selected_item]
        return self.selected_items

    def select_all_widgets(self):
        """Select all widgets on the canvas."""
        self.selected_items = self.design_items.copy()
        # Visual feedback for multi-select would go here

    def add_widget_from_data(self, widget_data: Dict[str, Any], position: tuple) -> Optional[DesignItem]:
        """Add a widget from data dictionary."""
        try:
            # Create design item
            item = DesignItem(widget_data, self.canvas_widget)

            # Set position
            x, y = position
            item.move(x, y)

            # Add to canvas
            self.design_items.append(item)
            item.show()

            # Connect signals
            item.selection_changed.connect(self.on_item_selected)
            item.position_changed.connect(self.item_moved)
            item.size_changed.connect(self.item_resized)

            # Update layout
            self.layout_changed.emit(self.get_layout_data())

            return item

        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to add widget: {e}")
            return None

    def remove_widget_by_id(self, widget_id: str):
        """Remove a widget by its ID."""
        for item in self.design_items[:]:
            if item.item_data.get('id') == widget_id:
                self.design_items.remove(item)
                item.setParent(None)
                item.deleteLater()
                break

        # Update layout
        self.layout_changed.emit(self.get_layout_data())

    def move_widget_to_position(self, widget_id: str, position: tuple):
        """Move a widget to a specific position."""
        for item in self.design_items:
            if item.item_data.get('id') == widget_id:
                x, y = position
                item.move(x, y)
                item.update_position_data()
                break

    def resize_widget_to_size(self, widget_id: str, size: tuple):
        """Resize a widget to a specific size."""
        for item in self.design_items:
            if item.item_data.get('id') == widget_id:
                width, height = size
                item.resize(width, height)
                item.update_position_data()
                break

    def set_widget_property(self, widget_id: str, property_path: str, value: Any):
        """Set a widget property by path."""
        for item in self.design_items:
            if item.item_data.get('id') == widget_id:
                # Handle nested property paths like 'position.x'
                keys = property_path.split('.')
                data = item.item_data

                # Navigate to the parent of the target property
                for key in keys[:-1]:
                    if key not in data:
                        data[key] = {}
                    data = data[key]

                # Set the final property
                data[keys[-1]] = value

                # Update the widget
                item.update_widget()
                break

    def set_interaction_mode(self, mode: str):
        """Set the interaction mode for the canvas."""
        self.interaction_mode = mode

    def copy_selected(self):
        """Copy selected widgets to clipboard."""
        # TODO: Implement copy functionality
        pass

    def paste(self):
        """Paste widgets from clipboard."""
        # TODO: Implement paste functionality
        pass

    def delete_selected(self):
        """Delete selected widgets."""
        if self.selected_items:
            for item in self.selected_items[:]:  # Copy list to avoid modification during iteration
                self.remove_item(item)
            self.selected_items.clear()
            self.selected_item = None
            self.selection_changed.emit(None)
        elif self.selected_item:
            self.remove_item(self.selected_item)
            self.selected_item = None
            self.selection_changed.emit(None)

    def remove_item(self, item: DesignItem):
        """Remove an item from the canvas."""
        if item in self.design_items:
            self.design_items.remove(item)
            item.setParent(None)
            item.deleteLater()

    def toggle_snap(self):
        """Toggle snap to grid."""
        self.snap_enabled = not self.snap_enabled

    def zoom_to_fit(self):
        """Zoom to fit all widgets."""
        # TODO: Implement zoom to fit
        pass

    def align_selected(self, alignment: str):
        """Align selected widgets."""
        if len(self.selected_items) < 2:
            return

        if alignment == 'left':
            min_x = min(item.x() for item in self.selected_items)
            for item in self.selected_items:
                item.move(min_x, item.y())
                item.update_position_data()
        elif alignment == 'center':
            center_x = sum(item.x() + item.width() // 2 for item in self.selected_items) // len(self.selected_items)
            for item in self.selected_items:
                item.move(center_x - item.width() // 2, item.y())
                item.update_position_data()
        elif alignment == 'right':
            max_x = max(item.x() + item.width() for item in self.selected_items)
            for item in self.selected_items:
                item.move(max_x - item.width(), item.y())
                item.update_position_data()

    def distribute_selected(self, direction: str):
        """Distribute selected widgets."""
        if len(self.selected_items) < 3:
            return

        if direction == 'horizontal':
            self.selected_items.sort(key=lambda item: item.x())
            first_x = self.selected_items[0].x()
            last_x = self.selected_items[-1].x() + self.selected_items[-1].width()
            total_width = sum(item.width() for item in self.selected_items)
            spacing = (last_x - first_x - total_width) // (len(self.selected_items) - 1)

            current_x = first_x
            for item in self.selected_items:
                item.move(current_x, item.y())
                item.update_position_data()
                current_x += item.width() + spacing
                item.update_from_data()
                break

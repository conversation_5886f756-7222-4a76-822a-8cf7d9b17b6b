"""
Project management for Arcanum applications.
"""

import shutil
from pathlib import Path
from typing import Optional, Dict, Any, List
from jinja2 import Template

from .config import ArcanumConfig
from ..schemas.config import ArcanumConfigSchema


class ArcanumProject:
    """
    Manages Arcanum project creation, loading, and structure.
    """
    
    def __init__(self, project_path: Path):
        """
        Initialize project manager.
        
        Args:
            project_path: Path to the project directory.
        """
        self.project_path = Path(project_path)
        self.config_manager = ArcanumConfig(self.project_path / "Arcanum.yaml")
        self._config: Optional[ArcanumConfigSchema] = None
    
    @classmethod
    def create(cls, project_path: Path, app_name: str, description: str = "") -> "ArcanumProject":
        """
        Create a new Arcanum project.
        
        Args:
            project_path: Path where to create the project.
            app_name: Name of the application.
            description: Description of the application.
            
        Returns:
            New ArcanumProject instance.
        """
        project_path = Path(project_path)
        
        # Create project directory
        project_path.mkdir(parents=True, exist_ok=True)
        
        # Create project structure
        cls._create_project_structure(project_path)
        
        # Create configuration file
        cls._create_config_file(project_path, app_name, description)
        
        # Create sample files
        cls._create_sample_files(project_path)
        
        return cls(project_path)
    
    @staticmethod
    def _create_project_structure(project_path: Path) -> None:
        """Create the standard Arcanum project directory structure."""
        directories = [
            "pages",
            "ui/widgets",
            "ui/themes", 
            "ui/layouts",
            "schemas",
            "renderers",
            "fiddle",
            "user_data",
            "plugins",
            "docs"
        ]
        
        for directory in directories:
            (project_path / directory).mkdir(parents=True, exist_ok=True)
    
    @staticmethod
    def _create_config_file(project_path: Path, app_name: str, description: str) -> None:
        """Create the Arcanum.yaml configuration file."""
        # Get template path
        template_path = Path(__file__).parent.parent / "templates" / "project_template" / "Arcanum.yaml"
        
        if template_path.exists():
            # Load template and render
            with open(template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()
                
            template = Template(template_content)
            rendered_content = template.render(
                app_name=app_name,
                description=description or f"A new Arcanum application: {app_name}"
            )
            
            # Write rendered config
            with open(project_path / "Arcanum.yaml", 'w', encoding='utf-8') as f:
                f.write(rendered_content)
        else:
            # Fallback: create basic config
            config = ArcanumConfigSchema(
                app_name=app_name,
                description=description or f"A new Arcanum application: {app_name}"
            )
            
            config_manager = ArcanumConfig(project_path / "Arcanum.yaml")
            config_manager.save(config)
    
    @staticmethod
    def _create_sample_files(project_path: Path) -> None:
        """Create sample files for the new project."""
        # Create welcome page layout
        welcome_layout = """# Welcome Page Layout
id: welcome_page
title: "Welcome to {{ app_name }}"

layout:
  type: column
  spacing: 20
  padding: 40
  
  content:
    - widget: image
      src: "/ui/assets/logo.png"
      width: 200
      height: 100
      align: center
      
    - widget: label
      text: "Welcome to your new Arcanum application!"
      style: "font-size: 24px; font-weight: bold; text-align: center;"
      
    - widget: label
      text: "Get started by editing this layout or creating new pages."
      style: "text-align: center; color: #666;"
      
    - widget: button
      label: "Get Started"
      action:
        type: link_to
        target: "pages/getting_started.yaml"
      style: "margin-top: 20px;"
"""
        
        with open(project_path / "pages" / "welcome_page.yaml", 'w', encoding='utf-8') as f:
            f.write(welcome_layout)
        
        # Create getting started page
        getting_started = """# Getting Started Page
id: getting_started
title: "Getting Started"

layout:
  type: column
  spacing: 15
  padding: 30
  
  content:
    - widget: label
      text: "Getting Started with Arcanum"
      style: "font-size: 20px; font-weight: bold;"
      
    - widget: label
      text: "Here are some things you can do:"
      
    - widget: label
      text: "• Edit layouts in the pages/ directory"
      
    - widget: label
      text: "• Create custom widgets in ui/widgets/"
      
    - widget: label
      text: "• Customize themes in your Arcanum.yaml"
      
    - widget: label
      text: "• Add logic with the visual logic graph editor"
      
    - widget: button
      label: "Back to Welcome"
      action:
        type: link_to
        target: "pages/welcome_page.yaml"
"""
        
        with open(project_path / "pages" / "getting_started.yaml", 'w', encoding='utf-8') as f:
            f.write(getting_started)
        
        # Create README
        readme_content = f"""# {project_path.name}

This is an Arcanum application project built with the modular, schema-driven, offline-first application builder.

## Quick Start

```bash
# Validate your project
arcanum validate

# Run in development mode
arcanum run

# Launch the visual GUI builder
arcanum builder

# Launch the logic graph editor
arcanum logic

# Export your application
arcanum export --target qt
```

## Development Workflow

1. **Design Layouts**: Edit YAML files in `pages/` or use the visual GUI builder
2. **Create Logic**: Use the visual logic graph editor for event handling
3. **Customize UI**: Add custom widgets in `ui/widgets/` and themes in `ui/themes/`
4. **Test & Validate**: Run `arcanum validate` to check your configuration
5. **Export**: Create standalone applications with `arcanum export`

## Project Structure

- `Arcanum.yaml` - Main configuration file with export settings
- `pages/` - Layout definitions (YAML format)
- `ui/` - UI components, themes, and assets
  - `widgets/` - Custom widget definitions
  - `themes/` - Theme configurations
  - `layouts/` - Reusable layout components
- `schemas/` - Data validation schemas
- `user_data/` - Application data and database
- `plugins/` - Local plugins and extensions
- `docs/` - Project documentation

## Runtime vs Development

Arcanum separates runtime and development concerns:

- **Runtime**: Minimal components needed for exported applications
- **Development**: Full toolset including GUI builder, logic editor, and export system

When you export your application, only the runtime components are bundled for optimal performance.

## Export Options

- **Desktop (Qt)**: Standalone executable with PyInstaller
- **Web (Static)**: HTML/CSS/JS for web deployment
- **Web (React)**: Modern React application
- **Admin Mode**: Include development tools in exports for debugging

## Admin Mode

Exported applications can include admin mode for debugging:

```bash
# Enable admin mode with default password
python admin_launcher.py

# Or set environment variable
export ARCANUM_ADMIN_TOKEN=your_token
python launcher.py
```

Default admin password: `arcanum_dev_2024`

## Commands Reference

### Development
- `arcanum init <name>` - Create new project
- `arcanum validate` - Validate project configuration
- `arcanum run` - Run application in development mode
- `arcanum builder` - Launch visual GUI builder
- `arcanum logic` - Launch logic graph editor

### Export
- `arcanum export --target qt` - Export desktop application
- `arcanum export --target html` - Export static web application
- `arcanum export-list` - List available export targets
- `arcanum export-all` - Export to all configured targets

### Plugins
- `arcanum plugin list` - List installed plugins
- `arcanum plugin install <name>` - Install plugin from registry
- `arcanum plugin create <name>` - Create new plugin template

## Learn More

- [Arcanum Documentation](https://arcanum.dev/docs)
- [Widget Reference](https://arcanum.dev/widgets)
- [Logic Graph Guide](https://arcanum.dev/logic)
- [Export Guide](https://arcanum.dev/export)
"""
        
        with open(project_path / "README.md", 'w', encoding='utf-8') as f:
            f.write(readme_content)
    
    def load(self) -> ArcanumConfigSchema:
        """
        Load the project configuration.
        
        Returns:
            Loaded configuration.
        """
        self._config = self.config_manager.load()
        return self._config
    
    def validate(self) -> Dict[str, Any]:
        """
        Validate the project structure and configuration.
        
        Returns:
            Validation results.
        """
        if not self._config:
            self.load()
            
        results = {
            "config_valid": True,
            "config_errors": [],
            "paths_exist": {},
            "missing_paths": [],
            "warnings": []
        }
        
        try:
            # Validate configuration
            self.config_manager.load()
        except Exception as e:
            results["config_valid"] = False
            results["config_errors"].append(str(e))
        
        # Validate paths
        if self._config:
            path_results = self.config_manager.validate_paths(self.project_path)
            results["paths_exist"] = path_results
            results["missing_paths"] = [name for name, exists in path_results.items() if not exists]
        
        # Check for common issues
        if not (self.project_path / "pages" / f"{self._config.start_layout}.yaml").exists():
            results["warnings"].append(f"Start layout '{self._config.start_layout}' not found")
        
        return results
    
    @property
    def config(self) -> Optional[ArcanumConfigSchema]:
        """Get the project configuration."""
        return self._config
    
    def get_pages(self) -> List[Path]:
        """Get list of all page files in the project."""
        pages_dir = self.project_path / (self._config.registry_path if self._config else "pages")
        if not pages_dir.exists():
            return []
        
        return list(pages_dir.glob("*.yaml")) + list(pages_dir.glob("*.yml"))
    
    def get_widgets(self) -> List[Path]:
        """Get list of all widget files in the project."""
        widgets_dir = self.project_path / (self._config.ui_path if self._config else "ui") / "widgets"
        if not widgets_dir.exists():
            return []
        
        return list(widgets_dir.glob("*.yaml")) + list(widgets_dir.glob("*.yml"))

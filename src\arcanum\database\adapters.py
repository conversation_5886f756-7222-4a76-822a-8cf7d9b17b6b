"""
Database adapters for SQLite and DuckDB.
"""

import sqlite3
import json
from typing import Dict, List, Any, Optional, Union, Tuple
from abc import ABC, abstractmethod
from pathlib import Path
from datetime import datetime
import logging

from .models import TableSchema, DatabaseModel, ColumnType

logger = logging.getLogger(__name__)


class DatabaseAdapter(ABC):
    """Abstract base class for database adapters."""
    
    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self.connection = None
        
    @abstractmethod
    def connect(self) -> bool:
        """Connect to the database."""
        pass
        
    @abstractmethod
    def disconnect(self):
        """Disconnect from the database."""
        pass
        
    @abstractmethod
    def execute(self, query: str, params: Tuple = None) -> Any:
        """Execute a query."""
        pass
        
    @abstractmethod
    def fetch_one(self, query: str, params: Tuple = None) -> Optional[Dict[str, Any]]:
        """Fetch one row."""
        pass
        
    @abstractmethod
    def fetch_all(self, query: str, params: Tuple = None) -> List[Dict[str, Any]]:
        """Fetch all rows."""
        pass
        
    @abstractmethod
    def create_table(self, schema: TableSchema) -> bool:
        """Create a table from schema."""
        pass
        
    @abstractmethod
    def drop_table(self, table_name: str) -> bool:
        """Drop a table."""
        pass
        
    @abstractmethod
    def table_exists(self, table_name: str) -> bool:
        """Check if table exists."""
        pass
        
    @abstractmethod
    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """Get table information."""
        pass


class SQLiteAdapter(DatabaseAdapter):
    """SQLite database adapter."""
    
    def __init__(self, database_path: Union[str, Path]):
        if isinstance(database_path, str):
            database_path = Path(database_path)
        super().__init__(str(database_path))
        self.database_path = database_path
        
    def connect(self) -> bool:
        """Connect to SQLite database."""
        try:
            # Create directory if it doesn't exist
            self.database_path.parent.mkdir(parents=True, exist_ok=True)
            
            self.connection = sqlite3.connect(str(self.database_path))
            self.connection.row_factory = sqlite3.Row  # Enable dict-like access
            
            # Enable foreign key constraints
            self.connection.execute("PRAGMA foreign_keys = ON")
            
            # Enable JSON support (SQLite 3.38+)
            try:
                self.connection.execute("SELECT json('{}')").fetchone()
                logger.info("SQLite JSON support enabled")
            except sqlite3.OperationalError:
                logger.warning("SQLite JSON support not available")
                
            logger.info(f"Connected to SQLite database: {self.database_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to SQLite database: {e}")
            return False
            
    def disconnect(self):
        """Disconnect from SQLite database."""
        if self.connection:
            self.connection.close()
            self.connection = None
            logger.info("Disconnected from SQLite database")
            
    def execute(self, query: str, params: Tuple = None) -> sqlite3.Cursor:
        """Execute a query."""
        if not self.connection:
            raise RuntimeError("Not connected to database")
            
        try:
            if params:
                cursor = self.connection.execute(query, params)
            else:
                cursor = self.connection.execute(query)
            self.connection.commit()
            return cursor
        except Exception as e:
            self.connection.rollback()
            logger.error(f"Query execution failed: {e}")
            logger.error(f"Query: {query}")
            logger.error(f"Params: {params}")
            raise
            
    def fetch_one(self, query: str, params: Tuple = None) -> Optional[Dict[str, Any]]:
        """Fetch one row."""
        cursor = self.execute(query, params)
        row = cursor.fetchone()
        return dict(row) if row else None
        
    def fetch_all(self, query: str, params: Tuple = None) -> List[Dict[str, Any]]:
        """Fetch all rows."""
        cursor = self.execute(query, params)
        rows = cursor.fetchall()
        return [dict(row) for row in rows]
        
    def create_table(self, schema: TableSchema) -> bool:
        """Create a table from schema."""
        try:
            # Validate schema
            errors = schema.validate()
            if errors:
                logger.error(f"Schema validation failed: {errors}")
                return False
                
            # Generate CREATE TABLE SQL
            create_sql = schema.to_create_sql("sqlite")
            logger.info(f"Creating table: {schema.name}")
            logger.debug(f"SQL: {create_sql}")
            
            self.execute(create_sql)
            
            # Create indexes
            for index in schema.indexes:
                index_sql = index.to_sql(schema.name, "sqlite")
                logger.debug(f"Creating index: {index_sql}")
                self.execute(index_sql)
                
            logger.info(f"Table '{schema.name}' created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create table '{schema.name}': {e}")
            return False
            
    def drop_table(self, table_name: str) -> bool:
        """Drop a table."""
        try:
            drop_sql = f"DROP TABLE IF EXISTS {table_name}"
            self.execute(drop_sql)
            logger.info(f"Table '{table_name}' dropped")
            return True
        except Exception as e:
            logger.error(f"Failed to drop table '{table_name}': {e}")
            return False
            
    def table_exists(self, table_name: str) -> bool:
        """Check if table exists."""
        query = """
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name=?
        """
        result = self.fetch_one(query, (table_name,))
        return result is not None
        
    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """Get table information."""
        if not self.table_exists(table_name):
            return {}
            
        # Get column information
        columns_query = f"PRAGMA table_info({table_name})"
        columns = self.fetch_all(columns_query)
        
        # Get index information
        indexes_query = f"PRAGMA index_list({table_name})"
        indexes = self.fetch_all(indexes_query)
        
        # Get foreign key information
        fk_query = f"PRAGMA foreign_key_list({table_name})"
        foreign_keys = self.fetch_all(fk_query)
        
        return {
            "name": table_name,
            "columns": columns,
            "indexes": indexes,
            "foreign_keys": foreign_keys
        }
        
    def insert(self, table_name: str, data: Dict[str, Any]) -> int:
        """Insert a record and return the ID."""
        if not data:
            raise ValueError("No data provided for insert")
            
        # Convert JSON data to strings
        processed_data = {}
        for key, value in data.items():
            if isinstance(value, (dict, list)):
                processed_data[key] = json.dumps(value)
            elif isinstance(value, datetime):
                processed_data[key] = value.isoformat()
            else:
                processed_data[key] = value
                
        columns = list(processed_data.keys())
        placeholders = ["?" for _ in columns]
        values = list(processed_data.values())
        
        query = f"""
        INSERT INTO {table_name} ({', '.join(columns)})
        VALUES ({', '.join(placeholders)})
        """
        
        cursor = self.execute(query, tuple(values))
        return cursor.lastrowid
        
    def update(self, table_name: str, data: Dict[str, Any], 
              where_clause: str, where_params: Tuple = None) -> int:
        """Update records and return the number of affected rows."""
        if not data:
            raise ValueError("No data provided for update")
            
        # Convert JSON data to strings
        processed_data = {}
        for key, value in data.items():
            if isinstance(value, (dict, list)):
                processed_data[key] = json.dumps(value)
            elif isinstance(value, datetime):
                processed_data[key] = value.isoformat()
            else:
                processed_data[key] = value
                
        set_clauses = [f"{key} = ?" for key in processed_data.keys()]
        values = list(processed_data.values())
        
        query = f"""
        UPDATE {table_name} 
        SET {', '.join(set_clauses)}
        WHERE {where_clause}
        """
        
        if where_params:
            values.extend(where_params)
            
        cursor = self.execute(query, tuple(values))
        return cursor.rowcount
        
    def delete(self, table_name: str, where_clause: str, where_params: Tuple = None) -> int:
        """Delete records and return the number of affected rows."""
        query = f"DELETE FROM {table_name} WHERE {where_clause}"
        cursor = self.execute(query, where_params)
        return cursor.rowcount
        
    def begin_transaction(self):
        """Begin a transaction."""
        if self.connection:
            self.connection.execute("BEGIN")
            
    def commit_transaction(self):
        """Commit a transaction."""
        if self.connection:
            self.connection.commit()
            
    def rollback_transaction(self):
        """Rollback a transaction."""
        if self.connection:
            self.connection.rollback()


class DuckDBAdapter(DatabaseAdapter):
    """DuckDB database adapter."""
    
    def __init__(self, database_path: Union[str, Path] = ":memory:"):
        super().__init__(str(database_path))
        self.database_path = database_path
        
    def connect(self) -> bool:
        """Connect to DuckDB database."""
        try:
            import duckdb
            
            if self.database_path != ":memory:":
                # Create directory if it doesn't exist
                Path(self.database_path).parent.mkdir(parents=True, exist_ok=True)
                
            self.connection = duckdb.connect(str(self.database_path))
            logger.info(f"Connected to DuckDB database: {self.database_path}")
            return True
            
        except ImportError:
            logger.error("DuckDB not available. Install with: pip install duckdb")
            return False
        except Exception as e:
            logger.error(f"Failed to connect to DuckDB database: {e}")
            return False
            
    def disconnect(self):
        """Disconnect from DuckDB database."""
        if self.connection:
            self.connection.close()
            self.connection = None
            logger.info("Disconnected from DuckDB database")
            
    def execute(self, query: str, params: Tuple = None):
        """Execute a query."""
        if not self.connection:
            raise RuntimeError("Not connected to database")
            
        try:
            if params:
                return self.connection.execute(query, params)
            else:
                return self.connection.execute(query)
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            logger.error(f"Query: {query}")
            logger.error(f"Params: {params}")
            raise
            
    def fetch_one(self, query: str, params: Tuple = None) -> Optional[Dict[str, Any]]:
        """Fetch one row."""
        result = self.execute(query, params)
        row = result.fetchone()
        if row:
            columns = [desc[0] for desc in result.description]
            return dict(zip(columns, row))
        return None
        
    def fetch_all(self, query: str, params: Tuple = None) -> List[Dict[str, Any]]:
        """Fetch all rows."""
        result = self.execute(query, params)
        rows = result.fetchall()
        columns = [desc[0] for desc in result.description]
        return [dict(zip(columns, row)) for row in rows]
        
    def create_table(self, schema: TableSchema) -> bool:
        """Create a table from schema."""
        # DuckDB implementation would be similar to SQLite
        # but with DuckDB-specific SQL syntax
        return self._create_table_sqlite_compatible(schema)
        
    def _create_table_sqlite_compatible(self, schema: TableSchema) -> bool:
        """Create table using SQLite-compatible syntax."""
        try:
            errors = schema.validate()
            if errors:
                logger.error(f"Schema validation failed: {errors}")
                return False
                
            create_sql = schema.to_create_sql("duckdb")
            logger.info(f"Creating table: {schema.name}")
            logger.debug(f"SQL: {create_sql}")
            
            self.execute(create_sql)
            
            for index in schema.indexes:
                index_sql = index.to_sql(schema.name, "duckdb")
                logger.debug(f"Creating index: {index_sql}")
                self.execute(index_sql)
                
            logger.info(f"Table '{schema.name}' created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create table '{schema.name}': {e}")
            return False
            
    def drop_table(self, table_name: str) -> bool:
        """Drop a table."""
        try:
            drop_sql = f"DROP TABLE IF EXISTS {table_name}"
            self.execute(drop_sql)
            logger.info(f"Table '{table_name}' dropped")
            return True
        except Exception as e:
            logger.error(f"Failed to drop table '{table_name}': {e}")
            return False
            
    def table_exists(self, table_name: str) -> bool:
        """Check if table exists."""
        query = """
        SELECT table_name FROM information_schema.tables 
        WHERE table_name = ?
        """
        result = self.fetch_one(query, (table_name,))
        return result is not None
        
    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """Get table information."""
        if not self.table_exists(table_name):
            return {}
            
        # Get column information
        columns_query = f"DESCRIBE {table_name}"
        columns = self.fetch_all(columns_query)
        
        return {
            "name": table_name,
            "columns": columns,
            "indexes": [],  # DuckDB index info would need different query
            "foreign_keys": []
        }

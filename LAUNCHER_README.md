# Arcanum Launcher Instructions

## Quick Start

### GUI Builder
To launch the Arcanum GUI Builder:

**Option 1: Python Script**
```bash
python launch_builder.py
```

**Option 2: CLI Command**
```bash
python arcanum.py builder
```

**Option 3: Windows Batch File**
```bash
launch_builder.bat
```

### CLI Commands
To use Arcanum CLI commands:

```bash
python arcanum.py --help
python arcanum.py init "My Project"
python arcanum.py builder
python arcanum.py logic
```

**Windows users can also use:**
```bash
arcanum.bat --help
arcanum.bat builder
```

## Features

### Enhanced GUI Builder
- ✅ **Welcome Screen**: Professional welcome interface with project creation/opening
- ✅ **Enhanced Property Editor**: Widget-specific properties with comprehensive styling options
- ✅ **YAML Editor**: Context menu with widget templates and validation
- ✅ **Design Canvas**: Grid system with snap-to-grid functionality
- ✅ **Widget Palette**: Visual icons, color-coded categories, improved styling
- ✅ **Grid Controls**: Toolbar buttons for grid toggle and snap toggle
- ✅ **Responsive Design**: Smaller default window size (1000x700) with minimum size (800x600)

### Key Enhancements
1. **Widget-Specific Properties**: Different property panels based on widget type
2. **Visual Grid System**: Customizable grid with alignment guides
3. **Context Menus**: Right-click to insert widget templates
4. **Professional UI**: Modern styling with emoji icons and color coding
5. **Responsive Layout**: Optimized for various screen sizes

## Troubleshooting

### Import Errors
If you see import errors, make sure you're running from the project root directory and using the provided launchers.

### Window Size Issues
The application now starts with a smaller, more manageable window size (1000x700) that should fit most screens.

### Unknown Property Warnings
The "Unknown property transform" warnings are expected and don't affect functionality.

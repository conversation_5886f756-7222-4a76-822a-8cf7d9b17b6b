"""
Abstract base renderer interface for Arcanum applications.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from pathlib import Path

from ..schemas.layout import PageLayout, BaseLayout
from ..schemas.widgets import BaseWidget
from ..core.layout import LayoutEngine


class RenderContext:
    """Context information for rendering operations."""
    
    def __init__(self, theme: str = "default", debug: bool = False):
        self.theme = theme
        self.debug = debug
        self.variables: Dict[str, Any] = {}
        self.event_handlers: Dict[str, Any] = {}
        
    def set_variable(self, name: str, value: Any) -> None:
        """Set a template variable."""
        self.variables[name] = value
        
    def get_variable(self, name: str, default: Any = None) -> Any:
        """Get a template variable."""
        return self.variables.get(name, default)
        
    def add_event_handler(self, event_type: str, handler: Any) -> None:
        """Add an event handler."""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)


class BaseRenderer(ABC):
    """
    Abstract base class for all Arcanum renderers.
    
    Renderers are responsible for converting layout definitions and widgets
    into platform-specific UI components.
    """
    
    def __init__(self, layout_engine: Optional[LayoutEngine] = None):
        """
        Initialize the renderer.
        
        Args:
            layout_engine: Layout engine for processing layouts.
        """
        self.layout_engine = layout_engine or LayoutEngine()
        self.context = RenderContext()
        self._initialized = False
        
    @abstractmethod
    def initialize(self, **kwargs) -> None:
        """
        Initialize the renderer with platform-specific setup.
        
        Args:
            **kwargs: Platform-specific initialization parameters.
        """
        pass
        
    @abstractmethod
    def render_page(self, page: PageLayout) -> Any:
        """
        Render a complete page layout.
        
        Args:
            page: Page layout to render.
            
        Returns:
            Platform-specific page representation.
        """
        pass
        
    @abstractmethod
    def render_layout(self, layout: BaseLayout, parent: Any = None) -> Any:
        """
        Render a layout container.
        
        Args:
            layout: Layout to render.
            parent: Parent container (platform-specific).
            
        Returns:
            Platform-specific layout container.
        """
        pass
        
    @abstractmethod
    def render_widget(self, widget: BaseWidget, parent: Any = None) -> Any:
        """
        Render a widget.
        
        Args:
            widget: Widget to render.
            parent: Parent container (platform-specific).
            
        Returns:
            Platform-specific widget representation.
        """
        pass
        
    @abstractmethod
    def apply_style(self, element: Any, style: Union[str, Dict[str, Any]]) -> None:
        """
        Apply styling to a rendered element.
        
        Args:
            element: Platform-specific element to style.
            style: Style definition (CSS string or style dict).
        """
        pass
        
    @abstractmethod
    def show(self) -> None:
        """Display the rendered application."""
        pass
        
    @abstractmethod
    def close(self) -> None:
        """Close the rendered application."""
        pass
        
    def set_theme(self, theme: str) -> None:
        """Set the current theme."""
        self.context.theme = theme
        
    def set_debug(self, debug: bool) -> None:
        """Enable or disable debug mode."""
        self.context.debug = debug
        
    def load_and_render_page(self, page_path: Path) -> Any:
        """
        Load a page from file and render it.
        
        Args:
            page_path: Path to the page YAML file.
            
        Returns:
            Platform-specific page representation.
        """
        page = self.layout_engine.load_page(page_path)
        return self.render_page(page)
        
    def process_template_variables(self, text: str) -> str:
        """
        Process template variables in text.
        
        Args:
            text: Text containing template variables.
            
        Returns:
            Text with variables replaced.
        """
        if not text:
            return text
            
        # Simple template variable replacement
        for name, value in self.context.variables.items():
            placeholder = f"{{{{ {name} }}}}"
            text = text.replace(placeholder, str(value))
            
        return text
        
    def validate_initialization(self) -> None:
        """Ensure the renderer is properly initialized."""
        if not self._initialized:
            raise RuntimeError("Renderer must be initialized before use")


class RendererError(Exception):
    """Base exception for renderer errors."""
    pass


class RenderingError(RendererError):
    """Exception raised during rendering operations."""
    pass


class StyleError(RendererError):
    """Exception raised during style application."""
    pass

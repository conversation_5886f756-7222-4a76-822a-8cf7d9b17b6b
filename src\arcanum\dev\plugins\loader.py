"""
Plugin discovery and loading system for Arcanum.
"""

import sys
import importlib
import importlib.util
from typing import Dict, List, Optional, Any, Type
from pathlib import Path
import logging
from dataclasses import dataclass
import traceback

from .manifest import PluginManifest, PluginInfo, PluginCapability
from .interfaces import PluginInterface

logger = logging.getLogger(__name__)


@dataclass
class LoadedPlugin:
    """Represents a loaded plugin instance."""
    info: PluginInfo
    instance: PluginInterface
    capabilities: Dict[str, Any]


class PluginLoader:
    """Handles plugin discovery and loading."""
    
    def __init__(self, plugins_path: Path):
        """
        Initialize plugin loader.
        
        Args:
            plugins_path: Path to plugins directory.
        """
        self.plugins_path = plugins_path
        self.loaded_plugins: Dict[str, LoadedPlugin] = {}
        self.plugin_modules: Dict[str, Any] = {}
        
        # Ensure plugins path exists
        self.plugins_path.mkdir(parents=True, exist_ok=True)
    
    def discover_plugins(self) -> List[PluginInfo]:
        """
        Discover all plugins in the plugins directory.
        
        Returns:
            List of discovered plugin information.
        """
        plugins = []
        
        for plugin_dir in self.plugins_path.iterdir():
            if not plugin_dir.is_dir():
                continue
            
            manifest_path = plugin_dir / "plugin.yaml"
            if not manifest_path.exists():
                # Try alternative names
                alt_paths = [
                    plugin_dir / "manifest.yaml",
                    plugin_dir / "plugin.yml",
                    plugin_dir / "manifest.yml"
                ]
                manifest_path = next((p for p in alt_paths if p.exists()), None)
                
                if not manifest_path:
                    logger.warning(f"No plugin manifest found in {plugin_dir}")
                    continue
            
            try:
                manifest = PluginManifest.from_file(manifest_path)
                plugin_info = PluginInfo(
                    manifest=manifest,
                    path=plugin_dir,
                    installed=True,
                    enabled=True  # TODO: Check enabled status from config
                )
                plugins.append(plugin_info)
                
            except Exception as e:
                logger.error(f"Failed to load plugin manifest from {manifest_path}: {e}")
                # Create error plugin info
                error_info = PluginInfo(
                    manifest=PluginManifest(
                        name=plugin_dir.name,
                        version="unknown",
                        description="Failed to load",
                        author={"name": "unknown"}
                    ),
                    path=plugin_dir,
                    installed=True,
                    enabled=False,
                    error=str(e)
                )
                plugins.append(error_info)
        
        return plugins
    
    def load_plugin(self, plugin_info: PluginInfo) -> Optional[LoadedPlugin]:
        """
        Load a specific plugin.
        
        Args:
            plugin_info: Plugin information to load.
            
        Returns:
            Loaded plugin instance or None if failed.
        """
        if not plugin_info.can_load():
            logger.warning(f"Cannot load plugin {plugin_info.name}: {plugin_info.error}")
            return None
        
        if plugin_info.name in self.loaded_plugins:
            logger.info(f"Plugin {plugin_info.name} already loaded")
            return self.loaded_plugins[plugin_info.name]
        
        try:
            # Load plugin module
            plugin_module = self._load_plugin_module(plugin_info)
            if not plugin_module:
                return None
            
            # Create plugin instance
            plugin_instance = self._create_plugin_instance(plugin_info, plugin_module)
            if not plugin_instance:
                return None
            
            # Load capabilities
            capabilities = self._load_plugin_capabilities(plugin_info, plugin_module)
            
            # Create loaded plugin
            loaded_plugin = LoadedPlugin(
                info=plugin_info,
                instance=plugin_instance,
                capabilities=capabilities
            )
            
            # Store loaded plugin
            self.loaded_plugins[plugin_info.name] = loaded_plugin
            plugin_info.loaded = True
            
            logger.info(f"Successfully loaded plugin: {plugin_info.name} v{plugin_info.version}")
            return loaded_plugin
            
        except Exception as e:
            logger.error(f"Failed to load plugin {plugin_info.name}: {e}")
            logger.debug(traceback.format_exc())
            plugin_info.error = str(e)
            return None
    
    def unload_plugin(self, plugin_name: str) -> bool:
        """
        Unload a plugin.
        
        Args:
            plugin_name: Name of plugin to unload.
            
        Returns:
            True if successfully unloaded.
        """
        if plugin_name not in self.loaded_plugins:
            return False
        
        try:
            loaded_plugin = self.loaded_plugins[plugin_name]
            
            # Call plugin cleanup if available
            if hasattr(loaded_plugin.instance, 'cleanup'):
                loaded_plugin.instance.cleanup()
            
            # Remove from loaded plugins
            del self.loaded_plugins[plugin_name]
            
            # Remove module from cache if it exists
            if plugin_name in self.plugin_modules:
                del self.plugin_modules[plugin_name]
            
            # Update plugin info
            loaded_plugin.info.loaded = False
            
            logger.info(f"Successfully unloaded plugin: {plugin_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unload plugin {plugin_name}: {e}")
            return False
    
    def reload_plugin(self, plugin_name: str) -> Optional[LoadedPlugin]:
        """
        Reload a plugin.
        
        Args:
            plugin_name: Name of plugin to reload.
            
        Returns:
            Reloaded plugin instance or None if failed.
        """
        if plugin_name in self.loaded_plugins:
            plugin_info = self.loaded_plugins[plugin_name].info
            self.unload_plugin(plugin_name)
        else:
            # Find plugin info
            plugins = self.discover_plugins()
            plugin_info = next((p for p in plugins if p.name == plugin_name), None)
            if not plugin_info:
                logger.error(f"Plugin not found: {plugin_name}")
                return None
        
        return self.load_plugin(plugin_info)
    
    def get_loaded_plugin(self, plugin_name: str) -> Optional[LoadedPlugin]:
        """Get a loaded plugin by name."""
        return self.loaded_plugins.get(plugin_name)
    
    def get_loaded_plugins(self) -> List[LoadedPlugin]:
        """Get all loaded plugins."""
        return list(self.loaded_plugins.values())
    
    def get_plugins_by_capability(self, capability_type: str) -> List[LoadedPlugin]:
        """Get all loaded plugins with a specific capability type."""
        return [
            plugin for plugin in self.loaded_plugins.values()
            if plugin.info.manifest.has_capability_type(capability_type)
        ]
    
    def _load_plugin_module(self, plugin_info: PluginInfo) -> Optional[Any]:
        """Load the plugin's Python module."""
        try:
            # Check if plugin has entry point
            entry_point = plugin_info.manifest.entry_point
            if not entry_point:
                # Default to __init__.py
                entry_point = "__init__"
            
            # Construct module path
            if entry_point.endswith('.py'):
                module_path = plugin_info.path / entry_point
            else:
                module_path = plugin_info.path / f"{entry_point}.py"
                if not module_path.exists():
                    module_path = plugin_info.path / entry_point / "__init__.py"
            
            if not module_path.exists():
                raise FileNotFoundError(f"Plugin entry point not found: {module_path}")
            
            # Load module
            module_name = f"arcanum_plugin_{plugin_info.name}"
            spec = importlib.util.spec_from_file_location(module_name, module_path)
            if not spec or not spec.loader:
                raise ImportError(f"Failed to create module spec for {module_path}")
            
            module = importlib.util.module_from_spec(spec)
            
            # Add plugin path to sys.path temporarily
            plugin_path_str = str(plugin_info.path)
            if plugin_path_str not in sys.path:
                sys.path.insert(0, plugin_path_str)
            
            try:
                spec.loader.exec_module(module)
                self.plugin_modules[plugin_info.name] = module
                return module
            finally:
                # Remove plugin path from sys.path
                if plugin_path_str in sys.path:
                    sys.path.remove(plugin_path_str)
            
        except Exception as e:
            logger.error(f"Failed to load module for plugin {plugin_info.name}: {e}")
            return None
    
    def _create_plugin_instance(self, plugin_info: PluginInfo, plugin_module: Any) -> Optional[PluginInterface]:
        """Create plugin instance from module."""
        try:
            # Look for plugin class
            plugin_class = None
            
            # Try common plugin class names
            class_names = [
                'Plugin',
                f'{plugin_info.name.title()}Plugin',
                f'{plugin_info.name}Plugin',
                'ArcanumPlugin'
            ]
            
            for class_name in class_names:
                if hasattr(plugin_module, class_name):
                    plugin_class = getattr(plugin_module, class_name)
                    break
            
            if not plugin_class:
                raise AttributeError(f"No plugin class found in module. Expected one of: {class_names}")
            
            # Create instance
            plugin_instance = plugin_class()
            
            # Validate interface
            if not isinstance(plugin_instance, PluginInterface):
                logger.warning(f"Plugin {plugin_info.name} does not implement PluginInterface")
            
            return plugin_instance
            
        except Exception as e:
            logger.error(f"Failed to create plugin instance for {plugin_info.name}: {e}")
            return None
    
    def _load_plugin_capabilities(self, plugin_info: PluginInfo, plugin_module: Any) -> Dict[str, Any]:
        """Load plugin capabilities."""
        capabilities = {}
        
        for capability in plugin_info.manifest.capabilities:
            try:
                # Load capability entry point
                entry_point_parts = capability.entry_point.split('.')
                
                # Start with plugin module
                current_obj = plugin_module
                
                # Navigate to the capability object
                for part in entry_point_parts:
                    if hasattr(current_obj, part):
                        current_obj = getattr(current_obj, part)
                    else:
                        raise AttributeError(f"Capability entry point not found: {capability.entry_point}")
                
                capabilities[capability.name] = current_obj
                
            except Exception as e:
                logger.error(f"Failed to load capability {capability.name} for plugin {plugin_info.name}: {e}")
        
        return capabilities

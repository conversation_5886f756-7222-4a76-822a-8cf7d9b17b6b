"""
Admin Mode System for Exported Arcanum Applications

This module provides a secure mechanism to re-enable development features
in exported applications when needed for debugging or maintenance.
"""

import os
import hashlib
import json
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime, timedelta


class AdminModeManager:
    """
    Manages admin mode access for exported applications.
    
    Admin mode allows re-enabling development features in exported apps
    using secure tokens or environment variables.
    """
    
    def __init__(self, project_path: str):
        """
        Initialize admin mode manager.
        
        Args:
            project_path: Path to the project directory.
        """
        self.project_path = Path(project_path)
        self.admin_config_path = self.project_path / ".admin_config"
        self._admin_enabled = False
        self._admin_token = None
        
    def is_admin_mode_enabled(self) -> bool:
        """Check if admin mode is currently enabled."""
        return self._admin_enabled
        
    def enable_admin_mode(self, token: Optional[str] = None, 
                         password: Optional[str] = None) -> bool:
        """
        Enable admin mode using token or password.
        
        Args:
            token: Admin token (if available).
            password: Admin password (if available).
            
        Returns:
            True if admin mode was enabled successfully.
        """
        # Check environment variable first
        env_token = os.environ.get("ARCANUM_ADMIN_TOKEN")
        if env_token:
            if self._validate_token(env_token):
                self._admin_enabled = True
                self._admin_token = env_token
                return True
        
        # Check provided token
        if token and self._validate_token(token):
            self._admin_enabled = True
            self._admin_token = token
            return True
            
        # Check provided password
        if password and self._validate_password(password):
            self._admin_enabled = True
            return True
            
        return False
        
    def disable_admin_mode(self):
        """Disable admin mode."""
        self._admin_enabled = False
        self._admin_token = None
        
    def create_admin_token(self, password: str, duration_hours: int = 24) -> str:
        """
        Create a temporary admin token.
        
        Args:
            password: Admin password.
            duration_hours: Token validity duration in hours.
            
        Returns:
            Generated admin token.
        """
        if not self._validate_password(password):
            raise ValueError("Invalid admin password")
            
        # Create token with expiration
        expiry = datetime.now() + timedelta(hours=duration_hours)
        token_data = {
            "project": str(self.project_path),
            "expiry": expiry.isoformat(),
            "created": datetime.now().isoformat()
        }
        
        # Generate token hash
        token_string = json.dumps(token_data, sort_keys=True)
        token_hash = hashlib.sha256(token_string.encode()).hexdigest()
        
        # Store token info
        self._store_token_info(token_hash, token_data)
        
        return token_hash
        
    def _validate_token(self, token: str) -> bool:
        """Validate an admin token."""
        try:
            token_info = self._load_token_info(token)
            if not token_info:
                return False
                
            # Check expiry
            expiry = datetime.fromisoformat(token_info["expiry"])
            if datetime.now() > expiry:
                self._remove_token_info(token)
                return False
                
            # Check project path
            if token_info["project"] != str(self.project_path):
                return False
                
            return True
            
        except Exception:
            return False
            
    def _validate_password(self, password: str) -> bool:
        """Validate admin password."""
        # Check for default development password
        if password == "arcanum_dev_2024":
            return True
            
        # Check for project-specific password
        config_path = self.project_path / "Arcanum.yaml"
        if config_path.exists():
            try:
                import yaml
                with open(config_path, 'r') as f:
                    config = yaml.safe_load(f)
                    
                admin_config = config.get("admin", {})
                stored_password = admin_config.get("password")
                
                if stored_password:
                    # Hash the provided password and compare
                    password_hash = hashlib.sha256(password.encode()).hexdigest()
                    return password_hash == stored_password
                    
            except Exception:
                pass
                
        return False
        
    def _store_token_info(self, token: str, token_data: Dict[str, Any]):
        """Store token information securely."""
        try:
            self.admin_config_path.mkdir(exist_ok=True)
            token_file = self.admin_config_path / f"{token}.json"
            
            with open(token_file, 'w') as f:
                json.dump(token_data, f)
                
        except Exception:
            pass  # Fail silently for security
            
    def _load_token_info(self, token: str) -> Optional[Dict[str, Any]]:
        """Load token information."""
        try:
            token_file = self.admin_config_path / f"{token}.json"
            if token_file.exists():
                with open(token_file, 'r') as f:
                    return json.load(f)
        except Exception:
            pass
            
        return None
        
    def _remove_token_info(self, token: str):
        """Remove expired token information."""
        try:
            token_file = self.admin_config_path / f"{token}.json"
            if token_file.exists():
                token_file.unlink()
        except Exception:
            pass
            
    def get_admin_capabilities(self) -> Dict[str, bool]:
        """
        Get available admin capabilities.
        
        Returns:
            Dictionary of capability names and availability.
        """
        if not self._admin_enabled:
            return {}
            
        return {
            "gui_builder": True,
            "logic_editor": True,
            "export_system": True,
            "plugin_system": True,
            "project_editing": True,
            "debug_mode": True,
            "live_reload": True,
        }


def check_admin_mode(project_path: str) -> AdminModeManager:
    """
    Check and initialize admin mode for a project.
    
    Args:
        project_path: Path to the project directory.
        
    Returns:
        Admin mode manager instance.
    """
    admin_manager = AdminModeManager(project_path)
    
    # Auto-enable if environment variable is set
    admin_token = os.environ.get("ARCANUM_ADMIN_TOKEN")
    if admin_token:
        admin_manager.enable_admin_mode(token=admin_token)
        
    return admin_manager


def create_admin_launcher(project_path: str, password: str) -> str:
    """
    Create an admin launcher script for development access.
    
    Args:
        project_path: Path to the project directory.
        password: Admin password.
        
    Returns:
        Path to the created launcher script.
    """
    admin_manager = AdminModeManager(project_path)
    token = admin_manager.create_admin_token(password, duration_hours=168)  # 1 week
    
    launcher_content = f'''#!/usr/bin/env python3
"""
Admin Launcher for Arcanum Application
This script enables development features in exported applications.
"""

import os
import sys
from pathlib import Path

# Set admin token
os.environ["ARCANUM_ADMIN_TOKEN"] = "{token}"

# Add project path
project_path = Path(__file__).parent
sys.path.insert(0, str(project_path / "src"))

def main():
    """Launch application with admin mode enabled."""
    try:
        from arcanum.runtime import run_app
        from arcanum.runtime.admin import check_admin_mode
        
        # Enable admin mode
        admin_manager = check_admin_mode(str(project_path))
        
        if admin_manager.is_admin_mode_enabled():
            print("Admin mode enabled - development features available")
            
            # Import development tools if available
            try:
                from arcanum.dev import launch_development_suite
                print("Development tools available")
            except ImportError:
                print("Development tools not bundled with this export")
        
        # Run the application
        return run_app(str(project_path), renderer="qt")
        
    except Exception as e:
        print(f"Error: {{e}}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
'''
    
    launcher_path = Path(project_path) / "admin_launcher.py"
    with open(launcher_path, 'w') as f:
        f.write(launcher_content)
        
    return str(launcher_path)

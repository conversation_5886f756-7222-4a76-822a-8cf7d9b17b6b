"""
Database Integration for Arcanum - SQLite and DuckDB support.
"""

from .models import (
    ColumnType,
    ConstraintType,
    ColumnDefinition,
    ColumnConstraint,
    IndexDefinition,
    TableSchema,
    DatabaseModel,
    UserModel,
    FormSubmissionModel
)

from .adapters import (
    DatabaseAdapter,
    SQLiteAdapter,
    DuckDBAdapter
)

from .query import (
    <PERSON><PERSON><PERSON><PERSON>er,
    QueryResult,
    JoinType,
    OrderDirection
)

from .manager import DatabaseManager

from .migrations import (
    Migration,
    MigrationManager
)

from .forms import (
    FormField,
    FormDefinition,
    FormSubmission,
    FormDataHandler,
    SubmissionStatus
)

__all__ = [
    # Models
    "ColumnType",
    "ConstraintType",
    "ColumnDefinition",
    "ColumnConstraint",
    "IndexDefinition",
    "TableSchema",
    "DatabaseModel",
    "UserModel",
    "FormSubmissionModel",

    # Adapters
    "DatabaseAdapter",
    "SQLiteAdapter",
    "DuckDBAdapter",

    # Query
    "QueryBuilder",
    "QueryResult",
    "JoinType",
    "OrderDirection",

    # Manager
    "DatabaseManager",

    # Migrations
    "Migration",
    "MigrationManager",

    # Forms
    "FormField",
    "FormDefinition",
    "FormSubmission",
    "FormDataHandler",
    "SubmissionStatus"
]

"""
Pydantic schemas for Arcanum configuration validation.
"""

from typing import Dict, List, Optional, Any, Literal
from pydantic import BaseModel, Field, validator
from pathlib import Path


class DatabaseConfig(BaseModel):
    """Database configuration schema."""
    type: Literal["sqlite", "duckdb"] = "sqlite"
    path: str = "user_data/app.db"
    auto_migrate: bool = True


class ThemeConfig(BaseModel):
    """Theme configuration schema."""
    primary_color: str = "#3b82f6"
    secondary_color: str = "#64748b"
    background_color: str = "#ffffff"
    text_color: str = "#1e293b"
    
    @validator("primary_color", "secondary_color", "background_color", "text_color")
    def validate_color(cls, v):
        """Validate color format (hex)."""
        if not v.startswith("#") or len(v) != 7:
            raise ValueError("Color must be in hex format (#RRGGBB)")
        try:
            int(v[1:], 16)
        except ValueError:
            raise ValueError("Invalid hex color format")
        return v


class PluginConfig(BaseModel):
    """Plugin configuration schema."""
    auto_update: bool = True
    registry_url: str = "https://github.com/arcanum/plugin-registry"
    local_plugins: List[str] = Field(default_factory=list)


class DevelopmentConfig(BaseModel):
    """Development configuration schema."""
    hot_reload: bool = True
    debug_mode: bool = False
    log_level: Literal["DEBUG", "INFO", "WARNING", "ERROR"] = "INFO"


class ExportDesktopConfig(BaseModel):
    """Desktop export configuration schema."""
    console: bool = False
    debug: bool = False
    upx: bool = True


class ExportWebConfig(BaseModel):
    """Web export configuration schema."""
    include_dev_server: bool = True
    minify: bool = False


class ExportConfig(BaseModel):
    """Export configuration schema."""
    include_admin_mode: bool = False
    minimal_runtime: bool = True
    desktop: ExportDesktopConfig = Field(default_factory=ExportDesktopConfig)
    web: ExportWebConfig = Field(default_factory=ExportWebConfig)


class AdminCapabilitiesConfig(BaseModel):
    """Admin capabilities configuration schema."""
    gui_builder: bool = True
    logic_editor: bool = True
    export_system: bool = True
    plugin_system: bool = True
    project_editing: bool = True
    debug_mode: bool = True
    live_reload: bool = True


class AdminConfig(BaseModel):
    """Admin configuration schema."""
    password: Optional[str] = None
    capabilities: AdminCapabilitiesConfig = Field(default_factory=AdminCapabilitiesConfig)


class ArcanumConfigSchema(BaseModel):
    """Main Arcanum configuration schema."""
    
    # Basic app information
    app_name: str
    version: str = "1.0.0"
    description: Optional[str] = None
    
    # Application behavior
    start_layout: str = "welcome_screen"
    theme: str = "default"
    autosave: bool = True
    
    # Feature toggles
    logic_graph: bool = True
    embed_database: bool = True
    
    # Project structure paths
    registry_path: str = "pages/"
    ui_path: str = "ui/"
    schemas_path: str = "schemas/"
    plugins_path: str = "plugins/"
    user_data_path: str = "user_data/"
    
    # Export targets
    export_targets: List[Literal["qt", "html", "json", "exe"]] = Field(
        default_factory=lambda: ["qt", "html", "json"]
    )
    
    # Configuration sections
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    themes: Dict[str, ThemeConfig] = Field(default_factory=dict)
    plugins: PluginConfig = Field(default_factory=PluginConfig)
    development: DevelopmentConfig = Field(default_factory=DevelopmentConfig)
    export: ExportConfig = Field(default_factory=ExportConfig)
    admin: AdminConfig = Field(default_factory=AdminConfig)
    
    @validator("app_name")
    def validate_app_name(cls, v):
        """Validate app name format."""
        if not v or len(v.strip()) == 0:
            raise ValueError("App name cannot be empty")
        return v.strip()
    
    @validator("themes")
    def validate_themes(cls, v):
        """Ensure default themes exist."""
        default_themes = {
            "default": ThemeConfig(),
            "dark": ThemeConfig(
                primary_color="#60a5fa",
                secondary_color="#94a3b8", 
                background_color="#0f172a",
                text_color="#f1f5f9"
            ),
            "fantasy": ThemeConfig(
                primary_color="#8b5cf6",
                secondary_color="#a78bfa",
                background_color="#1e1b4b", 
                text_color="#e0e7ff"
            ),
            "professional": ThemeConfig(
                primary_color="#059669",
                secondary_color="#6b7280",
                background_color="#f9fafb",
                text_color="#111827"
            )
        }
        
        # Merge with provided themes
        for name, theme in default_themes.items():
            if name not in v:
                v[name] = theme
                
        return v
    
    class Config:
        """Pydantic configuration."""
        extra = "forbid"  # Don't allow extra fields
        validate_assignment = True  # Validate on assignment

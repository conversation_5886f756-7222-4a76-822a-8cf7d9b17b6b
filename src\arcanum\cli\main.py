"""
Main CLI entry point for Arcanum.
"""

import click
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text

import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from arcanum.core.project import ArcanumProject
from arcanum.core.config import ArcanumConfig
from arcanum.dev.plugins.cli import register_plugin_commands
from arcanum.dev.export import ExportManager, ExportTarget, ExportConfig

console = Console()


@click.group()
@click.version_option(version="1.0.1", prog_name="Arcanum")
@click.pass_context
def main(ctx):
    """
    Arcanum - Modular, Schema-Driven, Offline-First Application Builder

    Build desktop and web applications using declarative YAML configuration.
    """
    # Ensure context object exists
    ctx.ensure_object(dict)
    ctx.obj['project_path'] = Path.cwd()


# Register plugin commands
register_plugin_commands(main)


@main.command()
@click.argument('project_name')
@click.option('--description', '-d', default="", help='Project description')
@click.option('--path', '-p', default=None, help='Project path (default: current directory)')
def init(project_name: str, description: str, path: str):
    """Initialize a new Arcanum project."""
    try:
        project_path = Path(path) / project_name if path else Path.cwd() / project_name
        
        if project_path.exists() and any(project_path.iterdir()):
            console.print(f"[red]Error: Directory {project_path} already exists and is not empty[/red]")
            return
        
        console.print(f"[blue]Creating new Arcanum project: {project_name}[/blue]")
        
        # Create project
        project = ArcanumProject.create(project_path, project_name, description)
        
        console.print(f"[green]✓ Project created successfully at {project_path}[/green]")
        console.print("\n[bold]Next steps:[/bold]")
        console.print(f"  cd {project_name}")
        console.print("  arcanum validate")
        console.print("  arcanum run")
        console.print("\n[bold]Development tools:[/bold]")
        console.print("  arcanum builder    # Launch visual GUI builder")
        console.print("  arcanum logic      # Launch logic graph editor")
        console.print("  arcanum export     # Export your application")
        console.print("\n[bold]Learn more:[/bold]")
        console.print("  Check out the README.md for detailed documentation")
        
    except Exception as e:
        console.print(f"[red]Error creating project: {e}[/red]")


@main.command()
@click.option('--project-path', '-p', default='.', help='Path to project directory')
def validate(project_path: str):
    """Validate project configuration and structure."""
    try:
        project_path = Path(project_path)
        
        if not (project_path / "Arcanum.yaml").exists():
            console.print(f"[red]Error: No Arcanum.yaml found in {project_path}[/red]")
            return
        
        console.print("[blue]Validating Arcanum project...[/blue]")
        
        project = ArcanumProject(project_path)
        results = project.validate()
        
        # Display results
        if results["config_valid"]:
            console.print("[green]✓ Configuration is valid[/green]")
        else:
            console.print("[red]✗ Configuration validation failed[/red]")
            for error in results["config_errors"]:
                console.print(f"  [red]• {error}[/red]")
        
        # Check paths
        console.print("\n[bold]Project Structure:[/bold]")
        table = Table()
        table.add_column("Path", style="cyan")
        table.add_column("Status", style="green")
        
        for path_name, exists in results["paths_exist"].items():
            status = "✓ Exists" if exists else "✗ Missing"
            style = "green" if exists else "red"
            table.add_row(path_name, f"[{style}]{status}[/{style}]")
        
        console.print(table)
        
        # Show warnings
        if results["warnings"]:
            console.print("\n[bold yellow]Warnings:[/bold yellow]")
            for warning in results["warnings"]:
                console.print(f"  [yellow]• {warning}[/yellow]")
        
        # Summary
        if results["config_valid"] and not results["missing_paths"] and not results["warnings"]:
            console.print("\n[green]✓ Project validation passed![/green]")
        else:
            console.print("\n[yellow]⚠ Project has issues that should be addressed[/yellow]")
            
    except Exception as e:
        console.print(f"[red]Error validating project: {e}[/red]")


@main.command()
@click.option('--project-path', '-p', default='.', help='Path to project directory')
def info(project_path: str):
    """Display project information."""
    try:
        project_path = Path(project_path)
        
        if not (project_path / "Arcanum.yaml").exists():
            console.print(f"[red]Error: No Arcanum.yaml found in {project_path}[/red]")
            return
        
        project = ArcanumProject(project_path)
        config = project.load()
        
        # Create info panel
        info_text = Text()
        info_text.append(f"Name: {config.app_name}\n", style="bold")
        info_text.append(f"Version: {config.version}\n")
        if config.description:
            info_text.append(f"Description: {config.description}\n")
        info_text.append(f"Theme: {config.theme}\n")
        info_text.append(f"Start Layout: {config.start_layout}\n")
        info_text.append(f"Export Targets: {', '.join(config.export_targets)}\n")
        
        panel = Panel(info_text, title="Project Information", border_style="blue")
        console.print(panel)
        
        # Show pages and widgets
        pages = project.get_pages()
        widgets = project.get_widgets()
        
        if pages:
            console.print(f"\n[bold]Pages ({len(pages)}):[/bold]")
            for page in pages:
                console.print(f"  • {page.name}")
        
        if widgets:
            console.print(f"\n[bold]Widgets ({len(widgets)}):[/bold]")
            for widget in widgets:
                console.print(f"  • {widget.name}")
                
    except Exception as e:
        console.print(f"[red]Error reading project info: {e}[/red]")


@main.command()
@click.option('--project-path', '-p', default='.', help='Path to project directory')
@click.option('--target', '-t',
              type=click.Choice(['static_html', 'react_app', 'pyinstaller', 'electron', 'cordova', 'react_native']),
              required=True, help='Export target')
@click.option('--output', '-o', type=click.Path(), help='Output directory')
@click.option('--minify', is_flag=True, help='Minify output files')
@click.option('--optimize', is_flag=True, help='Optimize build')
@click.option('--clean', is_flag=True, help='Clean output directory before export')
def export(project_path, target, output, minify, optimize, clean):
    """Export Arcanum project to various formats."""
    try:
        project_path = Path(project_path)

        if not (project_path / "Arcanum.yaml").exists():
            console.print(f"[red]Error: No Arcanum.yaml found in {project_path}[/red]")
            return

        console.print(f"[blue]Exporting project from {project_path}[/blue]")
        console.print(f"Target: {target}")

        # Create export manager
        export_manager = ExportManager(project_path)

        # Set default output path
        if not output:
            output = project_path / "dist" / target
        else:
            output = Path(output)

        console.print(f"Output: {output}")

        # Validate target requirements
        target_enum = ExportTarget(target)
        missing = export_manager.validate_target(target_enum)

        if missing:
            console.print(f"[red]Missing requirements for {target}:[/red]")
            for req in missing:
                console.print(f"  - {req}")
            return

        # Create export configuration
        config = export_manager.create_export_config(
            target=target_enum,
            output_path=output,
            minify=minify,
            optimize=optimize,
            platform_config={"clean_output": clean}
        )

        # Perform export
        with console.status(f"[bold green]Exporting to {target}..."):
            result = export_manager.export_project(config)

        if result.success:
            console.print(f"[green]✓ Export completed successfully![/green]")
            console.print(f"Build time: {result.build_time:.2f}s")
            if result.file_size:
                console.print(f"Output size: {result.file_size / 1024 / 1024:.2f} MB")
            console.print(f"Output location: {result.output_path}")
        else:
            console.print(f"[red]✗ Export failed![/red]")
            for error in result.errors:
                console.print(f"  [red]Error: {error}[/red]")
            for warning in result.warnings:
                console.print(f"  [yellow]Warning: {warning}[/yellow]")

    except Exception as e:
        console.print(f"[red]Error exporting project: {e}[/red]")


@main.command()
@click.option('--project-path', '-p', default='.', help='Path to project directory')
def export_list(project_path):
    """List available export targets and their status."""
    try:
        project_path = Path(project_path)

        if not (project_path / "Arcanum.yaml").exists():
            console.print(f"[red]Error: No Arcanum.yaml found in {project_path}[/red]")
            return

        export_manager = ExportManager(project_path)
        summary = export_manager.get_export_summary()

        console.print("[bold]Available Export Targets:[/bold]\n")

        for target in summary["available_targets"]:
            status = summary["target_status"][target]

            if status["available"]:
                console.print(f"[green]✓ {target}[/green] - Ready")
            else:
                console.print(f"[red]✗ {target}[/red] - Missing: {', '.join(status['missing_requirements'])}")

        # Show export history if available
        history = summary["export_history"]
        if history["total_exports"] > 0:
            console.print(f"\n[bold]Export History:[/bold]")
            console.print(f"Total exports: {history['total_exports']}")
            console.print(f"Successful: {history['successful_exports']}")
            console.print(f"Failed: {history['failed_exports']}")

            if history["exports_by_target"]:
                console.print("\nExports by target:")
                for target, count in history["exports_by_target"].items():
                    console.print(f"  {target}: {count}")

    except Exception as e:
        console.print(f"[red]Error listing export targets: {e}[/red]")


@main.command()
@click.option('--project-path', '-p', default='.', help='Path to project directory')
@click.option('--targets', '-t', multiple=True, help='Specific targets to export (exports all available if not specified)')
@click.option('--output', '-o', default='dist', help='Base output directory')
def export_all(project_path, targets, output):
    """Export project to multiple targets."""
    try:
        project_path = Path(project_path)

        if not (project_path / "Arcanum.yaml").exists():
            console.print(f"[red]Error: No Arcanum.yaml found in {project_path}[/red]")
            return

        from ..dev.export.manager import BatchExportManager

        batch_manager = BatchExportManager(project_path)
        output_path = project_path / output

        if targets:
            # Export specific targets
            target_enums = [ExportTarget(t) for t in targets]
            results = batch_manager.export_multiple(target_enums, output_path)
        else:
            # Export all available targets
            results = batch_manager.export_all_available(output_path)

        # Show results
        console.print(f"\n[bold]Batch Export Results:[/bold]")
        successful = 0
        failed = 0

        for target, result in results.items():
            if result.success:
                console.print(f"[green]✓ {target.value}[/green] - {result.build_time:.2f}s")
                successful += 1
            else:
                console.print(f"[red]✗ {target.value}[/red] - {', '.join(result.errors)}")
                failed += 1

        console.print(f"\nSummary: {successful} successful, {failed} failed")

    except Exception as e:
        console.print(f"[red]Error in batch export: {e}[/red]")


@main.command()
@click.option('--project-path', '-p', default='.', help='Path to project directory')
@click.option('--page', default='main', help='Page to run')
@click.option('--renderer', default='qt', type=click.Choice(['qt', 'web', 'headless']), help='Renderer to use')
def run(project_path: str, page: str, renderer: str):
    """Run the Arcanum application."""
    try:
        project_path = Path(project_path)

        if not (project_path / "Arcanum.yaml").exists():
            console.print(f"[red]Error: No Arcanum.yaml found in {project_path}[/red]")
            return

        project = ArcanumProject.load(project_path)
        console.print(f"[green]Running project: {project.config.name}[/green]")

        if renderer == 'qt':
            try:
                from ..renderers.qt_renderer import QtRenderer
                qt_renderer = QtRenderer()
                qt_renderer.initialize()

                # Load and render the page
                page_path = project_path / "pages" / f"{page}.yaml"
                if page_path.exists():
                    qt_renderer.render_page(str(page_path))
                    qt_renderer.show()
                    qt_renderer.run()
                else:
                    console.print(f"[red]Page not found: {page_path}[/red]")
            except ImportError:
                console.print("[red]PyQt5 not available. Install with: pip install PyQt5[/red]")
        else:
            console.print(f"[yellow]Renderer '{renderer}' not implemented yet[/yellow]")

    except Exception as e:
        console.print(f"[red]Error running application: {e}[/red]")


@main.command()
@click.option('--project-path', '-p', default=None, help='Project path (default: current directory)')
def dev(project_path: str):
    """Launch Arcanum development tools."""
    try:
        from ..dev import ArcanumDevelopmentSuite, check_development_environment

        # Check development environment
        env_status = check_development_environment()

        console.print("[bold blue]Arcanum Development Suite[/bold blue]")
        console.print("Available development tools:\n")

        # Show tool availability
        for tool, available in env_status["tools"].items():
            status = "[green]✓[/green]" if available else "[red]✗[/red]"
            tool_name = tool.replace("_", " ").title()
            console.print(f"  {status} {tool_name}")

        # Show PyQt5 status
        pyqt_status = env_status["pyqt5"]
        if pyqt_status["available"]:
            console.print(f"  [green]✓[/green] PyQt5 (v{pyqt_status['version']})")
        else:
            console.print("  [red]✗[/red] PyQt5 (required for GUI tools)")

        console.print(f"\n[bold]Ready for development:[/bold] {'Yes' if env_status['ready_for_development'] else 'No'}")

        if not env_status["ready_for_development"]:
            console.print("\n[yellow]Install missing dependencies:[/yellow]")
            console.print("  pip install PyQt5")

        # Launch development suite if available
        if env_status["ready_for_development"]:
            console.print("\n[bold]Launching development tools...[/bold]")
            dev_suite = ArcanumDevelopmentSuite()

            # For now, just show available commands
            console.print("\n[bold]Available commands:[/bold]")
            console.print("  arcanum builder    # Launch GUI Builder")
            console.print("  arcanum logic      # Launch Logic Editor")
            console.print("  arcanum export     # Export System")

    except ImportError as e:
        console.print(f"[red]Development tools not available: {e}[/red]")
    except Exception as e:
        console.print(f"[red]Error launching development suite: {e}[/red]")


@main.command()
@click.option('--project-path', '-p', default=None, help='Project to open in builder')
def builder(project_path: str):
    """Launch the Arcanum GUI Builder."""
    try:
        from ..dev.gui_builder.builder import main as builder_main
        console.print("[green]Launching Arcanum GUI Builder...[/green]")

        if project_path:
            console.print(f"[blue]Opening project: {project_path}[/blue]")

        # Launch the GUI builder
        import sys
        exit_code = builder_main()
        sys.exit(exit_code)

    except ImportError as e:
        console.print(f"[red]GUI Builder not available: {e}[/red]")
        console.print("[yellow]Install PyQt5 to use the GUI Builder: pip install PyQt5[/yellow]")
    except Exception as e:
        console.print(f"[red]Error launching GUI Builder: {e}[/red]")


@main.command()
@click.option('--tool', '-t',
              type=click.Choice(['builder', 'logic', 'all']),
              default='all',
              help='Development tool to launch')
@click.option('--project-path', '-p', default=None, help='Project to open')
def dev(tool: str, project_path: str):
    """Launch Arcanum development tools."""
    try:
        if tool == 'all':
            console.print("[green]Launching Arcanum Development Suite...[/green]")
            console.print("[blue]Available tools:[/blue]")
            console.print("  • GUI Builder (arcanum builder)")
            console.print("  • Logic Graph Editor (arcanum logic)")
            console.print("\n[yellow]Use specific commands or choose a tool:[/yellow]")

            choice = click.prompt(
                "Select tool",
                type=click.Choice(['builder', 'logic', 'exit']),
                default='builder'
            )

            if choice == 'exit':
                return
            tool = choice

        if tool == 'builder':
            from ..dev.gui_builder.builder import main as builder_main
            console.print("[green]Launching GUI Builder...[/green]")
            if project_path:
                console.print(f"[blue]Opening project: {project_path}[/blue]")
            import sys
            sys.exit(builder_main())

        elif tool == 'logic':
            import sys
            from PyQt5.QtWidgets import QApplication
            from ..dev.logic.editor import LogicGraphEditor

            console.print("[green]Launching Logic Graph Editor...[/green]")
            app = QApplication(sys.argv)
            app.setApplicationName("Arcanum Logic Editor")

            editor = LogicGraphEditor()
            editor.setWindowTitle("Arcanum Logic Graph Editor")
            editor.resize(1200, 800)
            editor.show()

            sys.exit(app.exec_())

    except ImportError as e:
        console.print(f"[red]Development tools not available: {e}[/red]")
        console.print("[yellow]Install PyQt5 to use development tools: pip install PyQt5[/yellow]")
    except Exception as e:
        console.print(f"[red]Error launching development tools: {e}[/red]")


@main.command()
def logic():
    """Launch the Logic Graph Editor."""
    try:
        import sys
        from PyQt5.QtWidgets import QApplication
        from ..dev.logic.editor import LogicGraphEditor

        console.print("[green]Launching Logic Graph Editor...[/green]")

        app = QApplication(sys.argv)
        app.setApplicationName("Arcanum Logic Editor")

        editor = LogicGraphEditor()
        editor.setWindowTitle("Arcanum Logic Graph Editor")
        editor.resize(1200, 800)
        editor.show()

        sys.exit(app.exec_())

    except ImportError as e:
        console.print(f"[red]Logic Editor not available: {e}[/red]")
        console.print("[yellow]Install PyQt5 to use the Logic Editor: pip install PyQt5[/yellow]")
    except Exception as e:
        console.print(f"[red]Error launching Logic Editor: {e}[/red]")


if __name__ == "__main__":
    main()

"""
Page Navigator for the Arcanum GUI Builder.
Provides page management functionality including navigation, creation, and deletion.
"""

from pathlib import Path
from typing import List, Optional, Dict, Any
import yaml

try:
    from PyQt5.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
        QPushButton, QLabel, QMessageBox, QInputDialog, QMenu, QAction
    )
    from PyQt5.QtCore import Qt, pyqtSignal
    from PyQt5.QtGui import QIcon, QFont
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False


class PageNavigator(QWidget):
    """
    Page navigation widget for managing project pages.
    """
    
    # Signals
    page_selected = pyqtSignal(Path)  # Emitted when a page is selected
    page_created = pyqtSignal(Path)   # Emitted when a new page is created
    page_deleted = pyqtSignal(Path)   # Emitted when a page is deleted
    page_renamed = pyqtSignal(Path, str)  # Emitted when a page is renamed
    
    def __init__(self, parent=None):
        """Initialize the page navigator."""
        super().__init__(parent)
        if not PYQT_AVAILABLE:
            raise ImportError("PyQt5 is required for the Page Navigator")
            
        self.current_project = None
        self.current_page_path = None
        self.pages_dir = None
        
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Title
        title = QLabel("Pages")
        title.setFont(QFont("Arial", 12, QFont.Bold))
        title.setStyleSheet("color: #374151; margin-bottom: 5px;")
        layout.addWidget(title)
        
        # Page list
        self.page_list = QListWidget()
        self.page_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #d1d5db;
                border-radius: 6px;
                background-color: white;
                selection-background-color: #3b82f6;
                selection-color: white;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f3f4f6;
            }
            QListWidget::item:hover {
                background-color: #f9fafb;
            }
            QListWidget::item:selected {
                background-color: #3b82f6;
                color: white;
            }
        """)
        layout.addWidget(self.page_list)
        
        # Buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(5)
        
        self.add_page_btn = QPushButton("+ Add")
        self.add_page_btn.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
            QPushButton:disabled {
                background-color: #d1d5db;
                color: #9ca3af;
            }
        """)
        button_layout.addWidget(self.add_page_btn)
        
        self.remove_page_btn = QPushButton("- Remove")
        self.remove_page_btn.setStyleSheet("""
            QPushButton {
                background-color: #ef4444;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #dc2626;
            }
            QPushButton:disabled {
                background-color: #d1d5db;
                color: #9ca3af;
            }
        """)
        button_layout.addWidget(self.remove_page_btn)
        
        layout.addLayout(button_layout)
        
        # Initially disable buttons
        self.add_page_btn.setEnabled(False)
        self.remove_page_btn.setEnabled(False)
    
    def setup_connections(self):
        """Set up signal connections."""
        self.page_list.itemClicked.connect(self.on_page_selected)
        self.page_list.itemDoubleClicked.connect(self.on_page_double_clicked)
        self.add_page_btn.clicked.connect(self.add_new_page)
        self.remove_page_btn.clicked.connect(self.remove_selected_page)
        
        # Context menu
        self.page_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.page_list.customContextMenuRequested.connect(self.show_context_menu)
    
    def set_project(self, project, current_page_path=None):
        """Set the current project and refresh the page list."""
        self.current_project = project
        self.current_page_path = current_page_path
        
        if project:
            self.pages_dir = project.project_path / "pages"
            self.add_page_btn.setEnabled(True)
            self.refresh_page_list()
        else:
            self.pages_dir = None
            self.add_page_btn.setEnabled(False)
            self.remove_page_btn.setEnabled(False)
            self.page_list.clear()
    
    def refresh_page_list(self):
        """Refresh the list of pages."""
        self.page_list.clear()
        
        if not self.pages_dir or not self.pages_dir.exists():
            return
        
        # Get all YAML files in pages directory
        page_files = list(self.pages_dir.glob("*.yaml")) + list(self.pages_dir.glob("*.yml"))
        page_files.sort(key=lambda p: p.name)
        
        for page_file in page_files:
            item = QListWidgetItem()
            
            # Load page info to get title
            try:
                with open(page_file, 'r', encoding='utf-8') as f:
                    page_data = yaml.safe_load(f)
                
                page_id = page_data.get('id', page_file.stem)
                page_title = page_data.get('title', page_file.stem)
                
                # Special handling for welcome page
                if page_file.stem in ['welcome_page', 'welcome_screen', 'welcome']:
                    item.setText(f"🏠 {page_title}")
                    item.setToolTip("Welcome Page (Entry Point)")
                else:
                    item.setText(page_title)
                    item.setToolTip(f"Page ID: {page_id}")
                
                # Store the file path
                item.setData(Qt.UserRole, page_file)
                
                # Highlight current page
                if self.current_page_path and page_file == self.current_page_path:
                    item.setSelected(True)
                    
            except Exception as e:
                # Fallback to filename if YAML parsing fails
                item.setText(page_file.stem)
                item.setData(Qt.UserRole, page_file)
                item.setToolTip(f"Error loading page: {e}")
            
            self.page_list.addItem(item)
        
        # Update remove button state
        self.remove_page_btn.setEnabled(self.page_list.count() > 0)
    
    def on_page_selected(self, item):
        """Handle page selection."""
        page_path = item.data(Qt.UserRole)
        if page_path:
            self.current_page_path = page_path
            self.page_selected.emit(page_path)
    
    def on_page_double_clicked(self, item):
        """Handle page double-click (rename)."""
        page_path = item.data(Qt.UserRole)
        if page_path and not self.is_welcome_page(page_path):
            self.rename_page(page_path)
    
    def add_new_page(self):
        """Add a new page."""
        if not self.pages_dir:
            return
        
        # Get page name from user
        page_name, ok = QInputDialog.getText(
            self, "New Page", "Enter page name:",
            text="new_page"
        )
        
        if not ok or not page_name.strip():
            return
        
        page_name = page_name.strip()
        
        # Validate page name
        if not page_name.replace('_', '').replace('-', '').isalnum():
            QMessageBox.warning(
                self, "Invalid Name", 
                "Page name can only contain letters, numbers, hyphens, and underscores."
            )
            return
        
        # Check if page already exists
        page_path = self.pages_dir / f"{page_name}.yaml"
        if page_path.exists():
            QMessageBox.warning(
                self, "Page Exists", 
                f"A page named '{page_name}' already exists."
            )
            return
        
        # Create new page
        try:
            self.create_page(page_path, page_name)
            self.refresh_page_list()
            self.page_created.emit(page_path)
            
            # Select the new page
            for i in range(self.page_list.count()):
                item = self.page_list.item(i)
                if item.data(Qt.UserRole) == page_path:
                    self.page_list.setCurrentItem(item)
                    self.on_page_selected(item)
                    break
                    
        except Exception as e:
            QMessageBox.critical(
                self, "Error", 
                f"Failed to create page: {e}"
            )
    
    def create_page(self, page_path: Path, page_name: str):
        """Create a new page file."""
        page_content = {
            "id": page_name,
            "title": page_name.replace('_', ' ').replace('-', ' ').title(),
            "layout": {
                "type": "column",
                "spacing": 20,
                "padding": 20,
                "content": [
                    {
                        "widget": "label",
                        "id": f"{page_name}_title",
                        "label": f"Welcome to {page_name.replace('_', ' ').title()}",
                        "style": {"font-size": "18px", "font-weight": "bold"}
                    }
                ]
            }
        }
        
        with open(page_path, 'w', encoding='utf-8') as f:
            yaml.dump(page_content, f, default_flow_style=False, sort_keys=False)
    
    def remove_selected_page(self):
        """Remove the selected page."""
        current_item = self.page_list.currentItem()
        if not current_item:
            QMessageBox.information(self, "No Selection", "Please select a page to remove.")
            return
        
        page_path = current_item.data(Qt.UserRole)
        if not page_path:
            return
        
        # Prevent removal of welcome page
        if self.is_welcome_page(page_path):
            QMessageBox.warning(
                self, "Cannot Remove", 
                "The welcome page cannot be removed as it's the entry point for your application."
            )
            return
        
        # Confirm deletion
        reply = QMessageBox.question(
            self, "Confirm Deletion",
            f"Are you sure you want to delete '{page_path.stem}'?\n\nThis action cannot be undone.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                page_path.unlink()
                self.refresh_page_list()
                self.page_deleted.emit(page_path)
            except Exception as e:
                QMessageBox.critical(
                    self, "Error", 
                    f"Failed to delete page: {e}"
                )
    
    def rename_page(self, page_path: Path):
        """Rename a page."""
        if self.is_welcome_page(page_path):
            QMessageBox.information(
                self, "Cannot Rename", 
                "The welcome page cannot be renamed as it's the entry point for your application."
            )
            return
        
        current_name = page_path.stem
        new_name, ok = QInputDialog.getText(
            self, "Rename Page", "Enter new page name:",
            text=current_name
        )
        
        if not ok or not new_name.strip() or new_name.strip() == current_name:
            return
        
        new_name = new_name.strip()
        
        # Validate new name
        if not new_name.replace('_', '').replace('-', '').isalnum():
            QMessageBox.warning(
                self, "Invalid Name", 
                "Page name can only contain letters, numbers, hyphens, and underscores."
            )
            return
        
        new_path = page_path.parent / f"{new_name}.yaml"
        if new_path.exists():
            QMessageBox.warning(
                self, "Page Exists", 
                f"A page named '{new_name}' already exists."
            )
            return
        
        try:
            # Update page ID in content
            with open(page_path, 'r', encoding='utf-8') as f:
                page_data = yaml.safe_load(f)
            
            page_data['id'] = new_name
            if 'title' not in page_data or page_data['title'] == current_name.replace('_', ' ').title():
                page_data['title'] = new_name.replace('_', ' ').replace('-', ' ').title()
            
            # Write to new file
            with open(new_path, 'w', encoding='utf-8') as f:
                yaml.dump(page_data, f, default_flow_style=False, sort_keys=False)
            
            # Remove old file
            page_path.unlink()
            
            self.refresh_page_list()
            self.page_renamed.emit(new_path, new_name)
            
        except Exception as e:
            QMessageBox.critical(
                self, "Error", 
                f"Failed to rename page: {e}"
            )
    
    def is_welcome_page(self, page_path: Path) -> bool:
        """Check if a page is the welcome page."""
        return page_path.stem in ['welcome_page', 'welcome_screen', 'welcome']
    
    def show_context_menu(self, position):
        """Show context menu for page list."""
        item = self.page_list.itemAt(position)
        if not item:
            return
        
        page_path = item.data(Qt.UserRole)
        if not page_path:
            return
        
        menu = QMenu(self)
        
        # Open action
        open_action = QAction("Open", self)
        open_action.triggered.connect(lambda: self.on_page_selected(item))
        menu.addAction(open_action)
        
        menu.addSeparator()
        
        # Rename action (not for welcome page)
        if not self.is_welcome_page(page_path):
            rename_action = QAction("Rename", self)
            rename_action.triggered.connect(lambda: self.rename_page(page_path))
            menu.addAction(rename_action)
            
            # Delete action
            delete_action = QAction("Delete", self)
            delete_action.triggered.connect(self.remove_selected_page)
            menu.addAction(delete_action)
        
        menu.exec_(self.page_list.mapToGlobal(position))
    
    def set_current_page(self, page_path: Path):
        """Set the current page and update selection."""
        self.current_page_path = page_path
        
        # Update selection in list
        for i in range(self.page_list.count()):
            item = self.page_list.item(i)
            if item.data(Qt.UserRole) == page_path:
                self.page_list.setCurrentItem(item)
                break

"""
Logic Graph Executor - Executes logic graphs at runtime.
"""

import asyncio
import time
from typing import Dict, Any, List, Optional, Callable, Set
from dataclasses import dataclass, field

from .graph import LogicGraph, LogicNode, LogicConnection, ConnectionType


@dataclass
class ExecutionContext:
    """Context for graph execution."""
    variables: Dict[str, Any] = field(default_factory=dict)
    functions: Dict[str, Callable] = field(default_factory=dict)
    current_event: Dict[str, Any] = field(default_factory=dict)
    is_startup: bool = False
    execution_id: str = ""
    debug_mode: bool = False
    breakpoints: Set[str] = field(default_factory=set)


@dataclass
class ExecutionResult:
    """Result of graph execution."""
    success: bool
    outputs: Dict[str, Any] = field(default_factory=dict)
    errors: List[str] = field(default_factory=list)
    execution_time: float = 0.0
    nodes_executed: List[str] = field(default_factory=list)


class LogicGraphExecutor:
    """Executes logic graphs with support for events, conditions, and async operations."""
    
    def __init__(self):
        self.running_graphs: Dict[str, LogicGraph] = {}
        self.execution_contexts: Dict[str, ExecutionContext] = {}
        self.event_handlers: Dict[str, List[Callable]] = {}
        self.timers: Dict[str, asyncio.Task] = {}
        
    def register_graph(self, graph: LogicGraph, context: Optional[ExecutionContext] = None) -> str:
        """Register a graph for execution."""
        if context is None:
            context = ExecutionContext()
            
        context.execution_id = graph.id
        self.running_graphs[graph.id] = graph
        self.execution_contexts[graph.id] = context
        
        # Set up event handlers for event nodes
        self._setup_event_handlers(graph)
        
        # Set up timers for trigger nodes
        self._setup_timers(graph)
        
        return graph.id
        
    def unregister_graph(self, graph_id: str):
        """Unregister a graph from execution."""
        if graph_id in self.running_graphs:
            # Cancel any running timers
            if graph_id in self.timers:
                self.timers[graph_id].cancel()
                del self.timers[graph_id]
                
            # Clean up
            del self.running_graphs[graph_id]
            del self.execution_contexts[graph_id]
            
    def execute_graph(self, graph: LogicGraph, 
                     context: Optional[ExecutionContext] = None,
                     start_nodes: Optional[List[str]] = None) -> ExecutionResult:
        """Execute a graph synchronously."""
        start_time = time.time()
        
        if context is None:
            context = ExecutionContext()
            
        result = ExecutionResult(success=True)
        
        try:
            # Validate graph first
            validation = graph.validate()
            if not validation["valid"]:
                result.success = False
                result.errors = validation["errors"]
                return result
                
            # Determine starting nodes
            if start_nodes is None:
                start_nodes = self._find_start_nodes(graph)
                
            # Execute nodes
            executed_nodes = set()
            nodes_to_execute = list(start_nodes)
            
            while nodes_to_execute:
                node_id = nodes_to_execute.pop(0)
                
                if node_id in executed_nodes:
                    continue
                    
                if node_id not in graph.nodes:
                    result.errors.append(f"Node {node_id} not found in graph")
                    continue
                    
                node = graph.nodes[node_id]
                
                # Check if all input dependencies are satisfied
                if not self._are_dependencies_satisfied(graph, node_id, executed_nodes):
                    # Move to end of queue
                    nodes_to_execute.append(node_id)
                    continue
                    
                # Execute the node
                try:
                    node_inputs = self._collect_node_inputs(graph, node_id, context)
                    node_outputs = node.execute(node_inputs, context.variables)
                    
                    # Store outputs in context
                    context.variables[f"node_{node_id}_outputs"] = node_outputs
                    
                    executed_nodes.add(node_id)
                    result.nodes_executed.append(node_id)
                    
                    # Find next nodes to execute
                    next_nodes = self._find_next_nodes(graph, node_id, node_outputs)
                    nodes_to_execute.extend(next_nodes)
                    
                except Exception as e:
                    result.errors.append(f"Error executing node {node.name}: {e}")
                    result.success = False
                    
        except Exception as e:
            result.success = False
            result.errors.append(f"Graph execution error: {e}")
            
        result.execution_time = time.time() - start_time
        result.outputs = context.variables.copy()
        
        return result
        
    async def execute_graph_async(self, graph: LogicGraph, 
                                 context: Optional[ExecutionContext] = None) -> ExecutionResult:
        """Execute a graph asynchronously."""
        # For now, just wrap the sync execution
        # TODO: Implement true async execution with proper event handling
        return self.execute_graph(graph, context)
        
    def trigger_event(self, event_type: str, event_data: Dict[str, Any] = None):
        """Trigger an event across all registered graphs."""
        if event_data is None:
            event_data = {}
            
        for graph_id, graph in self.running_graphs.items():
            context = self.execution_contexts[graph_id]
            context.current_event = {
                "type": event_type,
                "data": event_data,
                "timestamp": time.time()
            }
            
            # Find event nodes that match this event type
            event_nodes = []
            for node in graph.nodes.values():
                if (hasattr(node, 'config') and 
                    node.config.get('event_type') == event_type):
                    event_nodes.append(node.id)
                    
            if event_nodes:
                # Execute the graph starting from these event nodes
                self.execute_graph(graph, context, event_nodes)
                
    def set_variable(self, graph_id: str, variable_name: str, value: Any):
        """Set a variable in a specific graph context."""
        if graph_id in self.execution_contexts:
            self.execution_contexts[graph_id].variables[variable_name] = value
            
    def get_variable(self, graph_id: str, variable_name: str) -> Any:
        """Get a variable from a specific graph context."""
        if graph_id in self.execution_contexts:
            return self.execution_contexts[graph_id].variables.get(variable_name)
        return None
        
    def register_function(self, graph_id: str, function_name: str, function: Callable):
        """Register a custom function for a graph."""
        if graph_id in self.execution_contexts:
            self.execution_contexts[graph_id].functions[function_name] = function
            
    def _find_start_nodes(self, graph: LogicGraph) -> List[str]:
        """Find nodes that should start execution (no incoming execution connections)."""
        start_nodes = []
        
        for node_id, node in graph.nodes.items():
            has_incoming_execution = False
            
            for connection in graph.connections.values():
                if (connection.target_node_id == node_id and 
                    connection.connection_type == ConnectionType.EXECUTION):
                    has_incoming_execution = True
                    break
                    
            if not has_incoming_execution:
                start_nodes.append(node_id)
                
        return start_nodes
        
    def _are_dependencies_satisfied(self, graph: LogicGraph, node_id: str, 
                                  executed_nodes: Set[str]) -> bool:
        """Check if all dependencies for a node are satisfied."""
        for connection in graph.connections.values():
            if (connection.target_node_id == node_id and 
                connection.connection_type == ConnectionType.EXECUTION):
                if connection.source_node_id not in executed_nodes:
                    return False
        return True
        
    def _collect_node_inputs(self, graph: LogicGraph, node_id: str, 
                           context: ExecutionContext) -> Dict[str, Any]:
        """Collect input values for a node from connected outputs."""
        inputs = {}
        node = graph.nodes[node_id]
        
        # Set default values for input ports
        for port in node.input_ports:
            if port.default_value is not None:
                inputs[port.name] = port.default_value
                
        # Override with connected values
        for connection in graph.connections.values():
            if connection.target_node_id == node_id:
                source_node_id = connection.source_node_id
                source_port_id = connection.source_port_id
                target_port_id = connection.target_port_id
                
                # Get the output from the source node
                source_outputs = context.variables.get(f"node_{source_node_id}_outputs", {})
                
                # Find the port names
                source_node = graph.nodes[source_node_id]
                target_node = graph.nodes[node_id]
                
                source_port = source_node.get_output_port(source_port_id)
                target_port = target_node.get_input_port(target_port_id)
                
                if source_port and target_port:
                    if source_port.name in source_outputs:
                        inputs[target_port.name] = source_outputs[source_port.name]
                        
        return inputs
        
    def _find_next_nodes(self, graph: LogicGraph, node_id: str, 
                        node_outputs: Dict[str, Any]) -> List[str]:
        """Find nodes that should execute next based on node outputs."""
        next_nodes = []
        
        for connection in graph.connections.values():
            if (connection.source_node_id == node_id and 
                connection.connection_type == ConnectionType.EXECUTION):
                
                source_port = graph.nodes[node_id].get_output_port(connection.source_port_id)
                if source_port and source_port.name in node_outputs:
                    # Check if this execution path should be taken
                    output_value = node_outputs[source_port.name]
                    if output_value:  # Truthy values trigger execution
                        next_nodes.append(connection.target_node_id)
                        
        return next_nodes
        
    def _setup_event_handlers(self, graph: LogicGraph):
        """Set up event handlers for event nodes in the graph."""
        # TODO: Implement event handler setup
        pass
        
    def _setup_timers(self, graph: LogicGraph):
        """Set up timers for trigger nodes in the graph."""
        # TODO: Implement timer setup for async execution
        pass

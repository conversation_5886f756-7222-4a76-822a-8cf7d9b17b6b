"""
Mobile export implementations.
"""

import json
import shutil
from pathlib import Path
from typing import List, Dict, Any
import time

from .base import BaseExporter, ExportResult, ExportTarget, ExportValidationError, ExportBuildError


class MobileExporter(BaseExporter):
    """Base class for mobile exporters."""
    
    def validate_requirements(self) -> List[str]:
        """Validate mobile export requirements."""
        missing = []
        
        # Check for Node.js (most mobile frameworks need it)
        if not shutil.which("node"):
            missing.append("Node.js")
        if not shutil.which("npm"):
            missing.append("npm")
        
        return missing


class CordovaExporter(MobileExporter):
    """Exporter using Apache Cordova for mobile applications."""
    
    def validate_requirements(self) -> List[str]:
        """Validate Cordova requirements."""
        missing = super().validate_requirements()
        
        # Check for Cordova CLI
        if not shutil.which("cordova"):
            missing.append("Cordova CLI")
        
        return missing
    
    def export(self, project_path: Path) -> ExportResult:
        """Export project as Cordova mobile application."""
        start_time = time.time()
        result = ExportResult(
            success=False,
            target=self.config.target,
            output_path=self.config.output_path,
            build_time=0
        )
        
        try:
            # Validate requirements
            missing = self.validate_requirements()
            if missing:
                raise ExportValidationError(
                    f"Missing requirements: {', '.join(missing)}",
                    self.config.target
                )
            
            # Prepare output directory
            output_path = self.prepare_output_directory()
            
            # Create Cordova project
            self._create_cordova_project(project_path, output_path)
            
            # Add platforms
            platforms = self.config.platform_config.get("platforms", ["android", "ios"])
            for platform in platforms:
                if not self.run_build_commands([f"cordova platform add {platform}"], output_path):
                    result.warnings.append(f"Failed to add platform: {platform}")
            
            # Build for platforms
            for platform in platforms:
                if not self.run_build_commands([f"cordova build {platform}"], output_path):
                    result.warnings.append(f"Failed to build for platform: {platform}")
            
            # Calculate build info
            build_time = time.time() - start_time
            platforms_dir = output_path / "platforms"
            file_size = self.calculate_build_size(platforms_dir) if platforms_dir.exists() else 0
            
            result.success = True
            result.build_time = build_time
            result.file_size = file_size
            result.artifacts = list(platforms_dir.rglob("*.apk")) + list(platforms_dir.rglob("*.ipa"))
            result.build_info = self.create_build_info(project_path)
            result.logs.append(f"Cordova export completed in {build_time:.2f}s")
            
        except Exception as e:
            result.errors.append(str(e))
            result.build_time = time.time() - start_time
            self.logger.error(f"Cordova export failed: {e}")
        
        return result
    
    def _create_cordova_project(self, project_path: Path, output_path: Path):
        """Create Cordova project structure."""
        
        # Create Cordova project
        project_id = f"com.arcanum.{self.config.project_name.lower().replace(' ', '')}"
        create_command = f"cordova create . {project_id} \"{self.config.project_name}\""
        
        if not self.run_build_commands([create_command], output_path):
            raise ExportBuildError("Failed to create Cordova project", self.config.target)
        
        # Customize config.xml
        self._customize_config_xml(output_path)
        
        # Create web assets
        self._create_web_assets(project_path, output_path)
    
    def _customize_config_xml(self, output_path: Path):
        """Customize Cordova config.xml."""
        config_xml = f'''<?xml version='1.0' encoding='utf-8'?>
<widget id="com.arcanum.{self.config.project_name.lower().replace(' ', '')}" 
        version="{self.config.version}" 
        xmlns="http://www.w3.org/ns/widgets" 
        xmlns:cdv="http://cordova.apache.org/ns/1.0">
    
    <name>{self.config.project_name}</name>
    <description>Mobile application built with Arcanum</description>
    <author email="<EMAIL>" href="https://arcanum.app">Arcanum Team</author>
    
    <content src="index.html" />
    <access origin="*" />
    
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
    
    <platform name="android">
        <allow-intent href="market:*" />
    </platform>
    
    <platform name="ios">
        <allow-intent href="itms:*" />
        <allow-intent href="itms-apps:*" />
    </platform>
    
</widget>'''
        
        with open(output_path / "config.xml", "w") as f:
            f.write(config_xml)
    
    def _create_web_assets(self, project_path: Path, output_path: Path):
        """Create web assets for Cordova."""
        www_dir = output_path / "www"
        www_dir.mkdir(exist_ok=True)
        
        # Create index.html
        html_content = f'''<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{self.config.project_name}</title>
    <link rel="stylesheet" href="css/index.css">
</head>
<body>
    <div class="app">
        <h1>{self.config.project_name}</h1>
        <div id="deviceready" class="blink">
            <p class="event listening">Connecting to Device</p>
            <p class="event received">Device is Ready</p>
        </div>
    </div>
    <script src="cordova.js"></script>
    <script src="js/index.js"></script>
</body>
</html>'''
        
        with open(www_dir / "index.html", "w") as f:
            f.write(html_content)
        
        # Create CSS
        css_dir = www_dir / "css"
        css_dir.mkdir(exist_ok=True)
        
        css_content = '''* {
    -webkit-tap-highlight-color: rgba(0,0,0,0);
}

body {
    -webkit-touch-callout: none;
    -webkit-text-size-adjust: none;
    -webkit-user-select: none;
    background-color:#E4E4E4;
    background-image:linear-gradient(top, #A7A7A7 0%, #E4E4E4 51%);
    font-family: system-ui, -apple-system, -apple-system-font, 'Segoe UI', 'Roboto', sans-serif;
    font-size:12px;
    height:100vh;
    margin:0px;
    padding:0px;
    width:100%;
}

.app {
    background:url(../img/logo.png) no-repeat center top;
    position:absolute;
    left:50%;
    top:50%;
    height:50px;
    width:225px;
    text-align:center;
    padding:180px 0px 0px 0px;
    margin:-115px 0px 0px -112.5px;
}

h1 {
    font-size:24px;
    font-weight:normal;
    margin:0px;
    overflow:visible;
    padding:0px;
    text-align:center;
}

.event {
    border-radius:4px;
    color:#FFFFFF;
    font-size:12px;
    margin:0px 30px;
    padding:2px 0px;
}

.event.listening {
    background-color:#333333;
    display:block;
}

.event.received {
    background-color:#4B946A;
    display:none;
}

@keyframes fade {
    from { opacity: 1.0; }
    50% { opacity: 0.4; }
    to { opacity: 1.0; }
}

.blink {
    animation:fade 3000ms infinite;
    -webkit-animation:fade 3000ms infinite;
}'''
        
        with open(css_dir / "index.css", "w") as f:
            f.write(css_content)
        
        # Create JavaScript
        js_dir = www_dir / "js"
        js_dir.mkdir(exist_ok=True)
        
        js_content = '''var app = {
    initialize: function() {
        document.addEventListener('deviceready', this.onDeviceReady.bind(this), false);
    },

    onDeviceReady: function() {
        this.receivedEvent('deviceready');
    },

    receivedEvent: function(id) {
        var parentElement = document.getElementById(id);
        var listeningElement = parentElement.querySelector('.listening');
        var receivedElement = parentElement.querySelector('.received');

        listeningElement.setAttribute('style', 'display:none;');
        receivedElement.setAttribute('style', 'display:block;');

        console.log('Received Event: ' + id);
    }
};

app.initialize();'''
        
        with open(js_dir / "index.js", "w") as f:
            f.write(js_content)


class ReactNativeExporter(MobileExporter):
    """Exporter using React Native for mobile applications."""
    
    def validate_requirements(self) -> List[str]:
        """Validate React Native requirements."""
        missing = super().validate_requirements()
        
        # Check for React Native CLI
        if not shutil.which("npx"):
            missing.append("npx (comes with npm)")
        
        return missing
    
    def export(self, project_path: Path) -> ExportResult:
        """Export project as React Native mobile application."""
        start_time = time.time()
        result = ExportResult(
            success=False,
            target=self.config.target,
            output_path=self.config.output_path,
            build_time=0
        )
        
        try:
            # Validate requirements
            missing = self.validate_requirements()
            if missing:
                raise ExportValidationError(
                    f"Missing requirements: {', '.join(missing)}",
                    self.config.target
                )
            
            # Prepare output directory
            output_path = self.prepare_output_directory()
            
            # Create React Native project
            self._create_react_native_project(project_path, output_path)
            
            # Install dependencies
            if not self.run_build_commands(["npm install"], output_path):
                raise ExportBuildError("Failed to install dependencies", self.config.target)
            
            # Build for platforms (if specified)
            platforms = self.config.platform_config.get("platforms", [])
            for platform in platforms:
                if platform == "android":
                    if not self.run_build_commands(["npx react-native run-android --variant=release"], output_path):
                        result.warnings.append("Failed to build for Android")
                elif platform == "ios":
                    if not self.run_build_commands(["npx react-native run-ios --configuration Release"], output_path):
                        result.warnings.append("Failed to build for iOS")
            
            # Calculate build info
            build_time = time.time() - start_time
            file_size = self.calculate_build_size(output_path)
            
            result.success = True
            result.build_time = build_time
            result.file_size = file_size
            result.artifacts = list(output_path.rglob("*.apk")) + list(output_path.rglob("*.ipa"))
            result.build_info = self.create_build_info(project_path)
            result.logs.append(f"React Native export completed in {build_time:.2f}s")
            
        except Exception as e:
            result.errors.append(str(e))
            result.build_time = time.time() - start_time
            self.logger.error(f"React Native export failed: {e}")
        
        return result
    
    def _create_react_native_project(self, project_path: Path, output_path: Path):
        """Create React Native project structure."""
        
        # Initialize React Native project
        project_name = self.config.project_name.replace(" ", "").replace("-", "")
        init_command = f"npx react-native init {project_name} --directory ."
        
        if not self.run_build_commands([init_command], output_path):
            raise ExportBuildError("Failed to create React Native project", self.config.target)
        
        # Customize App.js
        self._customize_app_js(output_path)
    
    def _customize_app_js(self, output_path: Path):
        """Customize React Native App.js."""
        app_js = f'''import React from 'react';
import {{
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  View,
}} from 'react-native';

const App = () => {{
  return (
    <SafeAreaView style={{flex: 1}}>
      <StatusBar barStyle="dark-content" />
      <ScrollView contentInsetAdjustmentBehavior="automatic" style={{flex: 1}}>
        <View style={styles.container}>
          <Text style={styles.title}>{self.config.project_name}</Text>
          <Text style={styles.subtitle}>Built with Arcanum</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}};

const styles = StyleSheet.create({{
  container: {{
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    padding: 20,
  }},
  title: {{
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  }},
  subtitle: {{
    fontSize: 16,
    color: '#666',
  }},
}});

export default App;'''
        
        with open(output_path / "App.js", "w") as f:
            f.write(app_js)


class FlutterExporter(MobileExporter):
    """Exporter using Flutter for mobile applications."""
    
    def validate_requirements(self) -> List[str]:
        """Validate Flutter requirements."""
        missing = []
        
        # Check for Flutter
        if not shutil.which("flutter"):
            missing.append("Flutter SDK")
        
        return missing
    
    def export(self, project_path: Path) -> ExportResult:
        """Export project as Flutter mobile application."""
        # TODO: Implement Flutter export
        raise NotImplementedError("Flutter export not yet implemented")

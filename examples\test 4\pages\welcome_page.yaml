id: welcome_page
title: Welcome to Arcanum
layout:
  type: column
  spacing: 20
  padding: 30
  content:
  - widget: label
    id: title_label
    text: Welcome to Arcanum!
    style: 'font-size: 28px; font-weight: bold; color: #2563eb; text-align: center;
      margin-bottom: 10px;'
    position:
      x: 50
      y: 50
      width: 150
      height: 30
      snap_to_grid: true
      locked: false
      resizable: true
      z_index: 10
  - widget: label
    id: subtitle_label
    text: Your visual application builder is ready to use.
    style: 'font-size: 16px; color: #64748b; text-align: center; margin-bottom: 20px;'
    position:
      x: 50
      y: 90
      width: 150
      height: 40
      snap_to_grid: true
      locked: false
      resizable: true
      z_index: 10
  - widget: text_input
    id: sample_input
    label: 'Try typing here (live preview):'
    placeholder: Type something to test the live preview...
    style: 'margin-bottom: 15px;'
    position:
      x: 50
      y: 50
      width: 200
      height: 35
      snap_to_grid: true
      locked: false
      resizable: true
      z_index: 10
  - widget: button
    id: sample_button
    label: Click me (interactive)
    style: 'background-color: #059669; color: white; padding: 10px 20px; border-radius:
      5px; margin-bottom: 15px;'
    position:
      x: 50
      y: 50
      width: 120
      height: 35
      snap_to_grid: true
      locked: false
      resizable: true
      z_index: 10
  - widget: label
    id: help_title
    text: 'Getting Started:'
    style: 'font-size: 18px; font-weight: bold; color: #374151; margin-top: 20px;
      margin-bottom: 10px;'
    position:
      x: 60
      y: 270
      width: 150
      height: 30
      snap_to_grid: true
      locked: false
      resizable: true
      z_index: 10
  - widget: label
    id: help_1
    text: "\u2022 Select widgets from the palette on the left"
    style: 'color: #4b5563; margin-bottom: 5px;'
    position:
      x: 50
      y: 50
      width: 150
      height: 25
      snap_to_grid: true
      locked: false
      resizable: true
      z_index: 10
  - widget: label
    id: help_2
    text: "\u2022 Drag them onto the canvas to add them"
    style: 'color: #4b5563; margin-bottom: 5px;'
    position:
      x: 50
      y: 50
      width: 150
      height: 25
      snap_to_grid: true
      locked: false
      resizable: true
      z_index: 10
  - widget: label
    id: help_3
    text: "\u2022 Click widgets to edit their properties on the right"
    style: 'color: #4b5563; margin-bottom: 5px;'
    position:
      x: 50
      y: 50
      width: 150
      height: 25
      snap_to_grid: true
      locked: false
      resizable: true
      z_index: 10
  - widget: label
    id: help_4
    text: "\u2022 Use the page navigator to add more pages"
    style: 'color: #4b5563; margin-bottom: 5px;'
    position:
      x: 50
      y: 50
      width: 150
      height: 25
      snap_to_grid: true
      locked: false
      resizable: true
      z_index: 10
  - widget: label
    id: help_5
    text: "\u2022 See your changes instantly in the live preview"
    style: 'color: #4b5563; margin-bottom: 20px;'
    position:
      x: 50
      y: 50
      width: 150
      height: 25
      snap_to_grid: true
      locked: false
      resizable: true
      z_index: 10
  - widget: label
    id: footer_label
    text: "Happy building! \U0001F680"
    style: 'font-size: 16px; color: #059669; text-align: center; font-weight: bold;
      margin-top: 20px;'
    position:
      x: 50
      y: 50
      width: 150
      height: 25
      snap_to_grid: true
      locked: false
      resizable: true
      z_index: 10

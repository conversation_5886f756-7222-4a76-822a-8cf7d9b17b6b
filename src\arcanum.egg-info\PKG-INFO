Metadata-Version: 2.4
Name: arcanum
Version: 1.0.1
Summary: Modular, schema-driven, offline-first application builder
Home-page: https://github.com/arcanum/arcanum
Author: Arcanum Development Team
Author-email: <EMAIL>
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Software Development :: Libraries :: Application Frameworks
Classifier: Topic :: Software Development :: User Interfaces
Requires-Python: >=3.10
Description-Content-Type: text/markdown
Requires-Dist: pydantic>=2.0.0
Requires-Dist: pyyaml>=6.0.0
Requires-Dist: click>=8.0.0
Requires-Dist: rich>=13.0.0
Requires-Dist: jinja2>=3.1.0
Requires-Dist: sqlalchemy>=2.0.0
Requires-Dist: duckdb>=0.8.0
Requires-Dist: PyQt5>=5.15.0
Requires-Dist: fastapi>=0.100.0
Requires-Dist: uvicorn>=0.23.0
Requires-Dist: black>=23.0.0
Requires-Dist: flake8>=6.0.0
Requires-Dist: mypy>=1.0.0
Requires-Dist: pytest>=7.0.0
Requires-Dist: pytest-cov>=4.0.0
Requires-Dist: pathlib2>=2.3.0
Requires-Dist: typing-extensions>=4.0.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-cov>=4.0.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: flake8>=6.0.0; extra == "dev"
Requires-Dist: mypy>=1.0.0; extra == "dev"
Requires-Dist: pre-commit>=3.0.0; extra == "dev"
Provides-Extra: gui
Requires-Dist: PyQt5>=5.15.0; extra == "gui"
Requires-Dist: PyQt6>=6.4.0; extra == "gui"
Provides-Extra: web
Requires-Dist: fastapi>=0.100.0; extra == "web"
Requires-Dist: uvicorn>=0.23.0; extra == "web"
Requires-Dist: jinja2>=3.1.0; extra == "web"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

# Arcanum Developer Portfolio

## Overview

Arcanum is a modular, schema-driven, offline-first application builder that allows developers, designers, and no-code users to construct full-featured desktop or web applications using declarative YAML configuration and optional Python logic. Arcanum supports modern rendering backends (PyQt, web, headless) and is designed for scalability, extensibility, and composability.

## 🧠 Modern Architecture & Engine Design

- **Core Language (MVP)**: Python 3.10+
- **Planned Core Rebuild**: Rust (for safety, speed, embeddability)
- **CLI Tools**: Python or Go (eventually cross-platform binaries)
- **UI Renderer Support**:
  - PyQt5 / PyQt6 (GUI)
  - HTML5 + Tailwind (Web export)
  - Future: WebAssembly (WASM) support

## 🚀 Quick Start

### Installation

```bash
pip install arcanum
```

### Initialize a New Project

```bash
arcanum init my_app
cd my_app
```

### Project Structure

```
my_app/
├── Arcanum.yaml           # Core configuration
├── pages/                 # YAML logic & layout definitions
├── ui/                    # UI widgets, themes, and layouts
│   ├── widgets/
│   ├── themes/
│   └── layouts/
├── schemas/               # Pydantic or JSON schema files
├── renderers/             # Per-platform display code (Qt, web, WASM)
├── fiddle/                # Experimental sandbox layouts
├── user_data/             # Saved input and session data
├── docs/                  # Auto-generated references and plugin index
└── plugins/               # Local plugins
```

### Basic Usage

```bash
# Validate your project
arcanum validate

# Run the GUI builder
arcanum gui

# Export to different formats
arcanum export --target web
arcanum export --target qt
arcanum export --target json
```

## 🔧 Core Features

### 1. YAML-Driven Configuration
- Declarative layout definitions
- Schema validation with Pydantic
- Live YAML synchronization

### 2. Widget System
- Extensible widget registry
- Custom widget creation wizard
- Theme support (dark, fantasy, professional)

### 3. Logic Graph System
- Node-based visual programming
- Event handling (on_submit, on_change, on_exit)
- State machine workflows

### 4. Multi-Platform Rendering
- PyQt for desktop applications
- HTML5/Tailwind for web export
- Headless mode for CLI/API usage

### 5. Plugin Architecture
- GitHub-based plugin registry
- Local plugin development
- Auto-updating plugin index

### 6. Offline-First Design
- Local SQLite/DuckDB database
- No internet required for core functionality
- Export standalone applications

## 📖 Documentation

- [Getting Started Guide](docs/getting-started.md)
- [Widget Development](docs/widgets.md)
- [Layout System](docs/layouts.md)
- [Logic Graphs](docs/logic-graphs.md)
- [Plugin Development](docs/plugins.md)
- [API Reference](docs/api.md)

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🔮 Roadmap

- [ ] Core Python implementation (v1.0)
- [ ] Rust core rebuild (v2.0)
- [ ] WebAssembly support
- [ ] AI-powered layout suggestions
- [ ] Cloud synchronization
- [ ] Mobile app support

"""
Plugin system for extending Arcanum functionality.
"""

from .manifest import (
    PluginManifest, PluginInfo, PluginDependency, PluginAuthor,
    PluginCapability, PluginPermission, PluginRegistryEntry,
    create_plugin_manifest_template
)
from .loader import <PERSON>luginLoader, LoadedPlugin
from .manager import PluginManager
from .registry import PluginRegistry, LocalPluginRegistry
from .security import PluginSecurity, PluginSandbox, SecurityViolation
from .interfaces import (
    PluginInterface, WidgetPlugin, RendererPlugin, LogicNodePlugin,
    CommandPlugin, ThemePlugin, ExportPlugin, LayoutPlugin,
    get_plugin_interface, validate_plugin_interface
)
from .cli import register_plugin_commands

__all__ = [
    # Manifest and metadata
    "PluginManifest",
    "PluginInfo",
    "PluginDependency",
    "PluginAuthor",
    "PluginCapability",
    "PluginPermission",
    "PluginRegistryEntry",
    "create_plugin_manifest_template",

    # Core plugin system
    "PluginLoader",
    "LoadedPlugin",
    "PluginManager",
    "PluginRegistry",
    "LocalPluginRegistry",

    # Security
    "PluginSecurity",
    "PluginSandbox",
    "SecurityViolation",

    # Interfaces
    "PluginInterface",
    "WidgetPlugin",
    "RendererPlugin",
    "LogicNodePlugin",
    "CommandPlugin",
    "ThemePlugin",
    "ExportPlugin",
    "LayoutPlugin",
    "get_plugin_interface",
    "validate_plugin_interface",

    # CLI
    "register_plugin_commands",
]

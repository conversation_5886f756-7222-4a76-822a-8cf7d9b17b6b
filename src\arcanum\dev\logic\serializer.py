"""
Logic Graph Serializer - Converts logic graphs to/from YAML and JSON.
"""

import yaml
import json
from typing import Dict, Any, Type, Optional
from dataclasses import asdict

from .graph import LogicGraph, LogicNode, LogicConnection, NodeType, ConnectionType
from .nodes import (
    EventNode, ActionNode, ConditionNode, VariableNode, 
    FunctionNode, TriggerNode, DataNode
)


class LogicGraphSerializer:
    """Serializes and deserializes logic graphs."""
    
    # Node type mapping
    NODE_TYPE_MAP = {
        NodeType.EVENT: EventNode,
        NodeType.ACTION: ActionNode,
        NodeType.CONDITION: ConditionNode,
        NodeType.VARIABLE: VariableNode,
        NodeType.FUNCTION: FunctionNode,
        NodeType.TRIGGER: TriggerNode,
        NodeType.DATA: DataNode,
    }
    
    @classmethod
    def to_dict(cls, graph: LogicGraph) -> Dict[str, Any]:
        """Convert a logic graph to a dictionary."""
        return {
            "id": graph.id,
            "name": graph.name,
            "description": graph.description,
            "variables": graph.variables,
            "metadata": graph.metadata,
            "nodes": {
                node_id: cls._node_to_dict(node)
                for node_id, node in graph.nodes.items()
            },
            "connections": {
                conn_id: cls._connection_to_dict(connection)
                for conn_id, connection in graph.connections.items()
            }
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> LogicGraph:
        """Create a logic graph from a dictionary."""
        graph = LogicGraph(
            name=data.get("name", "Untitled Graph"),
            description=data.get("description", "")
        )
        
        # Set basic properties
        graph.id = data.get("id", graph.id)
        graph.variables = data.get("variables", {})
        graph.metadata = data.get("metadata", {})
        
        # Load nodes
        nodes_data = data.get("nodes", {})
        for node_id, node_data in nodes_data.items():
            node = cls._node_from_dict(node_data)
            if node:
                node.id = node_id
                graph.nodes[node_id] = node
                
        # Load connections
        connections_data = data.get("connections", {})
        for conn_id, conn_data in connections_data.items():
            connection = cls._connection_from_dict(conn_data)
            if connection:
                connection.id = conn_id
                graph.connections[conn_id] = connection
                
        return graph
        
    @classmethod
    def to_yaml(cls, graph: LogicGraph) -> str:
        """Convert a logic graph to YAML string."""
        data = cls.to_dict(graph)
        return yaml.dump(data, default_flow_style=False, sort_keys=False)
        
    @classmethod
    def from_yaml(cls, yaml_str: str) -> LogicGraph:
        """Create a logic graph from YAML string."""
        data = yaml.safe_load(yaml_str)
        return cls.from_dict(data)
        
    @classmethod
    def to_json(cls, graph: LogicGraph, indent: int = 2) -> str:
        """Convert a logic graph to JSON string."""
        data = cls.to_dict(graph)
        return json.dumps(data, indent=indent)
        
    @classmethod
    def from_json(cls, json_str: str) -> LogicGraph:
        """Create a logic graph from JSON string."""
        data = json.loads(json_str)
        return cls.from_dict(data)
        
    @classmethod
    def save_to_file(cls, graph: LogicGraph, file_path: str, format: str = "yaml"):
        """Save a logic graph to a file."""
        if format.lower() == "yaml":
            content = cls.to_yaml(graph)
        elif format.lower() == "json":
            content = cls.to_json(graph)
        else:
            raise ValueError(f"Unsupported format: {format}")
            
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
            
    @classmethod
    def load_from_file(cls, file_path: str) -> LogicGraph:
        """Load a logic graph from a file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Determine format from file extension
        if file_path.lower().endswith('.json'):
            return cls.from_json(content)
        else:
            return cls.from_yaml(content)
            
    @classmethod
    def _node_to_dict(cls, node: LogicNode) -> Dict[str, Any]:
        """Convert a node to a dictionary."""
        return {
            "node_type": node.node_type.value,
            "name": node.name,
            "description": node.description,
            "x": node.x,
            "y": node.y,
            "width": node.width,
            "height": node.height,
            "config": node.config,
            "enabled": node.enabled,
            "breakpoint": node.breakpoint,
            "input_ports": [cls._port_to_dict(port) for port in node.input_ports],
            "output_ports": [cls._port_to_dict(port) for port in node.output_ports],
        }
        
    @classmethod
    def _node_from_dict(cls, data: Dict[str, Any]) -> Optional[LogicNode]:
        """Create a node from a dictionary."""
        try:
            node_type_str = data.get("node_type", "action")
            node_type = NodeType(node_type_str)
            
            # Get the appropriate node class
            node_class = cls.NODE_TYPE_MAP.get(node_type, LogicNode)
            
            # Create node with basic properties
            node = node_class()
            node.node_type = node_type
            node.name = data.get("name", "")
            node.description = data.get("description", "")
            node.x = data.get("x", 0.0)
            node.y = data.get("y", 0.0)
            node.width = data.get("width", 150.0)
            node.height = data.get("height", 100.0)
            node.config = data.get("config", {})
            node.enabled = data.get("enabled", True)
            node.breakpoint = data.get("breakpoint", False)
            
            # Load ports
            node.input_ports = [
                cls._port_from_dict(port_data) 
                for port_data in data.get("input_ports", [])
            ]
            node.output_ports = [
                cls._port_from_dict(port_data) 
                for port_data in data.get("output_ports", [])
            ]
            
            return node
            
        except Exception as e:
            print(f"Error creating node from dict: {e}")
            return None
            
    @classmethod
    def _connection_to_dict(cls, connection: LogicConnection) -> Dict[str, Any]:
        """Convert a connection to a dictionary."""
        return {
            "source_node_id": connection.source_node_id,
            "source_port_id": connection.source_port_id,
            "target_node_id": connection.target_node_id,
            "target_port_id": connection.target_port_id,
            "connection_type": connection.connection_type.value,
        }
        
    @classmethod
    def _connection_from_dict(cls, data: Dict[str, Any]) -> Optional[LogicConnection]:
        """Create a connection from a dictionary."""
        try:
            connection_type_str = data.get("connection_type", "execution")
            connection_type = ConnectionType(connection_type_str)
            
            return LogicConnection(
                source_node_id=data.get("source_node_id", ""),
                source_port_id=data.get("source_port_id", ""),
                target_node_id=data.get("target_node_id", ""),
                target_port_id=data.get("target_port_id", ""),
                connection_type=connection_type
            )
            
        except Exception as e:
            print(f"Error creating connection from dict: {e}")
            return None
            
    @classmethod
    def _port_to_dict(cls, port) -> Dict[str, Any]:
        """Convert a port to a dictionary."""
        return {
            "id": port.id,
            "name": port.name,
            "port_type": port.port_type,
            "data_type": port.data_type,
            "required": port.required,
            "default_value": port.default_value,
            "description": port.description,
        }
        
    @classmethod
    def _port_from_dict(cls, data: Dict[str, Any]):
        """Create a port from a dictionary."""
        from .graph import NodePort
        return NodePort(
            id=data.get("id", ""),
            name=data.get("name", ""),
            port_type=data.get("port_type", "input"),
            data_type=data.get("data_type", "object"),
            required=data.get("required", False),
            default_value=data.get("default_value"),
            description=data.get("description", ""),
        )
        
    @classmethod
    def export_for_runtime(cls, graph: LogicGraph) -> Dict[str, Any]:
        """Export graph in a format optimized for runtime execution."""
        # Create a simplified representation for faster execution
        runtime_data = {
            "id": graph.id,
            "name": graph.name,
            "variables": graph.variables,
            "execution_order": graph.get_execution_order(),
            "nodes": {},
            "connections": {},
        }
        
        # Simplified node data
        for node_id, node in graph.nodes.items():
            runtime_data["nodes"][node_id] = {
                "type": node.node_type.value,
                "config": node.config,
                "enabled": node.enabled,
                "inputs": {port.name: port.default_value for port in node.input_ports},
                "outputs": [port.name for port in node.output_ports],
            }
            
        # Simplified connection data
        for conn_id, connection in graph.connections.items():
            runtime_data["connections"][conn_id] = {
                "from": f"{connection.source_node_id}.{connection.source_port_id}",
                "to": f"{connection.target_node_id}.{connection.target_port_id}",
                "type": connection.connection_type.value,
            }
            
        return runtime_data
        
    @classmethod
    def create_template_graph(cls, template_type: str = "basic") -> LogicGraph:
        """Create a template logic graph."""
        graph = LogicGraph(f"{template_type.title()} Template", 
                          f"Template logic graph for {template_type} scenarios")
        
        if template_type == "basic":
            # Create a simple click -> action flow
            event_node = EventNode(event_type="click")
            event_node.x = 100
            event_node.y = 100
            
            action_node = ActionNode(action_type="show_message")
            action_node.x = 300
            action_node.y = 100
            
            graph.add_node(event_node)
            graph.add_node(action_node)
            
            # Connect them
            connection = LogicConnection(
                source_node_id=event_node.id,
                source_port_id=event_node.output_ports[0].id,
                target_node_id=action_node.id,
                target_port_id=action_node.input_ports[0].id
            )
            graph.add_connection(connection)
            
        elif template_type == "conditional":
            # Create a condition-based flow
            event_node = EventNode(event_type="click")
            condition_node = ConditionNode(condition_type="equals")
            action_true = ActionNode(action_type="show_message")
            action_false = ActionNode(action_type="show_message")
            
            # Position nodes
            event_node.x, event_node.y = 50, 100
            condition_node.x, condition_node.y = 250, 100
            action_true.x, action_true.y = 450, 50
            action_false.x, action_false.y = 450, 150
            
            # Add to graph
            for node in [event_node, condition_node, action_true, action_false]:
                graph.add_node(node)
                
        return graph

"""
Web export implementations.
"""

import json
import shutil
from pathlib import Path
from typing import List, Dict, Any
import time
from datetime import datetime

from .base import BaseExporter, ExportResult, ExportTarget, ExportValidationError, ExportBuildError


class WebExporter(BaseExporter):
    """Base class for web exporters."""
    
    def validate_requirements(self) -> List[str]:
        """Validate web export requirements."""
        missing = []
        
        # Check for Node.js if needed
        if self.requires_nodejs():
            import shutil
            if not shutil.which("node"):
                missing.append("Node.js")
            if not shutil.which("npm"):
                missing.append("npm")
        
        return missing
    
    def requires_nodejs(self) -> bool:
        """Check if this exporter requires Node.js."""
        return False


class StaticWebExporter(WebExporter):
    """Exporter for static HTML/CSS/JS websites."""
    
    def export(self, project_path: Path) -> ExportResult:
        """Export project as static web application."""
        start_time = time.time()
        result = ExportResult(
            success=False,
            target=self.config.target,
            output_path=self.config.output_path,
            build_time=0
        )
        
        try:
            # Validate requirements
            missing = self.validate_requirements()
            if missing:
                raise ExportValidationError(
                    f"Missing requirements: {', '.join(missing)}",
                    self.config.target
                )
            
            # Prepare output directory
            output_path = self.prepare_output_directory()
            
            # Load project configuration
            from ...core.project import ArcanumProject
            project = ArcanumProject(project_path)
            config = project.load()
            
            # Generate HTML structure
            self._generate_html(config, output_path)
            
            # Generate CSS styles
            self._generate_css(config, output_path)
            
            # Generate JavaScript
            self._generate_javascript(config, output_path)

            # Create runtime launcher for web
            self._create_web_runtime_launcher(project_path, output_path)

            # Copy assets
            if not self.copy_assets(project_path, output_path):
                result.warnings.append("Failed to copy some assets")
            
            # Run post-build commands
            if self.config.post_build_commands:
                if not self.run_build_commands(self.config.post_build_commands, output_path):
                    raise ExportBuildError("Post-build commands failed", self.config.target)
            
            # Calculate build info
            build_time = time.time() - start_time
            file_size = self.calculate_build_size(output_path)
            
            result.success = True
            result.build_time = build_time
            result.file_size = file_size
            result.artifacts = list(output_path.rglob("*"))
            result.build_info = self.create_build_info(project_path)
            result.logs.append(f"Static web export completed in {build_time:.2f}s")
            
        except Exception as e:
            result.errors.append(str(e))
            result.build_time = time.time() - start_time
            self.logger.error(f"Static web export failed: {e}")
        
        return result
    
    def _generate_html(self, config, output_path: Path):
        """Generate HTML files."""
        # Create main HTML file
        html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{config.app_name}</title>
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div id="app">
        <div class="loading">Loading {config.app_name}...</div>
    </div>
    <script src="scripts/main.js"></script>
</body>
</html>"""
        
        with open(output_path / "index.html", "w", encoding="utf-8") as f:
            f.write(html_content)
    
    def _generate_css(self, config, output_path: Path):
        """Generate CSS files."""
        styles_dir = output_path / "styles"
        styles_dir.mkdir(exist_ok=True)
        
        # Basic CSS
        css_content = """
/* Arcanum Static Web App Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

#app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    font-size: 1.2rem;
    color: #666;
}

/* Layout styles */
.arcanum-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.arcanum-row {
    display: flex;
    flex-wrap: wrap;
    margin: -10px;
}

.arcanum-column {
    flex: 1;
    padding: 10px;
}

.arcanum-grid {
    display: grid;
    gap: 20px;
}

/* Widget styles */
.arcanum-widget {
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 16px;
}

.arcanum-button {
    background: #007bff;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.arcanum-button:hover {
    background: #0056b3;
}

.arcanum-input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.arcanum-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}
"""
        
        with open(styles_dir / "main.css", "w", encoding="utf-8") as f:
            f.write(css_content)

    def _create_web_runtime_launcher(self, project_path: Path, output_path: Path):
        """Create a Python launcher for web development server."""
        launcher_content = '''#!/usr/bin/env python3
"""
Web Development Server for Arcanum Application
Generated by Arcanum Export System - Web Runtime
"""

import sys
import os
from pathlib import Path
import http.server
import socketserver
import webbrowser
import threading
import time

def serve_static_files(port=8000):
    """Serve static files for development."""
    project_root = Path(__file__).parent
    os.chdir(project_root)

    class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
        def end_headers(self):
            self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
            self.send_header('Pragma', 'no-cache')
            self.send_header('Expires', '0')
            super().end_headers()

    with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
        print(f"🌐 Serving web application at http://localhost:{port}")
        print("Press Ctrl+C to stop the server")

        # Auto-open browser
        def open_browser():
            time.sleep(1)
            webbrowser.open(f"http://localhost:{port}")

        threading.Thread(target=open_browser, daemon=True).start()

        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\\nServer stopped.")

def main():
    """Main entry point for web development server."""
    import argparse

    parser = argparse.ArgumentParser(description="Arcanum Web Development Server")
    parser.add_argument("--port", type=int, default=8000, help="Port to serve on")
    parser.add_argument("--no-browser", action="store_true", help="Don't auto-open browser")

    args = parser.parse_args()

    serve_static_files(args.port)

if __name__ == "__main__":
    main()
'''

        # Write web launcher
        launcher_path = output_path / "serve.py"
        with open(launcher_path, 'w', encoding='utf-8') as f:
            f.write(launcher_content)

        # Make executable on Unix systems
        import os
        if hasattr(os, 'chmod'):
            os.chmod(launcher_path, 0o755)

    def _generate_javascript(self, config, output_path: Path):
        """Generate JavaScript files."""
        scripts_dir = output_path / "scripts"
        scripts_dir.mkdir(exist_ok=True)
        
        # Basic JavaScript
        js_content = f"""
// Arcanum Static Web App
class ArcanumApp {{
    constructor() {{
        this.config = {json.dumps(config.model_dump(), indent=2)};
        this.widgets = new Map();
        this.init();
    }}
    
    init() {{
        document.addEventListener('DOMContentLoaded', () => {{
            this.render();
        }});
    }}
    
    render() {{
        const app = document.getElementById('app');
        if (!app) return;
        
        // Clear loading
        app.innerHTML = '';
        
        // Create main container
        const container = document.createElement('div');
        container.className = 'arcanum-container';
        
        // Add title
        const title = document.createElement('h1');
        title.textContent = this.config.app_name;
        title.style.textAlign = 'center';
        title.style.marginBottom = '2rem';
        container.appendChild(title);
        
        // Render layouts
        this.renderLayouts(container);
        
        app.appendChild(container);
    }}
    
    renderLayouts(container) {{
        // This would be generated based on the actual project layout
        const placeholder = document.createElement('div');
        placeholder.className = 'arcanum-widget';
        placeholder.innerHTML = `
            <h2>Welcome to ${{this.config.app_name}}</h2>
            <p>This is a static web export of your Arcanum application.</p>
            <button class="arcanum-button" onclick="alert('Hello from Arcanum!')">
                Click Me
            </button>
        `;
        container.appendChild(placeholder);
    }}
    
    // Widget creation methods would be generated here
    createWidget(type, config) {{
        switch(type) {{
            case 'button':
                return this.createButton(config);
            case 'input':
                return this.createInput(config);
            default:
                return this.createPlaceholder(type, config);
        }}
    }}
    
    createButton(config) {{
        const button = document.createElement('button');
        button.className = 'arcanum-button';
        button.textContent = config.text || 'Button';
        if (config.onClick) {{
            button.onclick = new Function(config.onClick);
        }}
        return button;
    }}
    
    createInput(config) {{
        const input = document.createElement('input');
        input.className = 'arcanum-input';
        input.type = config.type || 'text';
        input.placeholder = config.placeholder || '';
        input.value = config.value || '';
        return input;
    }}
    
    createPlaceholder(type, config) {{
        const div = document.createElement('div');
        div.className = 'arcanum-widget';
        div.innerHTML = `<p>Widget: ${{type}}</p>`;
        return div;
    }}
}}

// Initialize app
new ArcanumApp();
"""
        
        with open(scripts_dir / "main.js", "w", encoding="utf-8") as f:
            f.write(js_content)


class ReactExporter(WebExporter):
    """Exporter for React applications."""
    
    def requires_nodejs(self) -> bool:
        """React export requires Node.js."""
        return True
    
    def export(self, project_path: Path) -> ExportResult:
        """Export project as React application."""
        start_time = time.time()
        result = ExportResult(
            success=False,
            target=self.config.target,
            output_path=self.config.output_path,
            build_time=0
        )
        
        try:
            # Validate requirements
            missing = self.validate_requirements()
            if missing:
                raise ExportValidationError(
                    f"Missing requirements: {', '.join(missing)}",
                    self.config.target
                )
            
            # Prepare output directory
            output_path = self.prepare_output_directory()
            
            # Create React project structure
            self._create_react_project(project_path, output_path)
            
            # Install dependencies
            if not self.run_build_commands(["npm install"], output_path):
                raise ExportBuildError("Failed to install dependencies", self.config.target)
            
            # Build React app
            if not self.run_build_commands(["npm run build"], output_path):
                raise ExportBuildError("Failed to build React app", self.config.target)
            
            # Calculate build info
            build_time = time.time() - start_time
            build_dir = output_path / "build"
            file_size = self.calculate_build_size(build_dir) if build_dir.exists() else 0
            
            result.success = True
            result.build_time = build_time
            result.file_size = file_size
            result.artifacts = list(build_dir.rglob("*")) if build_dir.exists() else []
            result.build_info = self.create_build_info(project_path)
            result.logs.append(f"React export completed in {build_time:.2f}s")
            
        except Exception as e:
            result.errors.append(str(e))
            result.build_time = time.time() - start_time
            self.logger.error(f"React export failed: {e}")
        
        return result
    
    def _create_react_project(self, project_path: Path, output_path: Path):
        """Create React project structure."""
        # This would create a full React project
        # For now, create a minimal structure
        
        # Create package.json
        package_json = {
            "name": self.config.project_name.lower().replace(" ", "-"),
            "version": self.config.version,
            "private": True,
            "dependencies": {
                "react": "^18.2.0",
                "react-dom": "^18.2.0",
                "react-scripts": "5.0.1"
            },
            "scripts": {
                "start": "react-scripts start",
                "build": "react-scripts build",
                "test": "react-scripts test",
                "eject": "react-scripts eject"
            },
            "browserslist": {
                "production": [
                    ">0.2%",
                    "not dead",
                    "not op_mini all"
                ],
                "development": [
                    "last 1 chrome version",
                    "last 1 firefox version",
                    "last 1 safari version"
                ]
            }
        }
        
        with open(output_path / "package.json", "w") as f:
            json.dump(package_json, f, indent=2)
        
        # Create public directory
        public_dir = output_path / "public"
        public_dir.mkdir(exist_ok=True)
        
        # Create index.html
        html_content = f"""<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>{self.config.project_name}</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>"""
        
        with open(public_dir / "index.html", "w") as f:
            f.write(html_content)
        
        # Create src directory
        src_dir = output_path / "src"
        src_dir.mkdir(exist_ok=True)
        
        # Create App.js
        app_js = f"""import React from 'react';
import './App.css';

function App() {{
  return (
    <div className="App">
      <header className="App-header">
        <h1>{self.config.project_name}</h1>
        <p>Generated by Arcanum</p>
      </header>
    </div>
  );
}}

export default App;"""
        
        with open(src_dir / "App.js", "w") as f:
            f.write(app_js)
        
        # Create index.js
        index_js = """import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);"""
        
        with open(src_dir / "index.js", "w") as f:
            f.write(index_js)
        
        # Create basic CSS
        css_content = """body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.App {
  text-align: center;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}"""
        
        with open(src_dir / "App.css", "w") as f:
            f.write(css_content)
        
        with open(src_dir / "index.css", "w") as f:
            f.write("/* Global styles */")


class VueExporter(WebExporter):
    """Exporter for Vue.js applications."""
    
    def requires_nodejs(self) -> bool:
        """Vue export requires Node.js."""
        return True
    
    def export(self, project_path: Path) -> ExportResult:
        """Export project as Vue application."""
        # TODO: Implement Vue export
        raise NotImplementedError("Vue export not yet implemented")


class AngularExporter(WebExporter):
    """Exporter for Angular applications."""
    
    def requires_nodejs(self) -> bool:
        """Angular export requires Node.js."""
        return True
    
    def export(self, project_path: Path) -> ExportResult:
        """Export project as Angular application."""
        # TODO: Implement Angular export
        raise NotImplementedError("Angular export not yet implemented")

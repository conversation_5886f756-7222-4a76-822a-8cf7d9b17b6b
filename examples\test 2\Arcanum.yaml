# Arcanum Project Configuration
# This file defines the core settings for your Arcanum application

app_name: "test 2"
version: "1.0.0"
description: "lets try again"

# Application behavior
start_layout: "welcome_page"
theme: "default"
autosave: true

# Feature toggles
logic_graph: true
embed_database: true

# Project structure
registry_path: "pages/"
ui_path: "ui/"
schemas_path: "schemas/"
plugins_path: "plugins/"
user_data_path: "user_data/"

# Export targets and configuration
export_targets:
  - qt
  - html
  - json

# Export settings
export:
  # Include admin mode in exports for debugging
  include_admin_mode: false

  # Bundle only runtime components (recommended)
  minimal_runtime: true

  # Platform-specific settings
  desktop:
    console: false
    debug: false
    upx: true

  web:
    include_dev_server: true
    minify: false

# Database configuration
database:
  type: "sqlite"
  path: "user_data/app.db"
  auto_migrate: true

# Admin configuration (for exported apps)
admin:
  # Set a custom admin password hash (optional)
  # password: "your_hashed_password_here"

  # Default capabilities in admin mode
  capabilities:
    gui_builder: true
    logic_editor: true
    export_system: true
    plugin_system: true
    project_editing: true
    debug_mode: true
    live_reload: true

# Theme configuration
themes:
  default:
    primary_color: "#3b82f6"
    secondary_color: "#64748b"
    background_color: "#ffffff"
    text_color: "#1e293b"
  dark:
    primary_color: "#60a5fa"
    secondary_color: "#94a3b8"
    background_color: "#0f172a"
    text_color: "#f1f5f9"
  fantasy:
    primary_color: "#8b5cf6"
    secondary_color: "#a78bfa"
    background_color: "#1e1b4b"
    text_color: "#e0e7ff"
  professional:
    primary_color: "#059669"
    secondary_color: "#6b7280"
    background_color: "#f9fafb"
    text_color: "#111827"

# Plugin configuration
plugins:
  auto_update: true
  registry_url: "https://github.com/arcanum/plugin-registry"
  local_plugins: []

# Development settings
development:
  hot_reload: true
  debug_mode: false
  log_level: "INFO"